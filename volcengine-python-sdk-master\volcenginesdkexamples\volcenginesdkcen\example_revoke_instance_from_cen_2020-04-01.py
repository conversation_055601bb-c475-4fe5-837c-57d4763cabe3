# Example Code generated by Beijing Volcanoengine Technology.
from __future__ import print_function
import volcenginesdkcore
import volcenginesdkcen
from pprint import pprint
from volcenginesdkcore.rest import ApiException

if __name__ == '__main__':
    configuration = volcenginesdkcore.Configuration()
    configuration.ak = "Your AK"
    configuration.sk = "Your SK"
    configuration.region = "cn-beijing"
    # set default configuration
    volcenginesdkcore.Configuration.set_default(configuration)

    # use global default configuration
    api_instance = volcenginesdkcen.CENApi()
    revoke_instance_from_cen_request = volcenginesdkcen.RevokeInstanceFromCenRequest(
        cen_id="cen-7qthudw0ll6jmc****",
        cen_owner_id="210000****",
        instance_id="vpc-uf6o8d1dj8sjwxi6o****",
        instance_region_id="cn-beijing",
        instance_type="VPC",
    )
    
    try:
        resp = api_instance.revoke_instance_from_cen(revoke_instance_from_cen_request)
        pprint(resp)
    except ApiException as e:
        print("Exception when calling api: %s\n" % e)
