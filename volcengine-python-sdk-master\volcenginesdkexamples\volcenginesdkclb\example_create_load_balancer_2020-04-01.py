# Example Code generated by Beijing Volcanoengine Technology.
from __future__ import print_function
import volcenginesdkcore
import volcenginesdkclb
from pprint import pprint
from volcenginesdkcore.rest import ApiException

if __name__ == '__main__':
    configuration = volcenginesdkcore.Configuration()
    configuration.ak = "Your AK"
    configuration.sk = "Your SK"
    configuration.region = "cn-beijing"
    # set default configuration
    volcenginesdkcore.Configuration.set_default(configuration)

    # use global default configuration
    api_instance = volcenginesdkclb.CLBApi()
    create_load_balancer_request = volcenginesdkclb.CreateLoadBalancerRequest(
        load_balancer_billing_type=2,
        load_balancer_name="clb-test",
        load_balancer_spec="small_1",
        type="private",
        vpc_id="vpc-bp1aevy8sofi8mh1****",
    )
    
    try:
        resp = api_instance.create_load_balancer(create_load_balancer_request)
        pprint(resp)
    except ApiException as e:
        print("Exception when calling api: %s\n" % e)
