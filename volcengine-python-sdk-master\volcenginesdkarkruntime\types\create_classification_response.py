from typing import List, Dict

from .completion_usage import CompletionUsage
from .._models import BaseModel

__all__ = ["CreateClassificationResponse"]


class LabelLogprobosValue(BaseModel):
    tokens: List[str]
    """The tokens of the label generated by the model."""

    token_logprobs: List[float]
    """The logprobs of the tokens generated by the model."""


class CreateClassificationResponse(BaseModel):
    label: str
    """The label generated by the model."""

    label_logprobos: Dict[str, LabelLogprobosValue]
    """The logprobos of labels generated by the model."""

    usage: CompletionUsage
    """Usage statistics for the completion request."""
