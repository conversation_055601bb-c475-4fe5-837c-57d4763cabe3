#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OpenCV手写体检测脚本

功能说明：
1. 让用户选择题型（和grounding_test.py一致）
2. 让用户选择检测方法（基础检测、高级检测、组合检测）
3. 让用户选择图片处理方式：
   - 白噪点：在框外添加80%密度的白色像素点（模拟马赛克）
   - 高斯模糊：对框外背景轻度模糊（radius=5-10），保留轮廓但弱化细节
   - 半透明蒙版：将框外区域覆盖50%透明度的黑色图层（类似手机截图标注效果）
   - 仅画框：除了画框外，其他区域不做任何处理
4. 从相应题型的 images 文件夹中读取所有图片
5. 使用OpenCV检测图片中的手写体区域
6. 在图片上绘制边界框并应用选择的处理方式
7. 将处理后的图片保存到OpenCV_result文件夹中
8. 生成包含处理结果的summary.md文档

使用方法：
1. 安装依赖：pip install opencv-python numpy
2. 运行脚本：python OpenCV_test.py
3. 选择题型
4. 选择检测方法
5. 选择图片处理方式
6. 脚本会自动处理相应题型下的所有图片
"""

import os
import cv2
import numpy as np
import datetime
import time  # 新增：用于记录处理时间
import random  # 新增：用于白噪点生成
from pypinyin import pinyin, Style

def chinese_to_pinyin(chinese_text):
    """将中文转换为拼音（无声调）"""
    pinyin_list = pinyin(chinese_text, style=Style.NORMAL)
    return ''.join([p[0] for p in pinyin_list])

def get_question_type():
    """获取用户输入的题型并转换为拼音路径（和grounding_test.py一致）"""
    question_types = {
        "1": "涂卡选择题",
        "2": "涂卡判断题", 
        "3": "连线题",
        "4": "图表题",
        "5": "翻译题",
        "6": "画图题",
        "7": "数学应用题",
        "8": "数学计算题",
        "9": "简单的四则运算",
        "10": "填空题",
        "11": "判断题",
        "12": "多选题",
        "13": "单选题"
    }

    print("请选择题型：")
    for key, value in question_types.items():
        print(f"{key}. {value}")

    while True:
        user_input = input("请输入题型编号（1-13）或直接输入中文：").strip()

        # 检查数字输入
        if user_input in question_types:
            question_type = question_types[user_input]
            break
        # 检查中文输入
        elif user_input in question_types.values():
            question_type = user_input
            break
        else:
            print("输入无效，请输入 1-13 的数字或直接输入中文题型名称")

    # 转换为拼音
    pinyin_name = chinese_to_pinyin(question_type)
    print(f"选择的题型：{question_type}")
    print(f"对应的拼音路径：{pinyin_name}")

    return question_type, pinyin_name

def get_image_processing_type():
    """获取用户选择的图片处理方式"""
    processing_types = {
        "1": "白噪点",
        "2": "高斯模糊",
        "3": "半透明蒙版",
        "4": "仅画框"
    }

    print("\n请选择图片处理方式：")
    print("1. 白噪点：在框外随机添加80%密度的白色像素点（模拟马赛克）")
    print("2. 高斯模糊：对框外背景轻度模糊（radius=5-10），保留轮廓但弱化细节")
    print("3. 半透明蒙版：将框外区域覆盖50%透明度的黑色图层（类似手机截图标注效果）")
    print("4. 仅画框：除了画框外，其他区域不做任何处理")

    while True:
        user_input = input("请输入处理方式编号（1-4）：").strip()

        if user_input in processing_types:
            processing_type = processing_types[user_input]
            print(f"选择的处理方式：{processing_type}")
            return user_input, processing_type
        else:
            print("输入无效，请输入 1-4 的数字")

def get_image_files(images_dir):
    """获取images文件夹中的所有图片文件，按文件名字典序排序"""
    image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp']
    image_files = []
    if os.path.exists(images_dir):
        for filename in os.listdir(images_dir):
            if any(filename.lower().endswith(ext) for ext in image_extensions):
                image_files.append(os.path.join(images_dir, filename))
    # 按文件名字典序排序
    return sorted(image_files)

def detect_handwriting(image_path):
    """
    使用OpenCV检测图片中的手写体区域（优化版，减少误检）
    返回检测到的边界框坐标列表
    """
    # 读取图片
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError(f"无法读取图片: {image_path}")

    # 转换为灰度图
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

    # 应用高斯模糊减少噪声
    blurred = cv2.GaussianBlur(gray, (3, 3), 0)

    # 使用更严格的自适应阈值，减少噪声
    thresh = cv2.adaptiveThreshold(blurred, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                   cv2.THRESH_BINARY_INV, 15, 4)

    # 使用更小的核进行形态学操作，避免连接过多区域
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (2, 2))
    morph = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel, iterations=1)

    # 轻微膨胀
    dilated = cv2.dilate(morph, kernel, iterations=1)

    # 查找轮廓
    contours, _ = cv2.findContours(dilated, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    # 更严格的过滤条件，专注于手写体特征
    handwriting_boxes = []
    image_area = gray.shape[0] * gray.shape[1]
    min_area = max(200, image_area * 0.0005)  # 提高最小面积阈值
    max_area = image_area * 0.15  # 降低最大面积阈值

    for contour in contours:
        area = cv2.contourArea(contour)
        if min_area < area < max_area:
            # 获取边界框
            x, y, w, h = cv2.boundingRect(contour)

            # 更严格的尺寸和比例过滤
            min_width = max(20, gray.shape[1] // 50)  # 最小宽度
            min_height = max(15, gray.shape[0] // 60)  # 最小高度
            max_width = gray.shape[1] // 3  # 最大宽度
            max_height = gray.shape[0] // 4  # 最大高度

            # 宽高比限制（手写体通常不会太扁或太长）
            aspect_ratio = w / h

            # 计算轮廓的填充率（实际面积/边界框面积）
            bbox_area = w * h
            fill_ratio = area / bbox_area if bbox_area > 0 else 0

            if (min_width <= w <= max_width and
                min_height <= h <= max_height and
                0.2 <= aspect_ratio <= 8.0 and  # 更合理的宽高比
                fill_ratio >= 0.15):  # 填充率过滤，避免空框
                handwriting_boxes.append((x, y, x + w, y + h))

    # 合并重叠的边界框
    handwriting_boxes = merge_overlapping_boxes(handwriting_boxes, overlap_threshold=0.4)

    return handwriting_boxes

def merge_overlapping_boxes(boxes, overlap_threshold=0.5):
    """合并重叠的边界框（优化版，减少过度合并）"""
    if not boxes:
        return boxes

    # 去除重复的框
    unique_boxes = list(set(boxes))

    if len(unique_boxes) <= 1:
        return unique_boxes

    # 按面积排序，大的在前
    boxes = sorted(unique_boxes, key=lambda box: (box[2] - box[0]) * (box[3] - box[1]), reverse=True)

    merged_boxes = []
    used = [False] * len(boxes)

    for i, box1 in enumerate(boxes):
        if used[i]:
            continue

        x1_min, y1_min, x1_max, y1_max = box1
        merged_box = list(box1)
        original_area = (x1_max - x1_min) * (y1_max - y1_min)

        for j, box2 in enumerate(boxes[i+1:], i+1):
            if used[j]:
                continue

            x2_min, y2_min, x2_max, y2_max = box2

            # 计算重叠面积
            overlap_x = max(0, min(x1_max, x2_max) - max(x1_min, x2_min))
            overlap_y = max(0, min(y1_max, y2_max) - max(y1_min, y2_min))
            overlap_area = overlap_x * overlap_y

            # 计算两个框的面积
            area1 = (x1_max - x1_min) * (y1_max - y1_min)
            area2 = (x2_max - x2_min) * (y2_max - y2_min)
            smaller_area = min(area1, area2)
            larger_area = max(area1, area2)

            # 更智能的合并判断：
            # 1. 小框被大框显著包含
            # 2. 两个框有显著重叠且大小相近
            overlap_ratio_small = overlap_area / smaller_area if smaller_area > 0 else 0
            overlap_ratio_large = overlap_area / larger_area if larger_area > 0 else 0
            size_ratio = smaller_area / larger_area if larger_area > 0 else 0

            should_merge = False
            if overlap_ratio_small > overlap_threshold:  # 小框被大框包含
                should_merge = True
            elif overlap_ratio_large > 0.3 and overlap_ratio_small > 0.4 and size_ratio > 0.3:
                # 两个框大小相近且有显著重叠
                should_merge = True

            if should_merge:
                # 检查合并后的框是否会变得过大
                new_x_min = min(merged_box[0], x2_min)
                new_y_min = min(merged_box[1], y2_min)
                new_x_max = max(merged_box[2], x2_max)
                new_y_max = max(merged_box[3], y2_max)
                new_area = (new_x_max - new_x_min) * (new_y_max - new_y_min)

                # 如果合并后面积不超过原始最大框的2.5倍，则合并
                if new_area <= original_area * 2.5:
                    merged_box[0] = new_x_min
                    merged_box[1] = new_y_min
                    merged_box[2] = new_x_max
                    merged_box[3] = new_y_max
                    used[j] = True

        merged_boxes.append(tuple(merged_box))
        used[i] = True

    return merged_boxes

def detect_handwriting_advanced(image_path):
    """
    使用更高级的方法检测手写体区域（优化版，专注手写体特征）
    结合边缘检测和手写体特征分析
    """
    # 读取图片
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError(f"无法读取图片: {image_path}")

    # 转换为灰度图
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)

    # 方法1：检测手写体的不规则边缘
    # 使用更保守的Canny参数，避免检测印刷体的规整边缘
    edges = cv2.Canny(gray, 30, 80, apertureSize=3)

    # 使用小核膨胀，只连接相近的笔画
    kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (3, 3))
    dilated = cv2.dilate(edges, kernel, iterations=1)

    # 查找轮廓
    contours1, _ = cv2.findContours(dilated, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    # 方法2：检测手写体的不规则形状
    # 使用更严格的自适应阈值
    thresh = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                   cv2.THRESH_BINARY_INV, 19, 6)

    # 使用小核进行形态学操作，保持手写体的不规则性
    small_kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (2, 2))
    morph = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, small_kernel, iterations=1)

    # 查找轮廓
    contours2, _ = cv2.findContours(morph, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

    # 合并两种方法的结果
    all_contours = contours1 + contours2

    # 专门针对手写体的过滤条件
    handwriting_boxes = []
    image_area = gray.shape[0] * gray.shape[1]
    min_area = max(150, image_area * 0.0008)  # 提高最小面积
    max_area = image_area * 0.12  # 降低最大面积

    for contour in all_contours:
        area = cv2.contourArea(contour)
        if min_area < area < max_area:
            x, y, w, h = cv2.boundingRect(contour)

            # 手写体特征过滤
            min_width = max(15, gray.shape[1] // 60)
            min_height = max(12, gray.shape[0] // 80)
            max_width = gray.shape[1] // 4
            max_height = gray.shape[0] // 5

            aspect_ratio = w / h
            bbox_area = w * h
            fill_ratio = area / bbox_area if bbox_area > 0 else 0

            # 计算轮廓的复杂度（周长平方/面积，手写体通常更复杂）
            perimeter = cv2.arcLength(contour, True)
            complexity = (perimeter * perimeter) / area if area > 0 else 0

            if (min_width <= w <= max_width and
                min_height <= h <= max_height and
                0.3 <= aspect_ratio <= 6.0 and  # 手写体的合理宽高比
                fill_ratio >= 0.2 and  # 足够的填充率
                complexity >= 15):  # 复杂度过滤，手写体比印刷体更复杂
                handwriting_boxes.append((x, y, x + w, y + h))

    # 合并重叠的边界框，使用更严格的阈值
    handwriting_boxes = merge_overlapping_boxes(handwriting_boxes, overlap_threshold=0.5)

    return handwriting_boxes

def create_bbox_mask(image_shape, boxes):
    """创建边界框区域的掩码，返回框内为True，框外为False的掩码"""
    h, w = image_shape[:2]
    mask = np.zeros((h, w), dtype=bool)

    for x_min, y_min, x_max, y_max in boxes:
        # 确保坐标在图像范围内，并且max坐标不能小于min坐标
        x_min = max(0, min(x_min, w-1))
        y_min = max(0, min(y_min, h-1))
        x_max = max(x_min, min(x_max, w-1))
        y_max = max(y_min, min(y_max, h-1))

        # 在掩码中标记边界框区域（确保有效的切片范围）
        if x_max > x_min and y_max > y_min:
            mask[y_min:y_max+1, x_min:x_max+1] = True

    return mask

def apply_white_noise_processing(image, bbox_mask):
    """应用白噪点处理：在框外区域随机添加10%-20%密度的白色像素点"""
    processed_image = image.copy()

    # 创建框外区域掩码
    outside_mask = ~bbox_mask

    # 调试信息
    total_pixels = bbox_mask.size
    bbox_pixels = np.sum(bbox_mask)
    outside_pixels = np.sum(outside_mask)
    print(f"  [调试] 总像素: {total_pixels}, 框内像素: {bbox_pixels}, 框外像素: {outside_pixels}")

    # 检查原图框内的白色像素数量
    original_bbox_region = image[bbox_mask]
    original_white_in_bbox = np.sum(np.all(original_bbox_region == [255, 255, 255], axis=1))
    print(f"  [调试] 原图框内白色像素: {original_white_in_bbox}")

    # 获取框外区域的像素位置
    outside_positions = np.where(outside_mask)
    total_outside_pixels = len(outside_positions[0])

    if total_outside_pixels > 0:
        # 固定选择80%的框外像素添加白噪点
        noise_density = 0.8
        num_noise_pixels = int(total_outside_pixels * noise_density)

        print(f"  [调试] 将添加 {num_noise_pixels} 个白噪点到框外区域")

        # 随机选择像素位置
        indices = random.sample(range(total_outside_pixels), num_noise_pixels)
        noise_y = outside_positions[0][indices]
        noise_x = outside_positions[1][indices]

        # 添加白色噪点（只在框外区域）
        processed_image[noise_y, noise_x] = [255, 255, 255]

        # 验证：检查处理后框内的白色像素数量
        processed_bbox_region = processed_image[bbox_mask]
        processed_white_in_bbox = np.sum(np.all(processed_bbox_region == [255, 255, 255], axis=1))

        if processed_white_in_bbox > original_white_in_bbox:
            print(f"  [警告] 框内新增了 {processed_white_in_bbox - original_white_in_bbox} 个白色像素！")
        elif processed_white_in_bbox == original_white_in_bbox:
            print(f"  [正常] 框内白色像素数量未变化，白噪点只在框外")
        else:
            print(f"  [异常] 框内白色像素减少了？这不应该发生")

    return processed_image

def apply_gaussian_blur_processing(image, bbox_mask):
    """应用高斯模糊处理：对框外背景轻度模糊（radius=5-10）"""
    processed_image = image.copy()

    # 随机选择模糊半径
    blur_radius = random.randint(5, 10)
    kernel_size = blur_radius * 2 + 1

    # 对整个图像应用高斯模糊
    blurred_image = cv2.GaussianBlur(image, (kernel_size, kernel_size), 0)

    # 只在框外区域应用模糊效果
    outside_mask = ~bbox_mask
    processed_image[outside_mask] = blurred_image[outside_mask]

    return processed_image

def apply_transparent_mask_processing(image, bbox_mask):
    """应用半透明蒙版处理：将框外区域覆盖50%透明度的黑色图层"""
    processed_image = image.copy().astype(np.float32)

    # 固定透明度和蒙版颜色
    alpha = 0.5  # 50%透明度
    mask_color = 0  # 黑色

    # 创建框外区域掩码
    outside_mask = ~bbox_mask

    # 应用半透明蒙版
    processed_image[outside_mask] = processed_image[outside_mask] * (1 - alpha) + mask_color * alpha

    return processed_image.astype(np.uint8)

def draw_boxes_on_image(image_path, boxes, output_path, processing_type="4"):
    """在图片上绘制边界框并根据处理类型应用不同的图像处理效果"""
    # 读取原图
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError(f"无法读取图片: {image_path}")

    # 创建边界框掩码
    bbox_mask = create_bbox_mask(image.shape, boxes)

    # 根据处理类型应用不同的图像处理
    if processing_type == "1":  # 白噪点
        processed_image = apply_white_noise_processing(image, bbox_mask)
    elif processing_type == "2":  # 高斯模糊
        processed_image = apply_gaussian_blur_processing(image, bbox_mask)
    elif processing_type == "3":  # 半透明蒙版
        processed_image = apply_transparent_mask_processing(image, bbox_mask)
    else:  # 仅画框（默认）
        processed_image = image.copy()

    # 定义不同颜色用于区分不同的边界框
    colors = [
        (0, 0, 255),    # 红色
        (0, 255, 0),    # 绿色
        (255, 0, 0),    # 蓝色
        (0, 255, 255),  # 黄色
        (255, 0, 255),  # 紫色
        (255, 255, 0),  # 青色
        (128, 0, 128),  # 紫色
        (255, 165, 0),  # 橙色
    ]

    # 绘制所有边界框
    for i, (x_min, y_min, x_max, y_max) in enumerate(boxes):
        # 选择颜色
        color = colors[i % len(colors)]

        # 绘制边界框
        cv2.rectangle(processed_image, (x_min, y_min), (x_max, y_max), color, 2)

        # 添加标签
        label = f"Handwriting{i+1}"
        cv2.putText(processed_image, label, (x_min, y_min - 10),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)

    # 保存结果图片
    cv2.imwrite(output_path, processed_image)
    return True

def get_detection_method():
    """让用户选择检测方法"""
    methods = {
        "1": "基础检测方法（适用于清晰的手写体）",
        "2": "高级检测方法（适用于复杂背景或模糊的手写体）",
        "3": "组合检测方法（同时使用两种方法，推荐）"
    }

    print("\n请选择手写体检测方法：")
    for key, value in methods.items():
        print(f"{key}. {value}")

    while True:
        user_input = input("请输入方法编号（1-3）：").strip()
        if user_input in methods:
            method = user_input
            break
        else:
            print("输入无效，请输入 1-3 的数字")

    print(f"选择的检测方法：{methods[method]}")
    return method

def get_detection_method_name(method_id):
    """根据方法ID获取方法名称"""
    method_names = {
        "1": "基础检测方法",
        "2": "高级检测方法",
        "3": "组合检测方法"
    }
    return method_names.get(method_id, "未知方法")

def process_single_image(image_path, output_dir, detection_method="3", processing_type="4"):
    """处理单张图片"""
    filename = os.path.basename(image_path)
    print(f"正在处理: {filename}")

    try:
        # 记录开始时间
        start_time = time.time()

        # 根据选择的方法检测手写体区域
        if detection_method == "1":
            boxes = detect_handwriting(image_path)
            method_name = "基础检测"
        elif detection_method == "2":
            boxes = detect_handwriting_advanced(image_path)
            method_name = "高级检测"
        else:  # detection_method == "3"
            # 组合两种方法的结果（优化版，减少误检）
            boxes1 = detect_handwriting(image_path)
            boxes2 = detect_handwriting_advanced(image_path)
            combined_boxes = boxes1 + boxes2

            # 如果有检测结果，进行额外过滤
            if combined_boxes:
                # 读取图片获取尺寸信息
                image = cv2.imread(image_path)
                if image is not None:
                    h, w = image.shape[:2]
                    image_area = h * w

                    # 过滤过大或过小的框
                    filtered_boxes = []
                    for box in combined_boxes:
                        x1, y1, x2, y2 = box
                        box_w = x2 - x1
                        box_h = y2 - y1
                        box_area = box_w * box_h

                        # 更严格的过滤条件
                        if (box_area >= image_area * 0.002 and  # 至少是图片的0.2%
                            box_area <= image_area * 0.12 and   # 最多是图片的12%
                            box_w >= 20 and box_h >= 15 and     # 最小尺寸
                            box_w <= w * 0.7 and box_h <= h * 0.5):  # 最大尺寸
                            filtered_boxes.append(box)

                    combined_boxes = filtered_boxes

            boxes = merge_overlapping_boxes(combined_boxes, overlap_threshold=0.5)
            method_name = "组合检测"

        # 记录结束时间并计算处理时间
        end_time = time.time()
        processing_time = end_time - start_time

        print(f"[{filename}] 使用{method_name}检测到 {len(boxes)} 个手写体区域，处理时间：{processing_time:.2f}秒")

        # 生成输出文件名
        bbox_filename = f"{os.path.splitext(filename)[0]}_with_bbox{os.path.splitext(filename)[1]}"
        bbox_output_path = os.path.join(output_dir, bbox_filename)

        # 绘制边界框并应用图片处理
        draw_boxes_on_image(image_path, boxes, bbox_output_path, processing_type)

        print(f"✓ 成功处理: {filename}")
        print(f"  - 标注图保存至: {bbox_output_path}")

        return {
            'success': True,
            'filename': filename,
            'bbox_filename': bbox_filename,
            'boxes': boxes,
            'box_count': len(boxes),
            'method': method_name,
            'processing_time': processing_time
        }

    except Exception as e:
        error_msg = f"处理图片 {filename} 时出错: {str(e)}"
        print(f"✗ {error_msg}")
        return {
            'success': False,
            'filename': filename,
            'error': str(e)
        }

def main():
    """主函数"""
    print("=" * 60)
    print("OpenCV手写体检测脚本")
    print("=" * 60)

    # 获取用户选择的题型
    question_type, pinyin_name = get_question_type()

    # 获取用户选择的检测方法
    detection_method = get_detection_method()

    # 获取用户选择的图片处理方式
    processing_type_id, processing_type_name = get_image_processing_type()

    # 构建题型相关的路径
    types_dir = "types"
    question_dir = os.path.join(types_dir, pinyin_name)
    images_dir = os.path.join(question_dir, "images")
    opencv_result_dir = os.path.join(question_dir, "OpenCV_result")

    print(f"\n使用配置：")
    print(f"题型：{question_type}")
    print(f"检测方法：{get_detection_method_name(detection_method)}")
    print(f"图片处理方式：{processing_type_name}")
    print(f"题型目录：{question_dir}")
    print(f"图片文件夹：{images_dir}")
    print(f"结果文件夹：{opencv_result_dir}")

    # 检查图片文件夹是否存在
    if not os.path.exists(images_dir):
        print(f"错误：图片文件夹 {images_dir} 不存在！")
        return

    # 检查并创建OpenCV_result目录
    os.makedirs(opencv_result_dir, exist_ok=True)

    # 获取图片文件
    image_files = get_image_files(images_dir)
    if not image_files:
        print(f"错误：在 {images_dir} 文件夹中没有找到图片文件！")
        print("支持的格式：.jpg, .jpeg, .png, .gif, .webp, .bmp")
        return

    print(f"找到 {len(image_files)} 张图片")

    # 创建输出目录
    timestamp = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    output_dir = os.path.join(opencv_result_dir, f"images_{timestamp}")
    os.makedirs(output_dir, exist_ok=True)
    print(f"输出目录: {output_dir}")

    # 处理所有图片
    print("\n--- 开始处理图片 ---\n")
    results = []
    success_count = 0
    total_boxes = 0

    for image_path in image_files:
        result = process_single_image(image_path, output_dir, detection_method, processing_type_id)
        results.append(result)

        if result['success']:
            success_count += 1
            total_boxes += result['box_count']
    
    # 生成处理报告
    print("\n" + "=" * 60)
    print("处理完成！")
    print("=" * 60)
    print(f"总计图片: {len(image_files)}")
    print(f"成功处理: {success_count}")
    print(f"失败处理: {len(image_files) - success_count}")
    print(f"成功率: {success_count/len(image_files)*100:.1f}%")
    print(f"总检测框数: {total_boxes}")
    print(f"平均每张图片检测框数: {total_boxes/success_count:.1f}" if success_count > 0 else "平均每张图片检测框数: 0")
    print(f"结果保存在: {output_dir}")

    # 获取检测方法名称
    method_name = get_detection_method_name(detection_method)

    # 生成summary.md报告
    summary_path = os.path.join(output_dir, "summary.md")
    with open(summary_path, 'w', encoding='utf-8') as f:
        # 头部统计信息
        f.write(f"# OpenCV手写体检测报告\n\n")
        f.write(f"**题型**: {question_type}\n\n")
        f.write(f"**检测方法**: {method_name}\n\n")
        f.write(f"**图片处理方式**: {processing_type_name}\n\n")
        f.write(f"**运行时间**: {timestamp}\n\n")
        f.write(f"**处理统计**:\n")
        f.write(f"- 总计图片: {len(image_files)}\n")
        f.write(f"- 成功处理: {success_count}\n")
        f.write(f"- 失败处理: {len(image_files) - success_count}\n")
        f.write(f"- 成功率: {success_count/len(image_files)*100:.1f}%\n")
        f.write(f"- 总检测框数: {total_boxes}\n")
        f.write(f"- 平均每张图片检测框数: {total_boxes/success_count:.1f}\n\n" if success_count > 0 else "- 平均每张图片检测框数: 0\n\n")

        # 详细处理结果
        f.write("## 详细处理结果\n\n")

        for i, result in enumerate(results, 1):
            f.write(f"### 第 {i} 张图片: {result['filename']}\n\n")

            if result['success']:
                # 显示处理后的图片
                f.write(f"![{result['bbox_filename']}]({result['bbox_filename']})\n\n")

                # OpenCV处理内容
                f.write("**OpenCV处理内容:**\n")
                f.write(f"- 使用方法: {result.get('method', '未知')}\n")
                f.write(f"- 检测到手写体区域数量: {result['box_count']}\n")
                f.write(f"- 处理时间: {result.get('processing_time', 0):.2f}秒\n")

                if result['boxes']:
                    f.write("- 边界框坐标 (x_min, y_min, x_max, y_max):\n")
                    for j, (x_min, y_min, x_max, y_max) in enumerate(result['boxes'], 1):
                        width = x_max - x_min
                        height = y_max - y_min
                        area = width * height
                        f.write(f"  - 区域{j}: ({x_min}, {y_min}, {x_max}, {y_max}) - 尺寸: {width}×{height}, 面积: {area}\n")
                else:
                    f.write("- 未检测到手写体区域\n")

            else:
                f.write(f"**处理失败**: {result['error']}\n")

            f.write("\n" + "-" * 50 + "\n\n")

        # 添加技术说明
        f.write("## 技术说明\n\n")
        f.write("### 检测方法说明\n")
        f.write("- **基础检测方法**: 使用自适应阈值和形态学操作检测文本区域\n")
        f.write("- **高级检测方法**: 结合边缘检测和文本区域分析，适用于复杂背景\n")
        f.write("- **组合检测方法**: 同时使用两种方法并合并结果，提高检测准确率\n\n")

        f.write("### 参数设置\n")
        f.write("- 最小检测区域面积: 动态调整（基于图片尺寸）\n")
        f.write("- 最大检测区域面积: 图片面积的30-50%\n")
        f.write("- 边界框合并阈值: 20-30%重叠率\n")
        f.write("- 宽高比限制: 1:15 到 15:1\n\n")

        # 添加结束标记
        f.write("=" * 50 + "\n")
        f.write("所有图片处理完成！\n")
        f.write("=" * 50 + "\n")

    print(f"Summary报告保存在: {summary_path}")

if __name__ == "__main__":
    main()
