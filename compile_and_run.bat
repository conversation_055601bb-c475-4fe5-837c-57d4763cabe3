@echo off
chcp 65001 >nul
echo 编译Java程序...
javac ImageProcessor.java

if %ERRORLEVEL% EQU 0 (
    echo 编译成功！
    echo.
    echo 创建JAR文件...
    jar cfe ImageProcessor.jar ImageProcessor ImageProcessor.class
    
    if %ERRORLEVEL% EQU 0 (
        echo JAR文件创建成功！
        echo.
        echo 现在可以运行Python脚本了！
        echo Python脚本会自动调用 ImageProcessor.jar
    ) else (
        echo JAR文件创建失败！
    )
) else (
    echo 编译失败！请检查Java环境是否正确安装。
    echo 确保已安装JDK并设置了JAVA_HOME环境变量。
)

pause 