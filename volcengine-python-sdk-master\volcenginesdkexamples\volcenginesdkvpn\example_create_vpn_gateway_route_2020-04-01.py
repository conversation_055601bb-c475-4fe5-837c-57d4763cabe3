# Example Code generated by Beijing Volcanoengine Technology.
from __future__ import print_function
import volcenginesdkcore
import volcenginesdkvpn
from pprint import pprint
from volcenginesdkcore.rest import ApiException

if __name__ == '__main__':
    configuration = volcenginesdkcore.Configuration()
    configuration.ak = "AK"
    configuration.sk = "SK"
    configuration.region = "cn-beijing"
    # set default configuration
    volcenginesdkcore.Configuration.set_default(configuration)

    # use global default configuration
    api_instance = volcenginesdkvpn.VPNApi()
    create_vpn_gateway_route_request = volcenginesdkvpn.CreateVpnGatewayRouteRequest(
        destination_cidr_block="172.XX.XX.0/24",
        next_hop_id="vgc-7qthudw0ll6jmc****",
        vpn_gateway_id="vgw-12bfa2du7fojk17q7y1rk****",
    )

    try:
        resp = api_instance.create_vpn_gateway_route(create_vpn_gateway_route_request)
        pprint(resp)
    except ApiException as e:
        print("Exception when calling api: %s\n" % e)
