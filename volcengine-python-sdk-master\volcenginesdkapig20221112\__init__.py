# coding: utf-8

# flake8: noqa

"""
    apig20221112

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


from __future__ import absolute_import

# import apis into sdk package
from volcenginesdkapig20221112.api.apig20221112_api import APIG20221112Api

# import models into sdk package
from volcenginesdkapig20221112.models.ai_provider_settings_for_create_route_input import AIProviderSettingsForCreateRouteInput
from volcenginesdkapig20221112.models.ai_provider_settings_for_get_route_output import AIProviderSettingsForGetRouteOutput
from volcenginesdkapig20221112.models.ai_provider_settings_for_list_routes_output import AIProviderSettingsForListRoutesOutput
from volcenginesdkapig20221112.models.ai_provider_settings_for_update_route_input import AIProviderSettingsForUpdateRouteInput
from volcenginesdkapig20221112.models.advanced_setting_for_create_route_input import AdvancedSettingForCreateRouteInput
from volcenginesdkapig20221112.models.advanced_setting_for_get_route_output import AdvancedSettingForGetRouteOutput
from volcenginesdkapig20221112.models.advanced_setting_for_list_routes_output import AdvancedSettingForListRoutesOutput
from volcenginesdkapig20221112.models.advanced_setting_for_update_route_input import AdvancedSettingForUpdateRouteInput
from volcenginesdkapig20221112.models.cors_policy_setting_for_create_route_input import CorsPolicySettingForCreateRouteInput
from volcenginesdkapig20221112.models.cors_policy_setting_for_get_route_output import CorsPolicySettingForGetRouteOutput
from volcenginesdkapig20221112.models.cors_policy_setting_for_list_routes_output import CorsPolicySettingForListRoutesOutput
from volcenginesdkapig20221112.models.cors_policy_setting_for_update_route_input import CorsPolicySettingForUpdateRouteInput
from volcenginesdkapig20221112.models.create_route_request import CreateRouteRequest
from volcenginesdkapig20221112.models.create_route_response import CreateRouteResponse
from volcenginesdkapig20221112.models.custom_domain_for_get_route_output import CustomDomainForGetRouteOutput
from volcenginesdkapig20221112.models.custom_domain_for_list_routes_output import CustomDomainForListRoutesOutput
from volcenginesdkapig20221112.models.delete_route_request import DeleteRouteRequest
from volcenginesdkapig20221112.models.delete_route_response import DeleteRouteResponse
from volcenginesdkapig20221112.models.domain_for_get_route_output import DomainForGetRouteOutput
from volcenginesdkapig20221112.models.domain_for_list_routes_output import DomainForListRoutesOutput
from volcenginesdkapig20221112.models.filter_for_list_routes_input import FilterForListRoutesInput
from volcenginesdkapig20221112.models.get_route_request import GetRouteRequest
from volcenginesdkapig20221112.models.get_route_response import GetRouteResponse
from volcenginesdkapig20221112.models.header_for_create_route_input import HeaderForCreateRouteInput
from volcenginesdkapig20221112.models.header_for_get_route_output import HeaderForGetRouteOutput
from volcenginesdkapig20221112.models.header_for_list_routes_output import HeaderForListRoutesOutput
from volcenginesdkapig20221112.models.header_for_update_route_input import HeaderForUpdateRouteInput
from volcenginesdkapig20221112.models.header_operation_for_create_route_input import HeaderOperationForCreateRouteInput
from volcenginesdkapig20221112.models.header_operation_for_get_route_output import HeaderOperationForGetRouteOutput
from volcenginesdkapig20221112.models.header_operation_for_list_routes_output import HeaderOperationForListRoutesOutput
from volcenginesdkapig20221112.models.header_operation_for_update_route_input import HeaderOperationForUpdateRouteInput
from volcenginesdkapig20221112.models.item_for_list_routes_output import ItemForListRoutesOutput
from volcenginesdkapig20221112.models.list_routes_request import ListRoutesRequest
from volcenginesdkapig20221112.models.list_routes_response import ListRoutesResponse
from volcenginesdkapig20221112.models.match_rule_for_create_route_input import MatchRuleForCreateRouteInput
from volcenginesdkapig20221112.models.match_rule_for_get_route_output import MatchRuleForGetRouteOutput
from volcenginesdkapig20221112.models.match_rule_for_list_routes_output import MatchRuleForListRoutesOutput
from volcenginesdkapig20221112.models.match_rule_for_update_route_input import MatchRuleForUpdateRouteInput
from volcenginesdkapig20221112.models.mirror_policy_for_create_route_input import MirrorPolicyForCreateRouteInput
from volcenginesdkapig20221112.models.mirror_policy_for_get_route_output import MirrorPolicyForGetRouteOutput
from volcenginesdkapig20221112.models.mirror_policy_for_list_routes_output import MirrorPolicyForListRoutesOutput
from volcenginesdkapig20221112.models.mirror_policy_for_update_route_input import MirrorPolicyForUpdateRouteInput
from volcenginesdkapig20221112.models.path_for_create_route_input import PathForCreateRouteInput
from volcenginesdkapig20221112.models.path_for_get_route_output import PathForGetRouteOutput
from volcenginesdkapig20221112.models.path_for_list_routes_output import PathForListRoutesOutput
from volcenginesdkapig20221112.models.path_for_update_route_input import PathForUpdateRouteInput
from volcenginesdkapig20221112.models.percent_for_create_route_input import PercentForCreateRouteInput
from volcenginesdkapig20221112.models.percent_for_get_route_output import PercentForGetRouteOutput
from volcenginesdkapig20221112.models.percent_for_list_routes_output import PercentForListRoutesOutput
from volcenginesdkapig20221112.models.percent_for_update_route_input import PercentForUpdateRouteInput
from volcenginesdkapig20221112.models.query_string_for_create_route_input import QueryStringForCreateRouteInput
from volcenginesdkapig20221112.models.query_string_for_get_route_output import QueryStringForGetRouteOutput
from volcenginesdkapig20221112.models.query_string_for_list_routes_output import QueryStringForListRoutesOutput
from volcenginesdkapig20221112.models.query_string_for_update_route_input import QueryStringForUpdateRouteInput
from volcenginesdkapig20221112.models.retry_policy_setting_for_create_route_input import RetryPolicySettingForCreateRouteInput
from volcenginesdkapig20221112.models.retry_policy_setting_for_get_route_output import RetryPolicySettingForGetRouteOutput
from volcenginesdkapig20221112.models.retry_policy_setting_for_list_routes_output import RetryPolicySettingForListRoutesOutput
from volcenginesdkapig20221112.models.retry_policy_setting_for_update_route_input import RetryPolicySettingForUpdateRouteInput
from volcenginesdkapig20221112.models.route_for_get_route_output import RouteForGetRouteOutput
from volcenginesdkapig20221112.models.tag_for_create_route_input import TagForCreateRouteInput
from volcenginesdkapig20221112.models.tag_for_get_route_output import TagForGetRouteOutput
from volcenginesdkapig20221112.models.tag_for_list_routes_output import TagForListRoutesOutput
from volcenginesdkapig20221112.models.timeout_setting_for_create_route_input import TimeoutSettingForCreateRouteInput
from volcenginesdkapig20221112.models.timeout_setting_for_get_route_output import TimeoutSettingForGetRouteOutput
from volcenginesdkapig20221112.models.timeout_setting_for_list_routes_output import TimeoutSettingForListRoutesOutput
from volcenginesdkapig20221112.models.timeout_setting_for_update_route_input import TimeoutSettingForUpdateRouteInput
from volcenginesdkapig20221112.models.url_rewrite_setting_for_create_route_input import URLRewriteSettingForCreateRouteInput
from volcenginesdkapig20221112.models.url_rewrite_setting_for_get_route_output import URLRewriteSettingForGetRouteOutput
from volcenginesdkapig20221112.models.url_rewrite_setting_for_list_routes_output import URLRewriteSettingForListRoutesOutput
from volcenginesdkapig20221112.models.url_rewrite_setting_for_update_route_input import URLRewriteSettingForUpdateRouteInput
from volcenginesdkapig20221112.models.update_route_request import UpdateRouteRequest
from volcenginesdkapig20221112.models.update_route_response import UpdateRouteResponse
from volcenginesdkapig20221112.models.upstream_for_create_route_input import UpstreamForCreateRouteInput
from volcenginesdkapig20221112.models.upstream_for_get_route_output import UpstreamForGetRouteOutput
from volcenginesdkapig20221112.models.upstream_for_list_routes_output import UpstreamForListRoutesOutput
from volcenginesdkapig20221112.models.upstream_for_update_route_input import UpstreamForUpdateRouteInput
from volcenginesdkapig20221112.models.upstream_list_for_create_route_input import UpstreamListForCreateRouteInput
from volcenginesdkapig20221112.models.upstream_list_for_get_route_output import UpstreamListForGetRouteOutput
from volcenginesdkapig20221112.models.upstream_list_for_list_routes_output import UpstreamListForListRoutesOutput
from volcenginesdkapig20221112.models.upstream_list_for_update_route_input import UpstreamListForUpdateRouteInput
from volcenginesdkapig20221112.models.value_for_create_route_input import ValueForCreateRouteInput
from volcenginesdkapig20221112.models.value_for_get_route_output import ValueForGetRouteOutput
from volcenginesdkapig20221112.models.value_for_list_routes_output import ValueForListRoutesOutput
from volcenginesdkapig20221112.models.value_for_update_route_input import ValueForUpdateRouteInput
