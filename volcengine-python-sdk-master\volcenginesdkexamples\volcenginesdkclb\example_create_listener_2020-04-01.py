# Example Code generated by Beijing Volcanoengine Technology.
from __future__ import print_function
import volcenginesdkcore
import volcenginesdkclb
from pprint import pprint
from volcenginesdkcore.rest import ApiException

if __name__ == '__main__':
    configuration = volcenginesdkcore.Configuration()
    configuration.ak = "Your AK"
    configuration.sk = "Your SK"
    configuration.region = "cn-beijing"
    # set default configuration
    volcenginesdkcore.Configuration.set_default(configuration)

    # use global default configuration
    api_instance = volcenginesdkclb.CLBApi()
    create_listener_request = volcenginesdkclb.CreateListenerRequest(
        acl_ids=["acl-3cj44nv0jhhxc6c6rrtet****"],
        acl_status="on",
        acl_type="black",
        enabled="on",
        established_timeout=122,
        listener_name="mylistener",
        load_balancer_id="clb-bp1o94dp5i6ea****",
        port=12,
        protocol="TCP",
        scheduler="wrr",
        server_group_id="rsp-bp1o94dp5i6ea****",
    )
    
    try:
        resp = api_instance.create_listener(create_listener_request)
        pprint(resp)
    except ApiException as e:
        print("Exception when calling api: %s\n" % e)
