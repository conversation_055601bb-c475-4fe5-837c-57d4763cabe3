# Example Code generated by Beijing Volcanoengine Technology.
from __future__ import print_function
import volcenginesdkcore
import volcenginesdkvpn
from pprint import pprint
from volcenginesdkcore.rest import ApiException

if __name__ == '__main__':
    configuration = volcenginesdkcore.Configuration()
    configuration.ak = "AK"
    configuration.sk = "SK"
    configuration.region = "cn-beijing"
    # set default configuration
    volcenginesdkcore.Configuration.set_default(configuration)

    # use global default configuration
    api_instance = volcenginesdkvpn.VPNApi()
    set_vpn_gateway_renewal_request = volcenginesdkvpn.SetVpnGatewayRenewalRequest(
        renew_type=3,
        vpn_gateway_id="vgw-2fe7zjsz13ksg5oxruwed****",
    )

    try:
        resp = api_instance.set_vpn_gateway_renewal(set_vpn_gateway_renewal_request)
        pprint(resp)
    except ApiException as e:
        print("Exception when calling api: %s\n" % e)
