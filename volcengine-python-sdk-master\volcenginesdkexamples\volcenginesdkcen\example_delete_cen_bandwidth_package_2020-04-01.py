# Example Code generated by Beijing Volcanoengine Technology.
from __future__ import print_function
import volcenginesdkcore
import volcenginesdkcen
from pprint import pprint
from volcenginesdkcore.rest import ApiException

if __name__ == '__main__':
    configuration = volcenginesdkcore.Configuration()
    configuration.ak = "Your AK"
    configuration.sk = "Your SK"
    configuration.region = "cn-beijing"
    # set default configuration
    volcenginesdkcore.Configuration.set_default(configuration)

    # use global default configuration
    api_instance = volcenginesdkcen.CENApi()
    delete_cen_bandwidth_package_request = volcenginesdkcen.DeleteCenBandwidthPackageRequest(
        cen_bandwidth_package_id="cbp-4c2zaavbvh5f42****",
    )
    
    try:
        resp = api_instance.delete_cen_bandwidth_package(delete_cen_bandwidth_package_request)
        pprint(resp)
    except ApiException as e:
        print("Exception when calling api: %s\n" % e)
