# Example Code generated by Beijing Volcanoengine Technology.
from __future__ import print_function
import volcenginesdkcore
import volcenginesdkvpc
from pprint import pprint
from volcenginesdkcore.rest import ApiException

if __name__ == '__main__':
    configuration = volcenginesdkcore.Configuration()
    configuration.ak = "Your AK"
    configuration.sk = "Your SK"
    configuration.region = "cn-beijing"
    # set default configuration
    volcenginesdkcore.Configuration.set_default(configuration)

    # use global default configuration
    api_instance = volcenginesdkvpc.VPCApi()
    create_network_interface_request = volcenginesdkvpc.CreateNetworkInterfaceRequest(
        network_interface_name="eni-1",
        primary_ip_address="192.XX.XX.10",
        security_group_ids=["sg-123edfgt8hhvj****"],
        subnet_id="subnet-h256yg29j****",
    )
    
    try:
        resp = api_instance.create_network_interface(create_network_interface_request)
        pprint(resp)
    except ApiException as e:
        print("Exception when calling api: %s\n" % e)
