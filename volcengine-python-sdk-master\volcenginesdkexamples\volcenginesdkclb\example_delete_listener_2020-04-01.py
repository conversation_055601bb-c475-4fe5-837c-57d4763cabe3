# Example Code generated by Beijing Volcanoengine Technology.
from __future__ import print_function
import volcenginesdkcore
import volcenginesdkclb
from pprint import pprint
from volcenginesdkcore.rest import ApiException

if __name__ == '__main__':
    configuration = volcenginesdkcore.Configuration()
    configuration.ak = "Your AK"
    configuration.sk = "Your SK"
    configuration.region = "cn-beijing"
    # set default configuration
    volcenginesdkcore.Configuration.set_default(configuration)

    # use global default configuration
    api_instance = volcenginesdkclb.CLBApi()
    delete_listener_request = volcenginesdkclb.DeleteListenerRequest(
        listener_id="lsn-2fek3rgsxhrsw5oxruwec****",
    )
    
    try:
        resp = api_instance.delete_listener(delete_listener_request)
        pprint(resp)
    except ApiException as e:
        print("Exception when calling api: %s\n" % e)
