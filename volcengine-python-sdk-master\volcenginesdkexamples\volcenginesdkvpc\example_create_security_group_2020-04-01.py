# Example Code generated by Beijing Volcanoengine Technology.
from __future__ import print_function
import volcenginesdkcore
import volcenginesdkvpc
from pprint import pprint
from volcenginesdkcore.rest import ApiException

if __name__ == '__main__':
    configuration = volcenginesdkcore.Configuration()
    configuration.ak = "Your AK"
    configuration.sk = "Your SK"
    configuration.region = "cn-beijing"
    # set default configuration
    volcenginesdkcore.Configuration.set_default(configuration)

    # use global default configuration
    api_instance = volcenginesdkvpc.VPCApi()
    create_security_group_request = volcenginesdkvpc.CreateSecurityGroupRequest(
        security_group_name="SG_1",
        vpc_id="vpc-bp1ou1zkhn00gzv****",
    )
    
    try:
        resp = api_instance.create_security_group(create_security_group_request)
        pprint(resp)
    except ApiException as e:
        print("Exception when calling api: %s\n" % e)
