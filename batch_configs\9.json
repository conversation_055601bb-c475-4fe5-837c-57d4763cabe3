{"batch_configs": [{"处理模式": 1, "模型ID": 1, "题型": 1, "图像文件夹": 1, "像素增强": "y", "灰度阀门": 180, "像素粘连": "n", "图像放大倍数": 1.5, "response_format": 1, "temperature": 0.7, "one_stage_test_prompt": "one_stage_prompt.md"}, {"处理模式": 2, "round2批改模式": 1, "模型ID": 2, "题型": 2, "图像文件夹": 1, "像素增强": "n", "像素粘连": "y", "图像放大倍数": 2, "response_format": 2, "top_p": 0.8, "test_prompt": "prompt.md", "test2_prompt": "round2_prompt.md"}, {"处理模式": 3, "模型ID": 3, "题型": 10, "图像文件夹": 1, "像素增强": "y", "灰度阀门": 200, "像素粘连": "n", "图像放大倍数": 1, "response_format": 1, "max_tokens": 4096, "test_prompt": "prompt.md", "test3_prompt": "round2_prompt_new.md"}, {"处理模式": 1, "模型ID": 4, "题型": 7, "图像文件夹": 1, "像素增强": "n", "像素粘连": "n", "图像放大倍数": 3, "response_format": 2, "temperature": 0.9, "top_p": 0.95, "one_stage_test_prompt": "你是一位严谨负责的资深数学老师，负责批改数学应用题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\\n以下是学生答题图片：\\n<student_answer_image>\\n{{STUDENT_ANSWER_IMAGE}}\\n</student_answer_image>\\n以下是正确答案：\\n<answer>\\n{{answer_json}}\\n</answer>\\n### 识别规则\\n#### 数学应用题\\n- **定位答题区域**：根据题号找到对应的答题位置。\\n- **答案判断**：\\n    - 仔细观察答题位置的解答过程和最终答案。\\n    - 重点关注最终答案的数值和单位。\\n    - 若答案正确则输出\\\"true\\\"，若答案错误或无答案则输出\\\"false\\\"。\\n### 输出格式\\n必须以JSON格式输出，键为\\\"题目1\\\"\\\"题目2\\\"……（按题号顺序编号），值为对应的判断结果。"}, {"处理模式": 2, "round2批改模式": 2, "模型ID": 1, "题型": 8, "图像文件夹": 1, "像素增强": "y", "灰度阀门": 160, "像素粘连": "y", "图像放大倍数": 1.2, "response_format": 1, "temperature": 0.6, "test_prompt": "prompt.md", "test2_prompt": "round2_prompt.md"}, {"处理模式": 3, "模型ID": 2, "题型": 11, "图像文件夹": 1, "像素增强": "n", "像素粘连": "n", "图像放大倍数": 2.5, "response_format": 2, "top_p": 0.7, "max_tokens": 6144, "test_prompt": "prompt.md", "test3_prompt": "round2_prompt_new.md"}, {"处理模式": 1, "模型ID": 3, "题型": 9, "图像文件夹": 1, "像素增强": "y", "灰度阀门": 220, "像素粘连": "n", "图像放大倍数": 1.8, "response_format": 1, "temperature": 0.5, "top_p": 0.85, "max_tokens": 2048, "one_stage_test_prompt": "你是一位严谨负责的资深数学老师，负责批改简单的四则运算题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\\n以下是学生答题图片：\\n<student_answer_image>\\n{{STUDENT_ANSWER_IMAGE}}\\n</student_answer_image>\\n以下是正确答案：\\n<answer>\\n{{answer_json}}\\n</answer>\\n### 识别规则\\n#### 四则运算题\\n- **定位答题区域**：根据题号找到对应的答题位置。\\n- **答案判断**：\\n    - 仔细观察答题位置的计算结果。\\n    - 检查数值计算是否正确。\\n    - 若答案正确则输出\\\"true\\\"，若答案错误或无答案则输出\\\"false\\\"。\\n### 输出格式\\n必须以JSON格式输出，键为\\\"题目1\\\"\\\"题目2\\\"……（按题号顺序编号），值为对应的判断结果。"}, {"处理模式": 2, "round2批改模式": 1, "模型ID": 4, "题型": 13, "图像文件夹": 1, "像素增强": "n", "像素粘连": "y", "图像放大倍数": 1, "response_format": 2, "temperature": 1.0, "top_p": 0.9, "max_tokens": 8192, "test_prompt": "prompt.md", "test2_prompt": "round2_prompt.md"}]}