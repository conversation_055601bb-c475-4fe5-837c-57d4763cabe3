# Example Code generated by Beijing Volcanoengine Technology.
from __future__ import print_function
import volcenginesdkcore
import volcenginesdkautoscaling
from pprint import pprint
from volcenginesdkcore.rest import ApiException

if __name__ == '__main__':
    configuration = volcenginesdkcore.Configuration()
    configuration.ak = "Your AK"
    configuration.sk = "Your SK"
    configuration.region = "cn-beijing"
    # set default configuration
    volcenginesdkcore.Configuration.set_default(configuration)

    # use global default configuration
    api_instance = volcenginesdkautoscaling.AUTOSCALINGApi()
    req_volumes = volcenginesdkautoscaling.VolumeForCreateScalingConfigurationInput(
        size=20,
        volume_type="PTSSD",
    )
    create_scaling_configuration_request = volcenginesdkautoscaling.CreateScalingConfigurationRequest(
        image_id="image-ybmhzqo8u4l8j1ii****",
        instance_name="instance-test",
        instance_types=["ecs.g1.2xlarge"],
        password="root@123",
        scaling_configuration_name="scaling-config-test",
        scaling_group_id="scg-ybmssdnnhn5pkgyd****",
        security_group_ids=["sg-3ti78x9h8t4bw*****"],
        volumes=[req_volumes],
        zone_id="cn-beijing-a",
    )
    
    try:
        resp = api_instance.create_scaling_configuration(create_scaling_configuration_request)
        pprint(resp)
    except ApiException as e:
        print("Exception when calling api: %s\n" % e)
