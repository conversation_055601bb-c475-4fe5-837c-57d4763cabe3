# Example Code generated by Beijing Volcanoengine Technology.
from __future__ import print_function
import volcenginesdkcore
import volcenginesdkvpn
from pprint import pprint
from volcenginesdkcore.rest import ApiException

if __name__ == '__main__':
    configuration = volcenginesdkcore.Configuration()
    configuration.ak = "AK"
    configuration.sk = "SK"
    configuration.region = "cn-beijing"
    # set default configuration
    volcenginesdkcore.Configuration.set_default(configuration)

    # use global default configuration
    api_instance = volcenginesdkvpn.VPNApi()
    modify_vpn_connection_attributes_request = volcenginesdkvpn.ModifyVpnConnectionAttributesRequest(
        vpn_connection_id="vgc-2bzvqi8kerd342dx0eg2f****",
    )

    try:
        resp = api_instance.modify_vpn_connection_attributes(modify_vpn_connection_attributes_request)
        pprint(resp)
    except ApiException as e:
        print("Exception when calling api: %s\n" % e)
