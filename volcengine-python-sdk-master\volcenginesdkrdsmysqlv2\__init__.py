# coding: utf-8

# flake8: noqa

"""
    rds_mysql_v2

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


from __future__ import absolute_import

# import apis into sdk package
from volcenginesdkrdsmysqlv2.api.rds_mysql_v2_api import RDSMYSQLV2Api

# import models into sdk package
from volcenginesdkrdsmysqlv2.models.account_for_describe_db_accounts_output import AccountForDescribeDBAccountsOutput
from volcenginesdkrdsmysqlv2.models.account_privilege_for_create_db_account_input import AccountPrivilegeForCreateDBAccountInput
from volcenginesdkrdsmysqlv2.models.account_privilege_for_describe_db_accounts_output import AccountPrivilegeForDescribeDBAccountsOutput
from volcenginesdkrdsmysqlv2.models.account_privilege_for_grant_db_account_privilege_input import AccountPrivilegeForGrantDBAccountPrivilegeInput
from volcenginesdkrdsmysqlv2.models.add_diagnostics_entity_request import AddDiagnosticsEntityRequest
from volcenginesdkrdsmysqlv2.models.add_diagnostics_entity_response import AddDiagnosticsEntityResponse
from volcenginesdkrdsmysqlv2.models.add_tags_to_resource_request import AddTagsToResourceRequest
from volcenginesdkrdsmysqlv2.models.add_tags_to_resource_response import AddTagsToResourceResponse
from volcenginesdkrdsmysqlv2.models.address_for_describe_db_instance_detail_output import AddressForDescribeDBInstanceDetailOutput
from volcenginesdkrdsmysqlv2.models.address_object_for_describe_db_instances_output import AddressObjectForDescribeDBInstancesOutput
from volcenginesdkrdsmysqlv2.models.allow_list_for_describe_allow_lists_output import AllowListForDescribeAllowListsOutput
from volcenginesdkrdsmysqlv2.models.associate_allow_list_request import AssociateAllowListRequest
from volcenginesdkrdsmysqlv2.models.associate_allow_list_response import AssociateAllowListResponse
from volcenginesdkrdsmysqlv2.models.associated_instance_for_describe_allow_list_detail_output import AssociatedInstanceForDescribeAllowListDetailOutput
from volcenginesdkrdsmysqlv2.models.auto_storage_scaling_config_for_create_db_instance_input import AutoStorageScalingConfigForCreateDBInstanceInput
from volcenginesdkrdsmysqlv2.models.auto_storage_scaling_config_for_create_dr_db_instance_input import AutoStorageScalingConfigForCreateDrDBInstanceInput
from volcenginesdkrdsmysqlv2.models.auto_storage_scaling_config_for_rebuild_db_instance_input import AutoStorageScalingConfigForRebuildDBInstanceInput
from volcenginesdkrdsmysqlv2.models.auto_storage_scaling_config_for_restore_to_cross_region_instance_input import AutoStorageScalingConfigForRestoreToCrossRegionInstanceInput
from volcenginesdkrdsmysqlv2.models.auto_storage_scaling_config_for_restore_to_new_instance_input import AutoStorageScalingConfigForRestoreToNewInstanceInput
from volcenginesdkrdsmysqlv2.models.backup_for_describe_backups_output import BackupForDescribeBackupsOutput
from volcenginesdkrdsmysqlv2.models.backup_meta_for_create_backup_input import BackupMetaForCreateBackupInput
from volcenginesdkrdsmysqlv2.models.basic_info_for_describe_db_instance_detail_output import BasicInfoForDescribeDBInstanceDetailOutput
from volcenginesdkrdsmysqlv2.models.binlog_file_for_describe_binlog_files_output import BinlogFileForDescribeBinlogFilesOutput
from volcenginesdkrdsmysqlv2.models.charge_detail_for_describe_db_instance_detail_output import ChargeDetailForDescribeDBInstanceDetailOutput
from volcenginesdkrdsmysqlv2.models.charge_detail_for_describe_db_instances_output import ChargeDetailForDescribeDBInstancesOutput
from volcenginesdkrdsmysqlv2.models.charge_info_for_create_db_instance_input import ChargeInfoForCreateDBInstanceInput
from volcenginesdkrdsmysqlv2.models.charge_info_for_rebuild_db_instance_input import ChargeInfoForRebuildDBInstanceInput
from volcenginesdkrdsmysqlv2.models.charge_info_for_restore_to_cross_region_instance_input import ChargeInfoForRestoreToCrossRegionInstanceInput
from volcenginesdkrdsmysqlv2.models.charge_info_for_restore_to_new_instance_input import ChargeInfoForRestoreToNewInstanceInput
from volcenginesdkrdsmysqlv2.models.charge_item_price_for_describe_db_instance_price_detail_output import ChargeItemPriceForDescribeDBInstancePriceDetailOutput
from volcenginesdkrdsmysqlv2.models.charge_item_price_for_describe_db_proxy_price_detail_output import ChargeItemPriceForDescribeDBProxyPriceDetailOutput
from volcenginesdkrdsmysqlv2.models.check_detail_for_describe_task_detail_output import CheckDetailForDescribeTaskDetailOutput
from volcenginesdkrdsmysqlv2.models.check_item_for_describe_task_detail_output import CheckItemForDescribeTaskDetailOutput
from volcenginesdkrdsmysqlv2.models.check_modify_db_proxy_allowed_for_describe_db_proxy_config_output import CheckModifyDBProxyAllowedForDescribeDBProxyConfigOutput
from volcenginesdkrdsmysqlv2.models.check_modify_db_proxy_allowed_v2_for_describe_db_proxy_config_output import CheckModifyDBProxyAllowedV2ForDescribeDBProxyConfigOutput
from volcenginesdkrdsmysqlv2.models.column_info_for_describe_db_account_table_column_info_output import ColumnInfoForDescribeDbAccountTableColumnInfoOutput
from volcenginesdkrdsmysqlv2.models.column_privilege_for_create_db_account_input import ColumnPrivilegeForCreateDBAccountInput
from volcenginesdkrdsmysqlv2.models.column_privilege_for_grant_db_account_privilege_input import ColumnPrivilegeForGrantDBAccountPrivilegeInput
from volcenginesdkrdsmysqlv2.models.convert_charge_item_price_for_describe_db_proxy_price_detail_output import ConvertChargeItemPriceForDescribeDBProxyPriceDetailOutput
from volcenginesdkrdsmysqlv2.models.copy_parameter_template_request import CopyParameterTemplateRequest
from volcenginesdkrdsmysqlv2.models.copy_parameter_template_response import CopyParameterTemplateResponse
from volcenginesdkrdsmysqlv2.models.create_allow_list_request import CreateAllowListRequest
from volcenginesdkrdsmysqlv2.models.create_allow_list_response import CreateAllowListResponse
from volcenginesdkrdsmysqlv2.models.create_backup_request import CreateBackupRequest
from volcenginesdkrdsmysqlv2.models.create_backup_response import CreateBackupResponse
from volcenginesdkrdsmysqlv2.models.create_binlog_backup_request import CreateBinlogBackupRequest
from volcenginesdkrdsmysqlv2.models.create_binlog_backup_response import CreateBinlogBackupResponse
from volcenginesdkrdsmysqlv2.models.create_db_account_request import CreateDBAccountRequest
from volcenginesdkrdsmysqlv2.models.create_db_account_response import CreateDBAccountResponse
from volcenginesdkrdsmysqlv2.models.create_db_endpoint_public_address_request import CreateDBEndpointPublicAddressRequest
from volcenginesdkrdsmysqlv2.models.create_db_endpoint_public_address_response import CreateDBEndpointPublicAddressResponse
from volcenginesdkrdsmysqlv2.models.create_db_endpoint_req_for_modify_db_endpoint_connection_mode_input import CreateDBEndpointReqForModifyDBEndpointConnectionModeInput
from volcenginesdkrdsmysqlv2.models.create_db_endpoint_request import CreateDBEndpointRequest
from volcenginesdkrdsmysqlv2.models.create_db_endpoint_response import CreateDBEndpointResponse
from volcenginesdkrdsmysqlv2.models.create_db_instance_request import CreateDBInstanceRequest
from volcenginesdkrdsmysqlv2.models.create_db_instance_response import CreateDBInstanceResponse
from volcenginesdkrdsmysqlv2.models.create_db_nodes_request import CreateDBNodesRequest
from volcenginesdkrdsmysqlv2.models.create_db_nodes_response import CreateDBNodesResponse
from volcenginesdkrdsmysqlv2.models.create_database_request import CreateDatabaseRequest
from volcenginesdkrdsmysqlv2.models.create_database_response import CreateDatabaseResponse
from volcenginesdkrdsmysqlv2.models.create_diagnostics_task_request import CreateDiagnosticsTaskRequest
from volcenginesdkrdsmysqlv2.models.create_diagnostics_task_response import CreateDiagnosticsTaskResponse
from volcenginesdkrdsmysqlv2.models.create_dr_db_instance_request import CreateDrDBInstanceRequest
from volcenginesdkrdsmysqlv2.models.create_dr_db_instance_response import CreateDrDBInstanceResponse
from volcenginesdkrdsmysqlv2.models.create_parameter_template_request import CreateParameterTemplateRequest
from volcenginesdkrdsmysqlv2.models.create_parameter_template_response import CreateParameterTemplateResponse
from volcenginesdkrdsmysqlv2.models.db_table_info_for_describe_backups_output import DBTableInfoForDescribeBackupsOutput
from volcenginesdkrdsmysqlv2.models.data_for_describe_cross_region_backup_db_instances_output import DataForDescribeCrossRegionBackupDBInstancesOutput
from volcenginesdkrdsmysqlv2.models.data_for_describe_deleted_db_instances_output import DataForDescribeDeletedDBInstancesOutput
from volcenginesdkrdsmysqlv2.models.data_for_describe_tasks_output import DataForDescribeTasksOutput
from volcenginesdkrdsmysqlv2.models.database_for_describe_databases_output import DatabaseForDescribeDatabasesOutput
from volcenginesdkrdsmysqlv2.models.database_privilege_for_create_database_input import DatabasePrivilegeForCreateDatabaseInput
from volcenginesdkrdsmysqlv2.models.database_privilege_for_describe_databases_output import DatabasePrivilegeForDescribeDatabasesOutput
from volcenginesdkrdsmysqlv2.models.database_privilege_for_grant_database_privilege_input import DatabasePrivilegeForGrantDatabasePrivilegeInput
from volcenginesdkrdsmysqlv2.models.delete_allow_list_request import DeleteAllowListRequest
from volcenginesdkrdsmysqlv2.models.delete_allow_list_response import DeleteAllowListResponse
from volcenginesdkrdsmysqlv2.models.delete_db_account_request import DeleteDBAccountRequest
from volcenginesdkrdsmysqlv2.models.delete_db_account_response import DeleteDBAccountResponse
from volcenginesdkrdsmysqlv2.models.delete_db_endpoint_public_address_request import DeleteDBEndpointPublicAddressRequest
from volcenginesdkrdsmysqlv2.models.delete_db_endpoint_public_address_response import DeleteDBEndpointPublicAddressResponse
from volcenginesdkrdsmysqlv2.models.delete_db_endpoint_request import DeleteDBEndpointRequest
from volcenginesdkrdsmysqlv2.models.delete_db_endpoint_response import DeleteDBEndpointResponse
from volcenginesdkrdsmysqlv2.models.delete_db_instance_request import DeleteDBInstanceRequest
from volcenginesdkrdsmysqlv2.models.delete_db_instance_response import DeleteDBInstanceResponse
from volcenginesdkrdsmysqlv2.models.delete_db_nodes_request import DeleteDBNodesRequest
from volcenginesdkrdsmysqlv2.models.delete_db_nodes_response import DeleteDBNodesResponse
from volcenginesdkrdsmysqlv2.models.delete_data_backup_request import DeleteDataBackupRequest
from volcenginesdkrdsmysqlv2.models.delete_data_backup_response import DeleteDataBackupResponse
from volcenginesdkrdsmysqlv2.models.delete_database_request import DeleteDatabaseRequest
from volcenginesdkrdsmysqlv2.models.delete_database_response import DeleteDatabaseResponse
from volcenginesdkrdsmysqlv2.models.delete_parameter_template_request import DeleteParameterTemplateRequest
from volcenginesdkrdsmysqlv2.models.delete_parameter_template_response import DeleteParameterTemplateResponse
from volcenginesdkrdsmysqlv2.models.describe_allow_list_detail_request import DescribeAllowListDetailRequest
from volcenginesdkrdsmysqlv2.models.describe_allow_list_detail_response import DescribeAllowListDetailResponse
from volcenginesdkrdsmysqlv2.models.describe_allow_lists_request import DescribeAllowListsRequest
from volcenginesdkrdsmysqlv2.models.describe_allow_lists_response import DescribeAllowListsResponse
from volcenginesdkrdsmysqlv2.models.describe_apply_parameter_template_request import DescribeApplyParameterTemplateRequest
from volcenginesdkrdsmysqlv2.models.describe_apply_parameter_template_response import DescribeApplyParameterTemplateResponse
from volcenginesdkrdsmysqlv2.models.describe_availability_zones_request import DescribeAvailabilityZonesRequest
from volcenginesdkrdsmysqlv2.models.describe_availability_zones_response import DescribeAvailabilityZonesResponse
from volcenginesdkrdsmysqlv2.models.describe_available_cross_region_request import DescribeAvailableCrossRegionRequest
from volcenginesdkrdsmysqlv2.models.describe_available_cross_region_response import DescribeAvailableCrossRegionResponse
from volcenginesdkrdsmysqlv2.models.describe_backup_decryption_key_request import DescribeBackupDecryptionKeyRequest
from volcenginesdkrdsmysqlv2.models.describe_backup_decryption_key_response import DescribeBackupDecryptionKeyResponse
from volcenginesdkrdsmysqlv2.models.describe_backup_encryption_status_request import DescribeBackupEncryptionStatusRequest
from volcenginesdkrdsmysqlv2.models.describe_backup_encryption_status_response import DescribeBackupEncryptionStatusResponse
from volcenginesdkrdsmysqlv2.models.describe_backup_policy_request import DescribeBackupPolicyRequest
from volcenginesdkrdsmysqlv2.models.describe_backup_policy_response import DescribeBackupPolicyResponse
from volcenginesdkrdsmysqlv2.models.describe_backup_stats_request import DescribeBackupStatsRequest
from volcenginesdkrdsmysqlv2.models.describe_backup_stats_response import DescribeBackupStatsResponse
from volcenginesdkrdsmysqlv2.models.describe_backups_request import DescribeBackupsRequest
from volcenginesdkrdsmysqlv2.models.describe_backups_response import DescribeBackupsResponse
from volcenginesdkrdsmysqlv2.models.describe_binlog_files_request import DescribeBinlogFilesRequest
from volcenginesdkrdsmysqlv2.models.describe_binlog_files_response import DescribeBinlogFilesResponse
from volcenginesdkrdsmysqlv2.models.describe_cross_backup_policy_request import DescribeCrossBackupPolicyRequest
from volcenginesdkrdsmysqlv2.models.describe_cross_backup_policy_response import DescribeCrossBackupPolicyResponse
from volcenginesdkrdsmysqlv2.models.describe_cross_region_backup_db_instances_request import DescribeCrossRegionBackupDBInstancesRequest
from volcenginesdkrdsmysqlv2.models.describe_cross_region_backup_db_instances_response import DescribeCrossRegionBackupDBInstancesResponse
from volcenginesdkrdsmysqlv2.models.describe_db_accounts_request import DescribeDBAccountsRequest
from volcenginesdkrdsmysqlv2.models.describe_db_accounts_response import DescribeDBAccountsResponse
from volcenginesdkrdsmysqlv2.models.describe_db_auto_scaling_config_request import DescribeDBAutoScalingConfigRequest
from volcenginesdkrdsmysqlv2.models.describe_db_auto_scaling_config_response import DescribeDBAutoScalingConfigResponse
from volcenginesdkrdsmysqlv2.models.describe_db_instance_detail_request import DescribeDBInstanceDetailRequest
from volcenginesdkrdsmysqlv2.models.describe_db_instance_detail_response import DescribeDBInstanceDetailResponse
from volcenginesdkrdsmysqlv2.models.describe_db_instance_engine_minor_versions_request import DescribeDBInstanceEngineMinorVersionsRequest
from volcenginesdkrdsmysqlv2.models.describe_db_instance_engine_minor_versions_response import DescribeDBInstanceEngineMinorVersionsResponse
from volcenginesdkrdsmysqlv2.models.describe_db_instance_nodes_request import DescribeDBInstanceNodesRequest
from volcenginesdkrdsmysqlv2.models.describe_db_instance_nodes_response import DescribeDBInstanceNodesResponse
from volcenginesdkrdsmysqlv2.models.describe_db_instance_parameters_log_request import DescribeDBInstanceParametersLogRequest
from volcenginesdkrdsmysqlv2.models.describe_db_instance_parameters_log_response import DescribeDBInstanceParametersLogResponse
from volcenginesdkrdsmysqlv2.models.describe_db_instance_parameters_request import DescribeDBInstanceParametersRequest
from volcenginesdkrdsmysqlv2.models.describe_db_instance_parameters_response import DescribeDBInstanceParametersResponse
from volcenginesdkrdsmysqlv2.models.describe_db_instance_price_detail_request import DescribeDBInstancePriceDetailRequest
from volcenginesdkrdsmysqlv2.models.describe_db_instance_price_detail_response import DescribeDBInstancePriceDetailResponse
from volcenginesdkrdsmysqlv2.models.describe_db_instance_price_detail_str_for_describe_db_instance_price_detail_output import DescribeDBInstancePriceDetailStrForDescribeDBInstancePriceDetailOutput
from volcenginesdkrdsmysqlv2.models.describe_db_instance_price_detail_str_for_describe_db_proxy_price_detail_output import DescribeDBInstancePriceDetailStrForDescribeDBProxyPriceDetailOutput
from volcenginesdkrdsmysqlv2.models.describe_db_instance_ssl_request import DescribeDBInstanceSSLRequest
from volcenginesdkrdsmysqlv2.models.describe_db_instance_ssl_response import DescribeDBInstanceSSLResponse
from volcenginesdkrdsmysqlv2.models.describe_db_instance_specs_request import DescribeDBInstanceSpecsRequest
from volcenginesdkrdsmysqlv2.models.describe_db_instance_specs_response import DescribeDBInstanceSpecsResponse
from volcenginesdkrdsmysqlv2.models.describe_db_instances_request import DescribeDBInstancesRequest
from volcenginesdkrdsmysqlv2.models.describe_db_instances_response import DescribeDBInstancesResponse
from volcenginesdkrdsmysqlv2.models.describe_db_node_parameter_differences_request import DescribeDBNodeParameterDifferencesRequest
from volcenginesdkrdsmysqlv2.models.describe_db_node_parameter_differences_response import DescribeDBNodeParameterDifferencesResponse
from volcenginesdkrdsmysqlv2.models.describe_db_proxy_config_request import DescribeDBProxyConfigRequest
from volcenginesdkrdsmysqlv2.models.describe_db_proxy_config_response import DescribeDBProxyConfigResponse
from volcenginesdkrdsmysqlv2.models.describe_db_proxy_price_detail_request import DescribeDBProxyPriceDetailRequest
from volcenginesdkrdsmysqlv2.models.describe_db_proxy_price_detail_response import DescribeDBProxyPriceDetailResponse
from volcenginesdkrdsmysqlv2.models.describe_db_proxy_price_detail_str_for_describe_db_proxy_price_detail_output import DescribeDBProxyPriceDetailStrForDescribeDBProxyPriceDetailOutput
from volcenginesdkrdsmysqlv2.models.describe_databases_request import DescribeDatabasesRequest
from volcenginesdkrdsmysqlv2.models.describe_databases_response import DescribeDatabasesResponse
from volcenginesdkrdsmysqlv2.models.describe_db_account_table_column_info_request import DescribeDbAccountTableColumnInfoRequest
from volcenginesdkrdsmysqlv2.models.describe_db_account_table_column_info_response import DescribeDbAccountTableColumnInfoResponse
from volcenginesdkrdsmysqlv2.models.describe_deleted_db_instances_request import DescribeDeletedDBInstancesRequest
from volcenginesdkrdsmysqlv2.models.describe_deleted_db_instances_response import DescribeDeletedDBInstancesResponse
from volcenginesdkrdsmysqlv2.models.describe_diagnostics_infos_request import DescribeDiagnosticsInfosRequest
from volcenginesdkrdsmysqlv2.models.describe_diagnostics_infos_response import DescribeDiagnosticsInfosResponse
from volcenginesdkrdsmysqlv2.models.describe_disaster_recovery_regions_request import DescribeDisasterRecoveryRegionsRequest
from volcenginesdkrdsmysqlv2.models.describe_disaster_recovery_regions_response import DescribeDisasterRecoveryRegionsResponse
from volcenginesdkrdsmysqlv2.models.describe_failover_logs_request import DescribeFailoverLogsRequest
from volcenginesdkrdsmysqlv2.models.describe_failover_logs_response import DescribeFailoverLogsResponse
from volcenginesdkrdsmysqlv2.models.describe_parameter_template_request import DescribeParameterTemplateRequest
from volcenginesdkrdsmysqlv2.models.describe_parameter_template_response import DescribeParameterTemplateResponse
from volcenginesdkrdsmysqlv2.models.describe_planned_events_request import DescribePlannedEventsRequest
from volcenginesdkrdsmysqlv2.models.describe_planned_events_response import DescribePlannedEventsResponse
from volcenginesdkrdsmysqlv2.models.describe_read_only_node_delay_request import DescribeReadOnlyNodeDelayRequest
from volcenginesdkrdsmysqlv2.models.describe_read_only_node_delay_response import DescribeReadOnlyNodeDelayResponse
from volcenginesdkrdsmysqlv2.models.describe_recoverable_time_request import DescribeRecoverableTimeRequest
from volcenginesdkrdsmysqlv2.models.describe_recoverable_time_response import DescribeRecoverableTimeResponse
from volcenginesdkrdsmysqlv2.models.describe_regions_request import DescribeRegionsRequest
from volcenginesdkrdsmysqlv2.models.describe_regions_response import DescribeRegionsResponse
from volcenginesdkrdsmysqlv2.models.describe_tags_by_resource_request import DescribeTagsByResourceRequest
from volcenginesdkrdsmysqlv2.models.describe_tags_by_resource_response import DescribeTagsByResourceResponse
from volcenginesdkrdsmysqlv2.models.describe_task_detail_request import DescribeTaskDetailRequest
from volcenginesdkrdsmysqlv2.models.describe_task_detail_response import DescribeTaskDetailResponse
from volcenginesdkrdsmysqlv2.models.describe_tasks_request import DescribeTasksRequest
from volcenginesdkrdsmysqlv2.models.describe_tasks_response import DescribeTasksResponse
from volcenginesdkrdsmysqlv2.models.diagnostics_info_for_describe_diagnostics_infos_output import DiagnosticsInfoForDescribeDiagnosticsInfosOutput
from volcenginesdkrdsmysqlv2.models.diagnostics_item_for_describe_diagnostics_infos_output import DiagnosticsItemForDescribeDiagnosticsInfosOutput
from volcenginesdkrdsmysqlv2.models.disassociate_allow_list_request import DisassociateAllowListRequest
from volcenginesdkrdsmysqlv2.models.disassociate_allow_list_response import DisassociateAllowListResponse
from volcenginesdkrdsmysqlv2.models.disaster_recovery_instance_for_describe_db_instance_detail_output import DisasterRecoveryInstanceForDescribeDBInstanceDetailOutput
from volcenginesdkrdsmysqlv2.models.download_backup_request import DownloadBackupRequest
from volcenginesdkrdsmysqlv2.models.download_backup_response import DownloadBackupResponse
from volcenginesdkrdsmysqlv2.models.download_ssl_certificate_request import DownloadSSLCertificateRequest
from volcenginesdkrdsmysqlv2.models.download_ssl_certificate_response import DownloadSSLCertificateResponse
from volcenginesdkrdsmysqlv2.models.ecs_info_for_describe_diagnostics_infos_output import EcsInfoForDescribeDiagnosticsInfosOutput
from volcenginesdkrdsmysqlv2.models.endpoint_for_describe_db_instance_detail_output import EndpointForDescribeDBInstanceDetailOutput
from volcenginesdkrdsmysqlv2.models.endpoint_info_for_describe_diagnostics_infos_output import EndpointInfoForDescribeDiagnosticsInfosOutput
from volcenginesdkrdsmysqlv2.models.estimation_result_for_create_db_nodes_output import EstimationResultForCreateDBNodesOutput
from volcenginesdkrdsmysqlv2.models.estimation_result_for_delete_db_nodes_output import EstimationResultForDeleteDBNodesOutput
from volcenginesdkrdsmysqlv2.models.estimation_result_for_modify_db_node_spec_output import EstimationResultForModifyDBNodeSpecOutput
from volcenginesdkrdsmysqlv2.models.estimation_result_for_modify_db_node_temporary_spec_output import EstimationResultForModifyDBNodeTemporarySpecOutput
from volcenginesdkrdsmysqlv2.models.failover_query_info_for_describe_failover_logs_output import FailoverQueryInfoForDescribeFailoverLogsOutput
from volcenginesdkrdsmysqlv2.models.feature_state_for_describe_db_proxy_config_output import FeatureStateForDescribeDBProxyConfigOutput
from volcenginesdkrdsmysqlv2.models.get_backup_download_link_request import GetBackupDownloadLinkRequest
from volcenginesdkrdsmysqlv2.models.get_backup_download_link_response import GetBackupDownloadLinkResponse
from volcenginesdkrdsmysqlv2.models.grant_db_account_privilege_request import GrantDBAccountPrivilegeRequest
from volcenginesdkrdsmysqlv2.models.grant_db_account_privilege_response import GrantDBAccountPrivilegeResponse
from volcenginesdkrdsmysqlv2.models.grant_database_privilege_request import GrantDatabasePrivilegeRequest
from volcenginesdkrdsmysqlv2.models.grant_database_privilege_response import GrantDatabasePrivilegeResponse
from volcenginesdkrdsmysqlv2.models.instance_for_describe_db_instances_output import InstanceForDescribeDBInstancesOutput
from volcenginesdkrdsmysqlv2.models.instance_specs_info_for_describe_db_instance_specs_output import InstanceSpecsInfoForDescribeDBInstanceSpecsOutput
from volcenginesdkrdsmysqlv2.models.instance_tag_for_create_db_instance_input import InstanceTagForCreateDBInstanceInput
from volcenginesdkrdsmysqlv2.models.instance_tag_for_rebuild_db_instance_input import InstanceTagForRebuildDBInstanceInput
from volcenginesdkrdsmysqlv2.models.instance_tag_for_restore_to_cross_region_instance_input import InstanceTagForRestoreToCrossRegionInstanceInput
from volcenginesdkrdsmysqlv2.models.instance_tag_for_restore_to_new_instance_input import InstanceTagForRestoreToNewInstanceInput
from volcenginesdkrdsmysqlv2.models.list_parameter_templates_request import ListParameterTemplatesRequest
from volcenginesdkrdsmysqlv2.models.list_parameter_templates_response import ListParameterTemplatesResponse
from volcenginesdkrdsmysqlv2.models.maintenance_window_for_create_db_instance_input import MaintenanceWindowForCreateDBInstanceInput
from volcenginesdkrdsmysqlv2.models.maintenance_window_for_describe_db_instance_detail_output import MaintenanceWindowForDescribeDBInstanceDetailOutput
from volcenginesdkrdsmysqlv2.models.maintenance_window_for_describe_db_instances_output import MaintenanceWindowForDescribeDBInstancesOutput
from volcenginesdkrdsmysqlv2.models.migrate_to_other_zone_request import MigrateToOtherZoneRequest
from volcenginesdkrdsmysqlv2.models.migrate_to_other_zone_response import MigrateToOtherZoneResponse
from volcenginesdkrdsmysqlv2.models.modify_allow_list_request import ModifyAllowListRequest
from volcenginesdkrdsmysqlv2.models.modify_allow_list_response import ModifyAllowListResponse
from volcenginesdkrdsmysqlv2.models.modify_backup_encryption_policy_request import ModifyBackupEncryptionPolicyRequest
from volcenginesdkrdsmysqlv2.models.modify_backup_encryption_policy_response import ModifyBackupEncryptionPolicyResponse
from volcenginesdkrdsmysqlv2.models.modify_backup_policy_request import ModifyBackupPolicyRequest
from volcenginesdkrdsmysqlv2.models.modify_backup_policy_response import ModifyBackupPolicyResponse
from volcenginesdkrdsmysqlv2.models.modify_backup_public_access_policy_request import ModifyBackupPublicAccessPolicyRequest
from volcenginesdkrdsmysqlv2.models.modify_backup_public_access_policy_response import ModifyBackupPublicAccessPolicyResponse
from volcenginesdkrdsmysqlv2.models.modify_cross_backup_policy_request import ModifyCrossBackupPolicyRequest
from volcenginesdkrdsmysqlv2.models.modify_cross_backup_policy_response import ModifyCrossBackupPolicyResponse
from volcenginesdkrdsmysqlv2.models.modify_db_account_description_request import ModifyDBAccountDescriptionRequest
from volcenginesdkrdsmysqlv2.models.modify_db_account_description_response import ModifyDBAccountDescriptionResponse
from volcenginesdkrdsmysqlv2.models.modify_db_account_host_request import ModifyDBAccountHostRequest
from volcenginesdkrdsmysqlv2.models.modify_db_account_host_response import ModifyDBAccountHostResponse
from volcenginesdkrdsmysqlv2.models.modify_db_auto_scaling_config_request import ModifyDBAutoScalingConfigRequest
from volcenginesdkrdsmysqlv2.models.modify_db_auto_scaling_config_response import ModifyDBAutoScalingConfigResponse
from volcenginesdkrdsmysqlv2.models.modify_db_endpoint_address_request import ModifyDBEndpointAddressRequest
from volcenginesdkrdsmysqlv2.models.modify_db_endpoint_address_response import ModifyDBEndpointAddressResponse
from volcenginesdkrdsmysqlv2.models.modify_db_endpoint_connection_mode_request import ModifyDBEndpointConnectionModeRequest
from volcenginesdkrdsmysqlv2.models.modify_db_endpoint_connection_mode_response import ModifyDBEndpointConnectionModeResponse
from volcenginesdkrdsmysqlv2.models.modify_db_endpoint_dns_request import ModifyDBEndpointDNSRequest
from volcenginesdkrdsmysqlv2.models.modify_db_endpoint_dns_response import ModifyDBEndpointDNSResponse
from volcenginesdkrdsmysqlv2.models.modify_db_endpoint_request import ModifyDBEndpointRequest
from volcenginesdkrdsmysqlv2.models.modify_db_endpoint_response import ModifyDBEndpointResponse
from volcenginesdkrdsmysqlv2.models.modify_db_instance_auto_upgrade_engine_minor_version_request import ModifyDBInstanceAutoUpgradeEngineMinorVersionRequest
from volcenginesdkrdsmysqlv2.models.modify_db_instance_auto_upgrade_engine_minor_version_response import ModifyDBInstanceAutoUpgradeEngineMinorVersionResponse
from volcenginesdkrdsmysqlv2.models.modify_db_instance_auto_upgrade_minor_version_request import ModifyDBInstanceAutoUpgradeMinorVersionRequest
from volcenginesdkrdsmysqlv2.models.modify_db_instance_auto_upgrade_minor_version_response import ModifyDBInstanceAutoUpgradeMinorVersionResponse
from volcenginesdkrdsmysqlv2.models.modify_db_instance_charge_type_request import ModifyDBInstanceChargeTypeRequest
from volcenginesdkrdsmysqlv2.models.modify_db_instance_charge_type_response import ModifyDBInstanceChargeTypeResponse
from volcenginesdkrdsmysqlv2.models.modify_db_instance_deletion_protection_policy_request import ModifyDBInstanceDeletionProtectionPolicyRequest
from volcenginesdkrdsmysqlv2.models.modify_db_instance_deletion_protection_policy_response import ModifyDBInstanceDeletionProtectionPolicyResponse
from volcenginesdkrdsmysqlv2.models.modify_db_instance_global_read_only_request import ModifyDBInstanceGlobalReadOnlyRequest
from volcenginesdkrdsmysqlv2.models.modify_db_instance_global_read_only_response import ModifyDBInstanceGlobalReadOnlyResponse
from volcenginesdkrdsmysqlv2.models.modify_db_instance_maintenance_window_request import ModifyDBInstanceMaintenanceWindowRequest
from volcenginesdkrdsmysqlv2.models.modify_db_instance_maintenance_window_response import ModifyDBInstanceMaintenanceWindowResponse
from volcenginesdkrdsmysqlv2.models.modify_db_instance_name_request import ModifyDBInstanceNameRequest
from volcenginesdkrdsmysqlv2.models.modify_db_instance_name_response import ModifyDBInstanceNameResponse
from volcenginesdkrdsmysqlv2.models.modify_db_instance_parameters_request import ModifyDBInstanceParametersRequest
from volcenginesdkrdsmysqlv2.models.modify_db_instance_parameters_response import ModifyDBInstanceParametersResponse
from volcenginesdkrdsmysqlv2.models.modify_db_instance_ssl_request import ModifyDBInstanceSSLRequest
from volcenginesdkrdsmysqlv2.models.modify_db_instance_ssl_response import ModifyDBInstanceSSLResponse
from volcenginesdkrdsmysqlv2.models.modify_db_instance_spec_request import ModifyDBInstanceSpecRequest
from volcenginesdkrdsmysqlv2.models.modify_db_instance_spec_response import ModifyDBInstanceSpecResponse
from volcenginesdkrdsmysqlv2.models.modify_db_instance_sync_mode_request import ModifyDBInstanceSyncModeRequest
from volcenginesdkrdsmysqlv2.models.modify_db_instance_sync_mode_response import ModifyDBInstanceSyncModeResponse
from volcenginesdkrdsmysqlv2.models.modify_db_node_spec_request import ModifyDBNodeSpecRequest
from volcenginesdkrdsmysqlv2.models.modify_db_node_spec_response import ModifyDBNodeSpecResponse
from volcenginesdkrdsmysqlv2.models.modify_db_node_temporary_spec_request import ModifyDBNodeTemporarySpecRequest
from volcenginesdkrdsmysqlv2.models.modify_db_node_temporary_spec_response import ModifyDBNodeTemporarySpecResponse
from volcenginesdkrdsmysqlv2.models.modify_db_proxy_config_request import ModifyDBProxyConfigRequest
from volcenginesdkrdsmysqlv2.models.modify_db_proxy_config_response import ModifyDBProxyConfigResponse
from volcenginesdkrdsmysqlv2.models.modify_db_proxy_request import ModifyDBProxyRequest
from volcenginesdkrdsmysqlv2.models.modify_db_proxy_response import ModifyDBProxyResponse
from volcenginesdkrdsmysqlv2.models.modify_db_proxy_spec_request import ModifyDBProxySpecRequest
from volcenginesdkrdsmysqlv2.models.modify_db_proxy_spec_response import ModifyDBProxySpecResponse
from volcenginesdkrdsmysqlv2.models.modify_database_description_request import ModifyDatabaseDescriptionRequest
from volcenginesdkrdsmysqlv2.models.modify_database_description_response import ModifyDatabaseDescriptionResponse
from volcenginesdkrdsmysqlv2.models.modify_parameter_template_request import ModifyParameterTemplateRequest
from volcenginesdkrdsmysqlv2.models.modify_parameter_template_response import ModifyParameterTemplateResponse
from volcenginesdkrdsmysqlv2.models.modify_planned_event_execute_time_request import ModifyPlannedEventExecuteTimeRequest
from volcenginesdkrdsmysqlv2.models.modify_planned_event_execute_time_response import ModifyPlannedEventExecuteTimeResponse
from volcenginesdkrdsmysqlv2.models.modify_read_only_node_delay_replication_time_request import ModifyReadOnlyNodeDelayReplicationTimeRequest
from volcenginesdkrdsmysqlv2.models.modify_read_only_node_delay_replication_time_response import ModifyReadOnlyNodeDelayReplicationTimeResponse
from volcenginesdkrdsmysqlv2.models.modify_task_request import ModifyTaskRequest
from volcenginesdkrdsmysqlv2.models.modify_task_response import ModifyTaskResponse
from volcenginesdkrdsmysqlv2.models.node_different_parameter_for_describe_db_node_parameter_differences_output import NodeDifferentParameterForDescribeDBNodeParameterDifferencesOutput
from volcenginesdkrdsmysqlv2.models.node_for_describe_db_instance_detail_output import NodeForDescribeDBInstanceDetailOutput
from volcenginesdkrdsmysqlv2.models.node_info_for_create_db_instance_input import NodeInfoForCreateDBInstanceInput
from volcenginesdkrdsmysqlv2.models.node_info_for_create_db_nodes_input import NodeInfoForCreateDBNodesInput
from volcenginesdkrdsmysqlv2.models.node_info_for_create_dr_db_instance_input import NodeInfoForCreateDrDBInstanceInput
from volcenginesdkrdsmysqlv2.models.node_info_for_describe_db_instance_nodes_output import NodeInfoForDescribeDBInstanceNodesOutput
from volcenginesdkrdsmysqlv2.models.node_info_for_describe_db_instance_price_detail_input import NodeInfoForDescribeDBInstancePriceDetailInput
from volcenginesdkrdsmysqlv2.models.node_info_for_migrate_to_other_zone_input import NodeInfoForMigrateToOtherZoneInput
from volcenginesdkrdsmysqlv2.models.node_info_for_modify_db_instance_spec_input import NodeInfoForModifyDBInstanceSpecInput
from volcenginesdkrdsmysqlv2.models.node_info_for_modify_db_node_spec_input import NodeInfoForModifyDBNodeSpecInput
from volcenginesdkrdsmysqlv2.models.node_info_for_modify_db_node_temporary_spec_input import NodeInfoForModifyDBNodeTemporarySpecInput
from volcenginesdkrdsmysqlv2.models.node_info_for_rebuild_db_instance_input import NodeInfoForRebuildDBInstanceInput
from volcenginesdkrdsmysqlv2.models.node_info_for_restore_to_cross_region_instance_input import NodeInfoForRestoreToCrossRegionInstanceInput
from volcenginesdkrdsmysqlv2.models.node_info_for_restore_to_new_instance_input import NodeInfoForRestoreToNewInstanceInput
from volcenginesdkrdsmysqlv2.models.node_parameter_value_detail_for_describe_db_node_parameter_differences_output import NodeParameterValueDetailForDescribeDBNodeParameterDifferencesOutput
from volcenginesdkrdsmysqlv2.models.option_for_describe_backups_input import OptionForDescribeBackupsInput
from volcenginesdkrdsmysqlv2.models.parameter_change_log_for_describe_db_instance_parameters_log_output import ParameterChangeLogForDescribeDBInstanceParametersLogOutput
from volcenginesdkrdsmysqlv2.models.parameter_for_describe_apply_parameter_template_output import ParameterForDescribeApplyParameterTemplateOutput
from volcenginesdkrdsmysqlv2.models.parameter_for_describe_db_instance_parameters_output import ParameterForDescribeDBInstanceParametersOutput
from volcenginesdkrdsmysqlv2.models.parameter_for_modify_db_instance_parameters_input import ParameterForModifyDBInstanceParametersInput
from volcenginesdkrdsmysqlv2.models.planned_event_for_describe_planned_events_output import PlannedEventForDescribePlannedEventsOutput
from volcenginesdkrdsmysqlv2.models.proxy_detail_for_describe_db_instance_detail_output import ProxyDetailForDescribeDBInstanceDetailOutput
from volcenginesdkrdsmysqlv2.models.proxy_node_custom_for_describe_db_instance_price_detail_input import ProxyNodeCustomForDescribeDBInstancePriceDetailInput
from volcenginesdkrdsmysqlv2.models.proxy_node_custom_for_describe_db_proxy_price_detail_input import ProxyNodeCustomForDescribeDBProxyPriceDetailInput
from volcenginesdkrdsmysqlv2.models.proxy_node_custom_for_modify_db_proxy_input import ProxyNodeCustomForModifyDBProxyInput
from volcenginesdkrdsmysqlv2.models.proxy_node_custom_for_modify_db_proxy_spec_input import ProxyNodeCustomForModifyDBProxySpecInput
from volcenginesdkrdsmysqlv2.models.proxy_resource_info_for_describe_db_instance_detail_output import ProxyResourceInfoForDescribeDBInstanceDetailOutput
from volcenginesdkrdsmysqlv2.models.proxy_resource_info_for_describe_db_proxy_config_output import ProxyResourceInfoForDescribeDBProxyConfigOutput
from volcenginesdkrdsmysqlv2.models.public_address_info_for_describe_diagnostics_infos_output import PublicAddressInfoForDescribeDiagnosticsInfosOutput
from volcenginesdkrdsmysqlv2.models.read_only_node_weight_for_create_db_endpoint_input import ReadOnlyNodeWeightForCreateDBEndpointInput
from volcenginesdkrdsmysqlv2.models.read_only_node_weight_for_describe_db_instance_detail_output import ReadOnlyNodeWeightForDescribeDBInstanceDetailOutput
from volcenginesdkrdsmysqlv2.models.read_only_node_weight_for_modify_db_endpoint_connection_mode_input import ReadOnlyNodeWeightForModifyDBEndpointConnectionModeInput
from volcenginesdkrdsmysqlv2.models.read_only_node_weight_for_modify_db_endpoint_input import ReadOnlyNodeWeightForModifyDBEndpointInput
from volcenginesdkrdsmysqlv2.models.rebuild_db_instance_request import RebuildDBInstanceRequest
from volcenginesdkrdsmysqlv2.models.rebuild_db_instance_response import RebuildDBInstanceResponse
from volcenginesdkrdsmysqlv2.models.rebuild_dr_instance_request import RebuildDrInstanceRequest
from volcenginesdkrdsmysqlv2.models.rebuild_dr_instance_response import RebuildDrInstanceResponse
from volcenginesdkrdsmysqlv2.models.recoverable_time_info_for_describe_recoverable_time_output import RecoverableTimeInfoForDescribeRecoverableTimeOutput
from volcenginesdkrdsmysqlv2.models.region_for_describe_disaster_recovery_regions_output import RegionForDescribeDisasterRecoveryRegionsOutput
from volcenginesdkrdsmysqlv2.models.region_for_describe_regions_output import RegionForDescribeRegionsOutput
from volcenginesdkrdsmysqlv2.models.related_instance_infos_for_describe_task_detail_output import RelatedInstanceInfosForDescribeTaskDetailOutput
from volcenginesdkrdsmysqlv2.models.related_instance_infos_for_describe_tasks_output import RelatedInstanceInfosForDescribeTasksOutput
from volcenginesdkrdsmysqlv2.models.remove_diagnostics_entity_request import RemoveDiagnosticsEntityRequest
from volcenginesdkrdsmysqlv2.models.remove_diagnostics_entity_response import RemoveDiagnosticsEntityResponse
from volcenginesdkrdsmysqlv2.models.remove_tags_from_resource_request import RemoveTagsFromResourceRequest
from volcenginesdkrdsmysqlv2.models.remove_tags_from_resource_response import RemoveTagsFromResourceResponse
from volcenginesdkrdsmysqlv2.models.reset_db_account_request import ResetDBAccountRequest
from volcenginesdkrdsmysqlv2.models.reset_db_account_response import ResetDBAccountResponse
from volcenginesdkrdsmysqlv2.models.restart_db_instance_request import RestartDBInstanceRequest
from volcenginesdkrdsmysqlv2.models.restart_db_instance_response import RestartDBInstanceResponse
from volcenginesdkrdsmysqlv2.models.restore_to_cross_region_instance_request import RestoreToCrossRegionInstanceRequest
from volcenginesdkrdsmysqlv2.models.restore_to_cross_region_instance_response import RestoreToCrossRegionInstanceResponse
from volcenginesdkrdsmysqlv2.models.restore_to_existed_instance_request import RestoreToExistedInstanceRequest
from volcenginesdkrdsmysqlv2.models.restore_to_existed_instance_response import RestoreToExistedInstanceResponse
from volcenginesdkrdsmysqlv2.models.restore_to_new_instance_request import RestoreToNewInstanceRequest
from volcenginesdkrdsmysqlv2.models.restore_to_new_instance_response import RestoreToNewInstanceResponse
from volcenginesdkrdsmysqlv2.models.revoke_db_account_privilege_request import RevokeDBAccountPrivilegeRequest
from volcenginesdkrdsmysqlv2.models.revoke_db_account_privilege_response import RevokeDBAccountPrivilegeResponse
from volcenginesdkrdsmysqlv2.models.revoke_database_privilege_request import RevokeDatabasePrivilegeRequest
from volcenginesdkrdsmysqlv2.models.revoke_database_privilege_response import RevokeDatabasePrivilegeResponse
from volcenginesdkrdsmysqlv2.models.save_as_parameter_template_request import SaveAsParameterTemplateRequest
from volcenginesdkrdsmysqlv2.models.save_as_parameter_template_response import SaveAsParameterTemplateResponse
from volcenginesdkrdsmysqlv2.models.security_group_bind_info_for_create_allow_list_input import SecurityGroupBindInfoForCreateAllowListInput
from volcenginesdkrdsmysqlv2.models.security_group_bind_info_for_describe_allow_list_detail_output import SecurityGroupBindInfoForDescribeAllowListDetailOutput
from volcenginesdkrdsmysqlv2.models.security_group_bind_info_for_describe_allow_lists_output import SecurityGroupBindInfoForDescribeAllowListsOutput
from volcenginesdkrdsmysqlv2.models.security_group_bind_info_for_modify_allow_list_input import SecurityGroupBindInfoForModifyAllowListInput
from volcenginesdkrdsmysqlv2.models.start_db_instance_request import StartDBInstanceRequest
from volcenginesdkrdsmysqlv2.models.start_db_instance_response import StartDBInstanceResponse
from volcenginesdkrdsmysqlv2.models.step_extra_info_for_describe_task_detail_output import StepExtraInfoForDescribeTaskDetailOutput
from volcenginesdkrdsmysqlv2.models.step_extra_info_for_describe_tasks_output import StepExtraInfoForDescribeTasksOutput
from volcenginesdkrdsmysqlv2.models.stop_db_instance_request import StopDBInstanceRequest
from volcenginesdkrdsmysqlv2.models.stop_db_instance_response import StopDBInstanceResponse
from volcenginesdkrdsmysqlv2.models.storage_config_for_describe_db_auto_scaling_config_output import StorageConfigForDescribeDBAutoScalingConfigOutput
from volcenginesdkrdsmysqlv2.models.storage_config_for_modify_db_auto_scaling_config_input import StorageConfigForModifyDBAutoScalingConfigInput
from volcenginesdkrdsmysqlv2.models.switch_db_instance_ha_request import SwitchDBInstanceHARequest
from volcenginesdkrdsmysqlv2.models.switch_db_instance_ha_response import SwitchDBInstanceHAResponse
from volcenginesdkrdsmysqlv2.models.switch_dr_instance_to_master_request import SwitchDrInstanceToMasterRequest
from volcenginesdkrdsmysqlv2.models.switch_dr_instance_to_master_response import SwitchDrInstanceToMasterResponse
from volcenginesdkrdsmysqlv2.models.table_column_privilege_for_create_db_account_input import TableColumnPrivilegeForCreateDBAccountInput
from volcenginesdkrdsmysqlv2.models.table_column_privilege_for_grant_db_account_privilege_input import TableColumnPrivilegeForGrantDBAccountPrivilegeInput
from volcenginesdkrdsmysqlv2.models.table_for_restore_to_existed_instance_input import TableForRestoreToExistedInstanceInput
from volcenginesdkrdsmysqlv2.models.table_info_for_describe_db_account_table_column_info_output import TableInfoForDescribeDbAccountTableColumnInfoOutput
from volcenginesdkrdsmysqlv2.models.table_meta_for_restore_to_existed_instance_input import TableMetaForRestoreToExistedInstanceInput
from volcenginesdkrdsmysqlv2.models.table_privilege_for_create_db_account_input import TablePrivilegeForCreateDBAccountInput
from volcenginesdkrdsmysqlv2.models.table_privilege_for_grant_db_account_privilege_input import TablePrivilegeForGrantDBAccountPrivilegeInput
from volcenginesdkrdsmysqlv2.models.tag_filter_for_describe_db_instances_input import TagFilterForDescribeDBInstancesInput
from volcenginesdkrdsmysqlv2.models.tag_filter_for_describe_tags_by_resource_input import TagFilterForDescribeTagsByResourceInput
from volcenginesdkrdsmysqlv2.models.tag_for_add_tags_to_resource_input import TagForAddTagsToResourceInput
from volcenginesdkrdsmysqlv2.models.tag_for_describe_db_instance_detail_output import TagForDescribeDBInstanceDetailOutput
from volcenginesdkrdsmysqlv2.models.tag_for_describe_db_instances_output import TagForDescribeDBInstancesOutput
from volcenginesdkrdsmysqlv2.models.tag_resource_for_describe_tags_by_resource_output import TagResourceForDescribeTagsByResourceOutput
from volcenginesdkrdsmysqlv2.models.task_info_for_describe_task_detail_output import TaskInfoForDescribeTaskDetailOutput
from volcenginesdkrdsmysqlv2.models.task_progress_for_describe_task_detail_output import TaskProgressForDescribeTaskDetailOutput
from volcenginesdkrdsmysqlv2.models.task_progress_for_describe_tasks_output import TaskProgressForDescribeTasksOutput
from volcenginesdkrdsmysqlv2.models.template_info_for_describe_parameter_template_output import TemplateInfoForDescribeParameterTemplateOutput
from volcenginesdkrdsmysqlv2.models.template_info_for_list_parameter_templates_output import TemplateInfoForListParameterTemplatesOutput
from volcenginesdkrdsmysqlv2.models.template_param_for_create_parameter_template_input import TemplateParamForCreateParameterTemplateInput
from volcenginesdkrdsmysqlv2.models.template_param_for_describe_parameter_template_output import TemplateParamForDescribeParameterTemplateOutput
from volcenginesdkrdsmysqlv2.models.template_param_for_list_parameter_templates_output import TemplateParamForListParameterTemplatesOutput
from volcenginesdkrdsmysqlv2.models.template_param_for_modify_parameter_template_input import TemplateParamForModifyParameterTemplateInput
from volcenginesdkrdsmysqlv2.models.upgrade_allow_list_version_request import UpgradeAllowListVersionRequest
from volcenginesdkrdsmysqlv2.models.upgrade_allow_list_version_response import UpgradeAllowListVersionResponse
from volcenginesdkrdsmysqlv2.models.upgrade_db_instance_engine_minor_version_request import UpgradeDBInstanceEngineMinorVersionRequest
from volcenginesdkrdsmysqlv2.models.upgrade_db_instance_engine_minor_version_response import UpgradeDBInstanceEngineMinorVersionResponse
from volcenginesdkrdsmysqlv2.models.upgrade_db_instance_kernel_version_request import UpgradeDBInstanceKernelVersionRequest
from volcenginesdkrdsmysqlv2.models.upgrade_db_instance_kernel_version_response import UpgradeDBInstanceKernelVersionResponse
from volcenginesdkrdsmysqlv2.models.usage_stat_for_describe_backup_stats_output import UsageStatForDescribeBackupStatsOutput
from volcenginesdkrdsmysqlv2.models.zone_for_describe_availability_zones_output import ZoneForDescribeAvailabilityZonesOutput
