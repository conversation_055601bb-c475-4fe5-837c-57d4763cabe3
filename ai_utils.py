import re
import json

def extract_json_from_response(response_text):
    json_match = re.search(r'```json\s*({.*?})\s*```', response_text, re.DOTALL)
    if json_match:
        json_str = json_match.group(1)
        if is_valid_json(json_str):
            return json_str
    try:
        if is_valid_json(response_text):
            return response_text
    except:
        pass
    json_match = re.search(r'({.*})', response_text, re.DOTALL)
    if json_match:
        json_str = json_match.group(0)
        if is_valid_json(json_str):
            return json_str
    return None

def is_valid_json(json_str):
    try:
        json.loads(json_str)
        return True
    except json.JSONDecodeError:
        return False 