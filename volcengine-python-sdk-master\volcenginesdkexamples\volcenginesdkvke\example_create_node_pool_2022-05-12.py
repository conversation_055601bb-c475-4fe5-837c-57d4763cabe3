# Example Code generated by Beijing Volcanoengine Technology.
from __future__ import print_function
import volcenginesdkcore
import volcenginesdkvke
from pprint import pprint
from volcenginesdkcore.rest import ApiException

if __name__ == '__main__':
    configuration = volcenginesdkcore.Configuration()
    configuration.ak = "AK"
    configuration.sk = "SK"
    configuration.region = "cn-beijing"
    # set default configuration
    volcenginesdkcore.Configuration.set_default(configuration)

    # use global default configuration
    api_instance = volcenginesdkvke.VKEApi()
    req_auto_scaling = volcenginesdkvke.AutoScalingForCreateNodePoolInput(
        desired_replicas=0,
        enabled=True,
        max_replicas=10,
        min_replicas=0,
        priority=10,
        subnet_policy="ZoneBalance",
    )
    req_labels0 = volcenginesdkvke.LabelForCreateNodePoolInput(
        key="label-key",
        value="label-value",
    )
    req_taints0 = volcenginesdkvke.TaintForCreateNodePoolInput(
        effect="NoSchedule",
        key="taint-key",
        value="taint-value",
    )
    req_kubernetes_config = volcenginesdkvke.KubernetesConfigForCreateNodePoolInput(
        cordon=False,
        labels=[req_labels0],
        taints=[req_taints0],
    )
    req_data_volumes0 = volcenginesdkvke.DataVolumeForCreateNodePoolInput(
        size=20,
        type="ESSD_PL0",
    )
    req_login = volcenginesdkvke.LoginForCreateNodePoolInput(
        password="UHdkMTIz***",
    )
    req_security = volcenginesdkvke.SecurityForCreateNodePoolInput(
        login=req_login,
        security_group_ids=["sg-2byy13cnsczy****"],
        security_strategies=["Hids"],
    )
    req_system_volume = volcenginesdkvke.SystemVolumeForCreateNodePoolInput(
        size=40,
        type="ESSD_PL0",
    )
    req_tags0 = volcenginesdkvke.TagForCreateNodePoolInput(
        key="key",
        value="value",
    )
    req_node_config = volcenginesdkvke.NodeConfigForCreateNodePoolInput(
        additional_container_storage_enabled=True,
        data_volumes=[req_data_volumes0],
        initialize_script="ZWNobyAidG******",
        instance_type_ids=["ecs.g1.xlarge"],
        name_prefix="name-prefix",
        security=req_security,
        subnet_ids=["subnet-3rf6vwbgkg****"],
        system_volume=req_system_volume,
        tags=[req_tags0],
    )
    req_tags1 = volcenginesdkvke.TagForCreateNodePoolInput(
        key="key",
        value="value",
    )
    create_node_pool_request = volcenginesdkvke.CreateNodePoolRequest(
        auto_scaling=req_auto_scaling,
        client_token="BC028527-33B9-4990-A633-84E9F9******",
        cluster_id="cc5silumrsfeq****",
        kubernetes_config=req_kubernetes_config,
        name="test-nodepool",
        node_config=req_node_config,
        tags=[req_tags1],
    )

    try:
        resp = api_instance.create_node_pool(create_node_pool_request)
        pprint(resp)
    except ApiException as e:
        print("Exception when calling api: %s\n" % e)
