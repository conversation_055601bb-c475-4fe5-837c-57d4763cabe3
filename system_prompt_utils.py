"""
System Prompt 工具模块
提供system prompt的读取、用户选择和请求体构建功能
"""

import os
import markdown
from bs4 import BeautifulSoup


def markdown_to_text(markdown_content):
    """
    将markdown格式转换为纯文本
    """
    # 将markdown转换为HTML
    html = markdown.markdown(markdown_content)
    # 使用BeautifulSoup提取纯文本
    soup = BeautifulSoup(html, 'html.parser')
    return soup.get_text()


def read_system_prompt(system_prompt_file="system_prompt.md"):
    """
    从system_prompt.md文件读取system prompt内容
    
    Args:
        system_prompt_file: system prompt文件路径，默认为"system_prompt.md"
    
    Returns:
        str: system prompt内容，如果读取失败返回None
    """
    if not os.path.exists(system_prompt_file):
        print(f"警告：System prompt文件 {system_prompt_file} 不存在！")
        return None
    
    try:
        with open(system_prompt_file, 'r', encoding='utf-8') as f:
            markdown_content = f.read().strip()
        
        if not markdown_content:
            print(f"警告：System prompt文件 {system_prompt_file} 为空！")
            return None
        
        # 将markdown格式转换为纯文本
        system_prompt_content = markdown_to_text(markdown_content)
        print(f"已从文件 {system_prompt_file} 读取system prompt")
        return system_prompt_content
        
    except Exception as e:
        print(f"读取system prompt文件时出错：{str(e)}")
        return None


def ask_user_for_system_prompt():
    """
    询问用户是否要加入system prompt
    
    Returns:
        bool: True表示用户选择加入system prompt，False表示不加入
    """
    print("\n是否要在请求中加入system prompt？")
    print("1. 是 - 加入system prompt")
    print("2. 否 - 不加入system prompt")
    
    while True:
        choice = input("请输入选择（1或2）：").strip()
        if choice == '1':
            return True
        elif choice == '2':
            return False
        else:
            print("输入无效，请输入1或2")


def build_messages_with_system_prompt(user_content, system_prompt_content=None, use_system_prompt=False):
    """
    构建包含system prompt的messages数组
    
    Args:
        user_content: 用户消息内容（可以是字符串或内容数组）
        system_prompt_content: system prompt内容
        use_system_prompt: 是否使用system prompt
    
    Returns:
        list: messages数组
    """
    messages = []
    
    # 如果需要使用system prompt且内容不为空，则添加system消息
    if use_system_prompt and system_prompt_content:
        messages.append({
            "content": system_prompt_content,
            "role": "system"
        })
    
    # 添加用户消息
    messages.append({
        "role": "user",
        "content": user_content
    })
    
    return messages


def get_system_prompt_config(interactive=True, system_prompt_file="system_prompt.md"):
    """
    获取system prompt配置
    
    Args:
        interactive: 是否进行交互式询问
        system_prompt_file: system prompt文件路径
    
    Returns:
        tuple: (use_system_prompt, system_prompt_content)
    """
    # 读取system prompt内容
    system_prompt_content = read_system_prompt(system_prompt_file)
    
    if not system_prompt_content:
        print("由于无法读取system prompt，将不使用system prompt功能")
        return False, None
    
    # 如果是交互模式，询问用户
    if interactive:
        use_system_prompt = ask_user_for_system_prompt()
    else:
        # 非交互模式默认不使用system prompt
        use_system_prompt = False
    
    return use_system_prompt, system_prompt_content
