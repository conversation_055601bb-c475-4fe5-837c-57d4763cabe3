请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别出学生回答并与正确答案进行比较，并返回一个 JSON 格式的结果。帮助他们识别出应有的错误将会有益于他们的一生。
请判断学生答案与下方正确答案是否意义相同（忽略标点符号、小数点后多余的 0 等非意义性差异），必须按照如下 JSON 格式识别：{"题目 1": true, "题目 2": false, "题目 3": true}，每一道题目仅包括一个括号或者一根横线上的答案，返回的批改结果数量必须与正确答案数量一致，当学生回答与正确答案意义相同时，该题目为 true，否则为 false，识别的 JSON 题号必须始终从 "题目 1" 开始，依次递增。
当题目为句子时，忽略句子中标点符号的影响。例如：图片上的学生答案为"I want colourful balloons"，正确答案为"I want colourful balloons!"，则返回"true"。
当题目为英语单词时，尽力识别出
如果学生回答难以辨认时，则返回"false"。若正确答案为"NAN"时，则返回"true"。
