# Example Code generated by Beijing Volcanoengine Technology.
from __future__ import print_function
import volcenginesdkcore
import volcenginesdkecs
from pprint import pprint
from volcenginesdkcore.rest import ApiException

if __name__ == '__main__':
    configuration = volcenginesdkcore.Configuration()
    configuration.ak = "Your AK"
    configuration.sk = "Your SK"
    configuration.region = "cn-beijing"
    # set default configuration
    volcenginesdkcore.Configuration.set_default(configuration)

    # use global default configuration
    api_instance = volcenginesdkecs.ECSApi()
    create_image_request = volcenginesdkecs.CreateImageRequest(
        image_name="image-1",
        instance_id="i-3teco18f1w5a71******",
    )
    
    try:
        resp = api_instance.create_image(create_image_request)
        pprint(resp)
    except ApiException as e:
        print("Exception when calling api: %s\n" % e)
