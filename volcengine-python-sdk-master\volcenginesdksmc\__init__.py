# coding: utf-8

# flake8: noqa

"""
    smc

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


from __future__ import absolute_import

# import apis into sdk package
from volcenginesdksmc.api.smc_api import SMCApi

# import models into sdk package
from volcenginesdksmc.models.associated_resource_for_describe_migration_jobs_output import AssociatedResourceForDescribeMigrationJobsOutput
from volcenginesdksmc.models.create_migration_job_request import CreateMigrationJobRequest
from volcenginesdksmc.models.create_migration_job_response import CreateMigrationJobResponse
from volcenginesdksmc.models.delete_migration_job_request import DeleteMigrationJobRequest
from volcenginesdksmc.models.delete_migration_job_response import DeleteMigrationJobResponse
from volcenginesdksmc.models.delete_migration_source_request import DeleteMigrationSourceRequest
from volcenginesdksmc.models.delete_migration_source_response import DeleteMigrationSourceResponse
from volcenginesdksmc.models.describe_migration_jobs_request import DescribeMigrationJobsRequest
from volcenginesdksmc.models.describe_migration_jobs_response import DescribeMigrationJobsResponse
from volcenginesdksmc.models.describe_migration_logs_request import DescribeMigrationLogsRequest
from volcenginesdksmc.models.describe_migration_logs_response import DescribeMigrationLogsResponse
from volcenginesdksmc.models.describe_migration_sources_request import DescribeMigrationSourcesRequest
from volcenginesdksmc.models.describe_migration_sources_response import DescribeMigrationSourcesResponse
from volcenginesdksmc.models.describe_migration_system_support_types_request import DescribeMigrationSystemSupportTypesRequest
from volcenginesdksmc.models.describe_migration_system_support_types_response import DescribeMigrationSystemSupportTypesResponse
from volcenginesdksmc.models.destination_config_for_create_migration_job_input import DestinationConfigForCreateMigrationJobInput
from volcenginesdksmc.models.destination_config_for_describe_migration_jobs_output import DestinationConfigForDescribeMigrationJobsOutput
from volcenginesdksmc.models.disk_config_for_create_migration_job_input import DiskConfigForCreateMigrationJobInput
from volcenginesdksmc.models.disk_info_for_describe_migration_sources_output import DiskInfoForDescribeMigrationSourcesOutput
from volcenginesdksmc.models.error_for_tag_resources_output import ErrorForTagResourcesOutput
from volcenginesdksmc.models.error_for_un_tag_resources_output import ErrorForUnTagResourcesOutput
from volcenginesdksmc.models.image_info_for_describe_migration_sources_output import ImageInfoForDescribeMigrationSourcesOutput
from volcenginesdksmc.models.instance_info_for_describe_migration_sources_output import InstanceInfoForDescribeMigrationSourcesOutput
from volcenginesdksmc.models.job_for_describe_migration_jobs_output import JobForDescribeMigrationJobsOutput
from volcenginesdksmc.models.last_migration_job_info_for_describe_migration_sources_output import LastMigrationJobInfoForDescribeMigrationSourcesOutput
from volcenginesdksmc.models.log_for_describe_migration_logs_output import LogForDescribeMigrationLogsOutput
from volcenginesdksmc.models.lv_info_for_describe_migration_sources_output import LvInfoForDescribeMigrationSourcesOutput
from volcenginesdksmc.models.lvm_info_for_describe_migration_sources_output import LvmInfoForDescribeMigrationSourcesOutput
from volcenginesdksmc.models.modify_migration_job_attribute_request import ModifyMigrationJobAttributeRequest
from volcenginesdksmc.models.modify_migration_job_attribute_response import ModifyMigrationJobAttributeResponse
from volcenginesdksmc.models.modify_migration_source_attribute_request import ModifyMigrationSourceAttributeRequest
from volcenginesdksmc.models.modify_migration_source_attribute_response import ModifyMigrationSourceAttributeResponse
from volcenginesdksmc.models.network_config_for_create_migration_job_input import NetworkConfigForCreateMigrationJobInput
from volcenginesdksmc.models.network_config_for_describe_migration_jobs_output import NetworkConfigForDescribeMigrationJobsOutput
from volcenginesdksmc.models.operation_detail_for_tag_resources_output import OperationDetailForTagResourcesOutput
from volcenginesdksmc.models.operation_detail_for_un_tag_resources_output import OperationDetailForUnTagResourcesOutput
from volcenginesdksmc.models.partition_for_describe_migration_sources_output import PartitionForDescribeMigrationSourcesOutput
from volcenginesdksmc.models.pv_info_for_describe_migration_sources_output import PvInfoForDescribeMigrationSourcesOutput
from volcenginesdksmc.models.set_incremental_sync_interval_request import SetIncrementalSyncIntervalRequest
from volcenginesdksmc.models.set_incremental_sync_interval_response import SetIncrementalSyncIntervalResponse
from volcenginesdksmc.models.source_for_describe_migration_sources_output import SourceForDescribeMigrationSourcesOutput
from volcenginesdksmc.models.source_image_info_for_describe_migration_jobs_output import SourceImageInfoForDescribeMigrationJobsOutput
from volcenginesdksmc.models.sync_config_for_create_migration_job_input import SyncConfigForCreateMigrationJobInput
from volcenginesdksmc.models.sync_config_for_describe_migration_jobs_output import SyncConfigForDescribeMigrationJobsOutput
from volcenginesdksmc.models.sync_type_support_status_for_describe_migration_sources_output import SyncTypeSupportStatusForDescribeMigrationSourcesOutput
from volcenginesdksmc.models.system_config_for_describe_migration_jobs_output import SystemConfigForDescribeMigrationJobsOutput
from volcenginesdksmc.models.system_info_for_describe_migration_sources_output import SystemInfoForDescribeMigrationSourcesOutput
from volcenginesdksmc.models.tag_filter_for_describe_migration_jobs_input import TagFilterForDescribeMigrationJobsInput
from volcenginesdksmc.models.tag_filter_for_describe_migration_sources_input import TagFilterForDescribeMigrationSourcesInput
from volcenginesdksmc.models.tag_for_create_migration_job_input import TagForCreateMigrationJobInput
from volcenginesdksmc.models.tag_for_describe_migration_jobs_output import TagForDescribeMigrationJobsOutput
from volcenginesdksmc.models.tag_for_describe_migration_sources_output import TagForDescribeMigrationSourcesOutput
from volcenginesdksmc.models.tag_for_tag_resources_input import TagForTagResourcesInput
from volcenginesdksmc.models.tag_resources_request import TagResourcesRequest
from volcenginesdksmc.models.tag_resources_response import TagResourcesResponse
from volcenginesdksmc.models.target_image_info_for_describe_migration_jobs_output import TargetImageInfoForDescribeMigrationJobsOutput
from volcenginesdksmc.models.target_os_info_for_describe_migration_system_support_types_output import TargetOSInfoForDescribeMigrationSystemSupportTypesOutput
from volcenginesdksmc.models.temporary_resource_for_describe_migration_jobs_output import TemporaryResourceForDescribeMigrationJobsOutput
from volcenginesdksmc.models.trigger_last_incremental_sync_request import TriggerLastIncrementalSyncRequest
from volcenginesdksmc.models.trigger_last_incremental_sync_response import TriggerLastIncrementalSyncResponse
from volcenginesdksmc.models.un_tag_resources_request import UnTagResourcesRequest
from volcenginesdksmc.models.un_tag_resources_response import UnTagResourcesResponse
from volcenginesdksmc.models.vg_info_for_describe_migration_sources_output import VgInfoForDescribeMigrationSourcesOutput
