{"batch_configs": [{"处理模式": 1, "模型ID": 1, "题型": 1, "图像文件夹": 1, "像素增强": "y", "灰度阀门": 180, "像素粘连": "n", "图像放大倍数": 1.5, "response_format": 1, "temperature": 0.7, "one_stage_test_prompt": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n以下是正确答案：\n<answer>\n{{answer_json}}\n</answer>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“false”。\n    - 若答题位置无书写内容，记录为“false”。\n    - 将识别到的学生答案和正确答案对比，若一致则输出“true”、若不一致则输出“false”\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、C、D，正确答案为：\n{\"题目1\": \"B\", \"题目2\": \"A\", \"题目3\": \"D\"}\n则输出：\n{\"题目1\": \"true\", \"题目2\": \"false\", \"题目3\": \"true\"}", "round2批改模式": 2}, {"处理模式": 2, "round2批改模式": 1, "模型ID": 2, "题型": 2, "图像文件夹": 1, "像素增强": "n", "像素粘连": "y", "图像放大倍数": 2, "response_format": 2, "top_p": 0.8, "test_prompt": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}", "test2_prompt": "你是一位严谨负责的资深阅卷老师，现在要为一位学生批改作业。你的任务是严格对照正确答案，比对学生的答案，然后给出每个题目的对错情况，最终以JSON格式输出结果，JSON的键为“题目1”“题目2”……（按题号顺序编号，且必须始终从“题目1”开始，依次递增），值只可能为true或者false。\n以下是学生的答案：\n<student_answers>\n{{STUDENT_ANSWERS}}\n</student_answers>\n以下是正确答案：\n<correct_answers>\n{{CORRECT_ANSWERS}}\n</correct_answers>\n比对规则如下：\n\n- 逐一对比学生答案和正确答案中相同位置的题目答案。\n- 如果两个答案相同，则该题目对应返回true；如果不同，则返回false。\n\n例如，若学生答案json为{\"题目1\": \"B\", \"题目2\": \"B\", \"题目3\": \"A\", \"题目4\": \"C\", \"题目5\": \"B\"}，正确答案为{\"题目1\": \"A\", \"题目2\": \"B\", \"题目3\": \"A\", \"题目4\": \"C\", \"题目5\": \"C\"}，则返回{\"题目1\": false, \"题目2\": true, \"题目3\": true, \"题目4\": true, \"题目5\": false}。"}, {"处理模式": 3, "模型ID": 3, "题型": 10, "图像文件夹": 1, "像素增强": "y", "灰度阀门": 200, "像素粘连": "n", "图像放大倍数": 1, "response_format": 1, "max_tokens": 4096, "test_prompt": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}", "test3_prompt": "你是一位严谨负责的资深阅卷老师，现在要为一位学生批改作业。你的任务是严格对照正确答案，比对学生的答案，然后给出每个题目的对错情况，最终以JSON格式输出结果，JSON的键为“题目1”“题目2”……（按题号顺序编号，且必须始终从“题目1”开始，依次递增），值只可能为true或者false。\n以下是学生的答案：\n<student_answers>\n{{STUDENT_ANSWERS}}\n</student_answers>\n以下是正确答案：\n<correct_answers>\n{{CORRECT_ANSWERS}}\n</correct_answers>\n比对规则如下：\n\n- 逐一对比学生答案和正确答案中相同位置的题目答案。\n- 如果两个答案相同，则该题目对应返回true；如果不同，则返回false。\n\n例如，若学生答案json为{\"题目1\": \"B\", \"题目2\": \"B\", \"题目3\": \"A\", \"题目4\": \"C\", \"题目5\": \"B\"}，正确答案为{\"题目1\": \"A\", \"题目2\": \"B\", \"题目3\": \"A\", \"题目4\": \"C\", \"题目5\": \"C\"}，则返回{\"题目1\": false, \"题目2\": true, \"题目3\": true, \"题目4\": true, \"题目5\": false}。", "round2批改模式": 2}, {"处理模式": 1, "模型ID": 4, "题型": 7, "图像文件夹": 1, "像素增强": "n", "像素粘连": "n", "图像放大倍数": 3, "response_format": 2, "temperature": 0.9, "top_p": 0.95, "one_stage_test_prompt": "你是一位严谨负责的资深数学老师，负责批改数学应用题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\\n以下是学生答题图片：\\n<student_answer_image>\\n{{STUDENT_ANSWER_IMAGE}}\\n</student_answer_image>\\n以下是正确答案：\\n<answer>\\n{{answer_json}}\\n</answer>\\n### 识别规则\\n#### 数学应用题\\n- **定位答题区域**：根据题号找到对应的答题位置。\\n- **答案判断**：\\n    - 仔细观察答题位置的解答过程和最终答案。\\n    - 重点关注最终答案的数值和单位。\\n    - 若答案正确则输出\\\"true\\\"，若答案错误或无答案则输出\\\"false\\\"。\\n### 输出格式\\n必须以JSON格式输出，键为\\\"题目1\\\"\\\"题目2\\\"……（按题号顺序编号），值为对应的判断结果。", "round2批改模式": 2}, {"处理模式": 2, "round2批改模式": 2, "模型ID": 1, "题型": 8, "图像文件夹": 1, "像素增强": "y", "灰度阀门": 160, "像素粘连": "y", "图像放大倍数": 1.2, "response_format": 1, "temperature": 0.6, "test_prompt": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}", "test2_prompt": "你是一位严谨负责的资深阅卷老师，现在要为一位学生批改作业。你的任务是严格对照正确答案，比对学生的答案，然后给出每个题目的对错情况，最终以JSON格式输出结果，JSON的键为“题目1”“题目2”……（按题号顺序编号，且必须始终从“题目1”开始，依次递增），值只可能为true或者false。\n以下是学生的答案：\n<student_answers>\n{{STUDENT_ANSWERS}}\n</student_answers>\n以下是正确答案：\n<correct_answers>\n{{CORRECT_ANSWERS}}\n</correct_answers>\n比对规则如下：\n\n- 逐一对比学生答案和正确答案中相同位置的题目答案。\n- 如果两个答案相同，则该题目对应返回true；如果不同，则返回false。\n\n例如，若学生答案json为{\"题目1\": \"B\", \"题目2\": \"B\", \"题目3\": \"A\", \"题目4\": \"C\", \"题目5\": \"B\"}，正确答案为{\"题目1\": \"A\", \"题目2\": \"B\", \"题目3\": \"A\", \"题目4\": \"C\", \"题目5\": \"C\"}，则返回{\"题目1\": false, \"题目2\": true, \"题目3\": true, \"题目4\": true, \"题目5\": false}。"}, {"处理模式": 3, "模型ID": 2, "题型": 11, "图像文件夹": 1, "像素增强": "n", "像素粘连": "n", "图像放大倍数": 2.5, "response_format": 2, "top_p": 0.7, "max_tokens": 6144, "test_prompt": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}", "test3_prompt": "你是一位严谨负责的资深阅卷老师，现在要为一位学生批改作业。你的任务是严格对照正确答案，比对学生的答案，然后给出每个题目的对错情况，最终以JSON格式输出结果，JSON的键为“题目1”“题目2”……（按题号顺序编号，且必须始终从“题目1”开始，依次递增），值只可能为true或者false。\n以下是学生的答案：\n<student_answers>\n{{STUDENT_ANSWERS}}\n</student_answers>\n以下是正确答案：\n<correct_answers>\n{{CORRECT_ANSWERS}}\n</correct_answers>\n比对规则如下：\n\n- 逐一对比学生答案和正确答案中相同位置的题目答案。\n- 如果两个答案相同，则该题目对应返回true；如果不同，则返回false。\n\n例如，若学生答案json为{\"题目1\": \"B\", \"题目2\": \"B\", \"题目3\": \"A\", \"题目4\": \"C\", \"题目5\": \"B\"}，正确答案为{\"题目1\": \"A\", \"题目2\": \"B\", \"题目3\": \"A\", \"题目4\": \"C\", \"题目5\": \"C\"}，则返回{\"题目1\": false, \"题目2\": true, \"题目3\": true, \"题目4\": true, \"题目5\": false}。", "round2批改模式": 2}, {"处理模式": 1, "模型ID": 3, "题型": 9, "图像文件夹": 1, "像素增强": "y", "灰度阀门": 220, "像素粘连": "n", "图像放大倍数": 1.8, "response_format": 1, "temperature": 0.5, "top_p": 0.85, "max_tokens": 2048, "one_stage_test_prompt": "你是一位严谨负责的资深数学老师，负责批改简单的四则运算题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\\n以下是学生答题图片：\\n<student_answer_image>\\n{{STUDENT_ANSWER_IMAGE}}\\n</student_answer_image>\\n以下是正确答案：\\n<answer>\\n{{answer_json}}\\n</answer>\\n### 识别规则\\n#### 四则运算题\\n- **定位答题区域**：根据题号找到对应的答题位置。\\n- **答案判断**：\\n    - 仔细观察答题位置的计算结果。\\n    - 检查数值计算是否正确。\\n    - 若答案正确则输出\\\"true\\\"，若答案错误或无答案则输出\\\"false\\\"。\\n### 输出格式\\n必须以JSON格式输出，键为\\\"题目1\\\"\\\"题目2\\\"……（按题号顺序编号），值为对应的判断结果。", "round2批改模式": 2}, {"处理模式": 2, "round2批改模式": 1, "模型ID": 4, "题型": 13, "图像文件夹": 1, "像素增强": "n", "像素粘连": "y", "图像放大倍数": 1, "response_format": 2, "temperature": 1.0, "top_p": 0.9, "max_tokens": 8192, "test_prompt": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为“错误”。\n    - 若答题位置无书写内容，记录为“NAN”。\n### 输出格式\n必须以JSON格式输出，键为“题目1”“题目2”……（按题号顺序编号，不管图中题号从几开始，必须始终从“题目1”开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、NAN、D，则输出：\n{\"题目1\": \"B\", \"题目2\": \"NAN\", \"题目3\": \"D\"}", "test2_prompt": "你是一位严谨负责的资深阅卷老师，现在要为一位学生批改作业。你的任务是严格对照正确答案，比对学生的答案，然后给出每个题目的对错情况，最终以JSON格式输出结果，JSON的键为“题目1”“题目2”……（按题号顺序编号，且必须始终从“题目1”开始，依次递增），值只可能为true或者false。\n以下是学生的答案：\n<student_answers>\n{{STUDENT_ANSWERS}}\n</student_answers>\n以下是正确答案：\n<correct_answers>\n{{CORRECT_ANSWERS}}\n</correct_answers>\n比对规则如下：\n\n- 逐一对比学生答案和正确答案中相同位置的题目答案。\n- 如果两个答案相同，则该题目对应返回true；如果不同，则返回false。\n\n例如，若学生答案json为{\"题目1\": \"B\", \"题目2\": \"B\", \"题目3\": \"A\", \"题目4\": \"C\", \"题目5\": \"B\"}，正确答案为{\"题目1\": \"A\", \"题目2\": \"B\", \"题目3\": \"A\", \"题目4\": \"C\", \"题目5\": \"C\"}，则返回{\"题目1\": false, \"题目2\": true, \"题目3\": true, \"题目4\": true, \"题目5\": false}。"}]}