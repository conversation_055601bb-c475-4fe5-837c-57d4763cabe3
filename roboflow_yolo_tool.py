#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Roboflow YOLO标注数据处理工具

功能说明：
1. 让用户选择题型（和其他脚本一致）
2. 让用户选择图片处理方式（白噪点、高斯模糊、半透明蒙版、仅画框）
3. 让用户选择框的粗细（细、中等、粗、很粗）
4. 让用户选择框的颜色（黑色、黄色、红色、绿色）
5. 从相应题型的 train 文件夹中读取Roboflow标注的数据集
6. 解析YOLO格式的标注文件（.txt），将归一化坐标转换为像素坐标
7. 在图片上绘制边界框并应用选择的处理方式和框样式
8. 将处理后的图片保存到roboflow_yolo_result文件夹中
9. 生成包含处理结果的summary.md文档

使用方法：
1. 安装依赖：pip install opencv-python numpy pypinyin
2. 运行脚本：python roboflow_yolo_tool.py
3. 选择题型、图片处理方式、框粗细和框颜色
4. 脚本会自动处理相应题型下train文件夹中的所有标注数据

注意：
- 标注文件格式为YOLO格式：class_id center_x center_y width height（归一化坐标）
- 图片和标注文件需要在train/images和train/labels文件夹中
- 框粗细选项：细(1像素)、中等(2像素)、粗(3像素)、很粗(5像素)
- 框颜色选项：黑色、黄色、红色、绿色
"""

import os
import cv2
import numpy as np
import datetime
import time
import random
from pypinyin import pinyin, Style

def chinese_to_pinyin(chinese_text):
    """将中文转换为拼音（无声调）"""
    pinyin_list = pinyin(chinese_text, style=Style.NORMAL)
    return ''.join([p[0] for p in pinyin_list])

def get_question_type():
    """获取用户输入的题型并转换为拼音路径"""
    question_types = {
        "1": "涂卡选择题",
        "2": "涂卡判断题", 
        "3": "连线题",
        "4": "图表题",
        "5": "翻译题",
        "6": "画图题",
        "7": "数学应用题",
        "8": "数学计算题",
        "9": "简单的四则运算",
        "10": "填空题",
        "11": "判断题",
        "12": "多选题",
        "13": "单选题"
    }

    print("请选择题型：")
    for key, value in question_types.items():
        print(f"{key}. {value}")

    while True:
        user_input = input("请输入题型编号（1-13）：").strip()
        if user_input in question_types:
            question_type = question_types[user_input]
            break
        else:
            print("输入无效，请输入 1-13 的数字")

    pinyin_name = chinese_to_pinyin(question_type)
    print(f"选择的题型：{question_type}")
    print(f"对应的拼音路径：{pinyin_name}")
    return question_type, pinyin_name

def get_image_processing_type():
    """获取用户选择的图片处理方式"""
    processing_types = {
        "1": "白噪点",
        "2": "高斯模糊",
        "3": "半透明蒙版",
        "4": "仅画框"
    }

    print("\n请选择图片处理方式：")
    print("1. 白噪点：在框外随机添加80%密度的白色像素点")
    print("2. 高斯模糊：对框外背景轻度模糊（radius=5-10）")
    print("3. 半透明蒙版：将框外区域覆盖50%透明度的黑色图层")
    print("4. 仅画框：除了画框外，其他区域不做任何处理")

    while True:
        user_input = input("请输入处理方式编号（1-4）：").strip()
        if user_input in processing_types:
            processing_type = processing_types[user_input]
            print(f"选择的处理方式：{processing_type}")
            return user_input, processing_type
        else:
            print("输入无效，请输入 1-4 的数字")

def get_box_thickness():
    """获取用户选择的框粗细"""
    thickness_options = {
        "1": ("细", 1),
        "2": ("中等", 2),
        "3": ("粗", 3),
        "4": ("很粗", 5)
    }

    print("\n请选择框的粗细：")
    print("1. 细 (1像素)")
    print("2. 中等 (2像素)")
    print("3. 粗 (3像素)")
    print("4. 很粗 (5像素)")

    while True:
        user_input = input("请输入粗细编号（1-4）：").strip()
        if user_input in thickness_options:
            thickness_name, thickness_value = thickness_options[user_input]
            print(f"选择的框粗细：{thickness_name} ({thickness_value}像素)")
            return thickness_value, thickness_name
        else:
            print("输入无效，请输入 1-4 的数字")

def get_box_color():
    """获取用户选择的框颜色"""
    color_options = {
        "1": ("黑色", (0, 0, 0)),
        "2": ("黄色", (0, 255, 255)),
        "3": ("红色", (0, 0, 255)),
        "4": ("绿色", (0, 255, 0))
    }

    print("\n请选择框的颜色：")
    print("1. 黑色")
    print("2. 黄色")
    print("3. 红色")
    print("4. 绿色")

    while True:
        user_input = input("请输入颜色编号（1-4）：").strip()
        if user_input in color_options:
            color_name, color_value = color_options[user_input]
            print(f"选择的框颜色：{color_name}")
            return color_value, color_name
        else:
            print("输入无效，请输入 1-4 的数字")

def parse_yolo_annotation(annotation_path, image_width, image_height):
    """
    解析YOLO格式的标注文件，将归一化坐标转换为像素坐标
    
    YOLO格式：class_id center_x center_y width height（归一化坐标，范围0-1）
    返回：[(x_min, y_min, x_max, y_max, class_id), ...]
    """
    boxes = []
    
    if not os.path.exists(annotation_path):
        return boxes
    
    try:
        with open(annotation_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            parts = line.split()
            if len(parts) != 5:
                continue
            
            try:
                class_id = int(parts[0])
                center_x = float(parts[1])
                center_y = float(parts[2])
                width = float(parts[3])
                height = float(parts[4])
                
                # 转换为像素坐标
                center_x_pixel = center_x * image_width
                center_y_pixel = center_y * image_height
                width_pixel = width * image_width
                height_pixel = height * image_height
                
                # 计算边界框坐标
                x_min = int(center_x_pixel - width_pixel / 2)
                y_min = int(center_y_pixel - height_pixel / 2)
                x_max = int(center_x_pixel + width_pixel / 2)
                y_max = int(center_y_pixel + height_pixel / 2)
                
                # 确保坐标在图像范围内
                x_min = max(0, min(x_min, image_width - 1))
                y_min = max(0, min(y_min, image_height - 1))
                x_max = max(x_min, min(x_max, image_width - 1))
                y_max = max(y_min, min(y_max, image_height - 1))
                
                boxes.append((x_min, y_min, x_max, y_max, class_id))
                
            except ValueError as e:
                print(f"解析标注行时出错: {line}, 错误: {e}")
                continue
    
    except Exception as e:
        print(f"读取标注文件时出错: {annotation_path}, 错误: {e}")
    
    return boxes

def create_bbox_mask(image_shape, boxes):
    """创建边界框区域的掩码"""
    h, w = image_shape[:2]
    mask = np.zeros((h, w), dtype=bool)
    
    for x_min, y_min, x_max, y_max, _ in boxes:
        x_min = max(0, min(x_min, w-1))
        y_min = max(0, min(y_min, h-1))
        x_max = max(x_min, min(x_max, w-1))
        y_max = max(y_min, min(y_max, h-1))
        
        if x_max > x_min and y_max > y_min:
            mask[y_min:y_max+1, x_min:x_max+1] = True
    
    return mask

def apply_white_noise_processing(image, bbox_mask):
    """应用白噪点处理"""
    processed_image = image.copy()
    outside_mask = ~bbox_mask
    outside_positions = np.where(outside_mask)
    total_outside_pixels = len(outside_positions[0])
    
    if total_outside_pixels > 0:
        noise_density = 0.8
        num_noise_pixels = int(total_outside_pixels * noise_density)
        indices = random.sample(range(total_outside_pixels), num_noise_pixels)
        noise_y = outside_positions[0][indices]
        noise_x = outside_positions[1][indices]
        processed_image[noise_y, noise_x] = [255, 255, 255]
    
    return processed_image

def apply_gaussian_blur_processing(image, bbox_mask):
    """应用高斯模糊处理"""
    processed_image = image.copy()
    blur_radius = random.randint(5, 10)
    kernel_size = blur_radius * 2 + 1
    blurred_image = cv2.GaussianBlur(image, (kernel_size, kernel_size), 0)
    outside_mask = ~bbox_mask
    processed_image[outside_mask] = blurred_image[outside_mask]
    return processed_image

def apply_transparent_mask_processing(image, bbox_mask):
    """应用半透明蒙版处理"""
    processed_image = image.copy().astype(np.float32)
    alpha = 0.5
    mask_color = 0
    outside_mask = ~bbox_mask
    processed_image[outside_mask] = processed_image[outside_mask] * (1 - alpha) + mask_color * alpha
    return processed_image.astype(np.uint8)

def process_image_with_roboflow_boxes(image_path, boxes, output_path, processing_type="4", box_color=(0, 0, 0), box_thickness=2):
    """处理图片并应用选择的处理方式"""
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError(f"无法读取图片: {image_path}")

    bbox_mask = create_bbox_mask(image.shape, boxes)

    # 根据处理类型应用不同的图像处理
    if processing_type == "1":
        processed_image = apply_white_noise_processing(image, bbox_mask)
    elif processing_type == "2":
        processed_image = apply_gaussian_blur_processing(image, bbox_mask)
    elif processing_type == "3":
        processed_image = apply_transparent_mask_processing(image, bbox_mask)
    else:
        processed_image = image.copy()

    # 绘制边界框（使用用户选择的颜色和粗细）
    for i, (x_min, y_min, x_max, y_max, class_id) in enumerate(boxes):
        cv2.rectangle(processed_image, (x_min, y_min), (x_max, y_max), box_color, box_thickness)

    cv2.imwrite(output_path, processed_image)
    return True

def roboflow_yolo_tool():
    """主函数"""
    print("=== Roboflow YOLO标注数据处理工具 ===")

    # 获取题型和处理方式
    question_type, pinyin_name = get_question_type()
    processing_type_id, processing_type = get_image_processing_type()

    # 获取框的样式设置
    box_thickness, thickness_name = get_box_thickness()
    box_color, color_name = get_box_color()

    # 设置路径
    types_dir = "types"
    question_dir = os.path.join(types_dir, pinyin_name)
    train_dir = os.path.join(question_dir, "train")
    images_dir = os.path.join(train_dir, "images")
    labels_dir = os.path.join(train_dir, "labels")

    # 检查目录是否存在
    if not os.path.exists(train_dir):
        print(f"错误：找不到训练数据目录 {train_dir}")
        return

    if not os.path.exists(images_dir):
        print(f"错误：找不到图片目录 {images_dir}")
        return

    if not os.path.exists(labels_dir):
        print(f"错误：找不到标注目录 {labels_dir}")
        return

    # 获取图片文件
    image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp']
    image_files = []
    for filename in os.listdir(images_dir):
        if any(filename.lower().endswith(ext) for ext in image_extensions):
            image_files.append(filename)

    image_files.sort()
    print(f"找到 {len(image_files)} 张图片")

    # 创建输出目录
    timestamp = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    output_dir = os.path.join(question_dir, "roboflow_yolo_result", f"images_{timestamp}")
    os.makedirs(output_dir, exist_ok=True)

    print(f"\n开始处理，输出目录: {output_dir}")

    results = []

    for i, image_filename in enumerate(image_files):
        print(f"\n处理第 {i+1}/{len(image_files)} 张图片: {image_filename}")

        # 构建文件路径
        image_path = os.path.join(images_dir, image_filename)

        # 构建对应的标注文件路径（去掉扩展名，加上.txt）
        base_name = os.path.splitext(image_filename)[0]
        annotation_path = os.path.join(labels_dir, f"{base_name}.txt")

        try:
            # 读取图片获取尺寸
            image = cv2.imread(image_path)
            if image is None:
                result = {
                    'success': False,
                    'filename': image_filename,
                    'error': '无法读取图片'
                }
                print(f"✗ 错误: 无法读取图片 {image_filename}")
                results.append(result)
                continue

            image_height, image_width = image.shape[:2]

            # 解析标注文件
            start_time = time.time()
            boxes = parse_yolo_annotation(annotation_path, image_width, image_height)

            # 生成输出文件名
            bbox_filename = f"{os.path.splitext(image_filename)[0]}_with_roboflow_yolo{os.path.splitext(image_filename)[1]}"
            bbox_output_path = os.path.join(output_dir, bbox_filename)

            # 处理图片（即使没有标注框也要处理）
            process_image_with_roboflow_boxes(image_path, boxes, bbox_output_path, processing_type_id, box_color, box_thickness)
            end_time = time.time()

            result = {
                'success': True,
                'filename': image_filename,
                'bbox_filename': bbox_filename,
                'boxes': boxes,
                'box_count': len(boxes),
                'processing_time': end_time - start_time,
                'image_size': f"{image_width}x{image_height}"
            }

            if boxes:
                print(f"✓ 成功处理: {image_filename}, 检测到 {len(boxes)} 个标注框")
            else:
                print(f"✓ 成功处理: {image_filename}, 无标注框（空标注文件）")

        except Exception as e:
            result = {
                'success': False,
                'filename': image_filename,
                'error': str(e)
            }
            print(f"✗ 处理失败: {image_filename}, 错误: {e}")

        results.append(result)

    # 生成summary.md
    generate_summary(results, output_dir, question_type, processing_type, thickness_name, color_name)

def generate_summary(results, output_dir, question_type, processing_type, thickness_name, color_name):
    """生成处理结果的summary.md文档"""
    summary_path = os.path.join(output_dir, "summary.md")

    with open(summary_path, 'w', encoding='utf-8') as f:
        f.write(f"# Roboflow YOLO标注数据处理结果\n\n")
        f.write(f"**题型**: {question_type}\n")
        f.write(f"**处理方式**: {processing_type}\n")
        f.write(f"**框粗细**: {thickness_name}\n")
        f.write(f"**框颜色**: {color_name}\n")
        f.write(f"**处理时间**: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

        success_count = sum(1 for r in results if r['success'])
        total_boxes = sum(r.get('box_count', 0) for r in results if r['success'])

        f.write(f"**处理统计**:\n")
        f.write(f"- 总图片数: {len(results)}\n")
        f.write(f"- 成功处理: {success_count}\n")
        f.write(f"- 总标注框数: {total_boxes}\n\n")

        f.write("## 处理结果\n\n")

        for i, result in enumerate(results, 1):
            f.write(f"### 第 {i} 张图片: {result['filename']}\n\n")

            if result['success']:
                f.write(f"![{result['bbox_filename']}]({result['bbox_filename']})\n\n")
                f.write("**Roboflow YOLO标注内容:**\n")
                f.write(f"- 图片尺寸: {result.get('image_size', '未知')}\n")
                f.write(f"- 标注框数量: {result['box_count']}\n")
                f.write(f"- 处理时间: {result.get('processing_time', 0):.2f}秒\n")

                if result['boxes']:
                    f.write("- 标注框坐标 (x_min, y_min, x_max, y_max, class_id):\n")
                    for j, (x_min, y_min, x_max, y_max, class_id) in enumerate(result['boxes'], 1):
                        width = x_max - x_min
                        height = y_max - y_min
                        area = width * height
                        f.write(f"  - 区域{j}: ({x_min}, {y_min}, {x_max}, {y_max}, 类别{class_id}) - 尺寸: {width}×{height}, 面积: {area}\n")
            else:
                f.write(f"**错误**: {result.get('error', '未知错误')}\n")

            f.write("\n---\n\n")

    print(f"\n✓ 处理完成！summary.md 已保存至: {summary_path}")

if __name__ == "__main__":
    roboflow_yolo_tool()
