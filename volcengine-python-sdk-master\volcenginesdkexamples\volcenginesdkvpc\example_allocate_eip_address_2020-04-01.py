# Example Code generated by Beijing Volcanoengine Technology.
from __future__ import print_function
import volcenginesdkcore
import volcenginesdkvpc
from pprint import pprint
from volcenginesdkcore.rest import ApiException

if __name__ == '__main__':
    configuration = volcenginesdkcore.Configuration()
    configuration.ak = "Your AK"
    configuration.sk = "Your SK"
    configuration.region = "cn-beijing"
    # set default configuration
    volcenginesdkcore.Configuration.set_default(configuration)

    # use global default configuration
    api_instance = volcenginesdkvpc.VPCApi()
    allocate_eip_address_request = volcenginesdkvpc.AllocateEipAddressRequest(
        bandwidth=10,
        billing_type=2,
        isp="BGP",
        name="eip-1",
    )
    
    try:
        resp = api_instance.allocate_eip_address(allocate_eip_address_request)
        pprint(resp)
    except ApiException as e:
        print("Exception when calling api: %s\n" % e)
