# Example Code generated by Beijing Volcanoengine Technology.
from __future__ import print_function
import volcenginesdkcore
import volcenginesdknatgateway
from pprint import pprint
from volcenginesdkcore.rest import ApiException

if __name__ == '__main__':
    configuration = volcenginesdkcore.Configuration()
    configuration.ak = "Your AK"
    configuration.sk = "Your SK"
    configuration.region = "cn-beijing"
    # set default configuration
    volcenginesdkcore.Configuration.set_default(configuration)

    # use global default configuration
    api_instance = volcenginesdknatgateway.NATGATEWAYApi()
    modify_snat_entry_attributes_request = volcenginesdknatgateway.ModifySnatEntryAttributesRequest(
        eip_id="eip-2feaac9wtccn459gp67qe****",
        snat_entry_id="snat-2fedi096gdiww59gp680r****",
    )
    
    try:
        resp = api_instance.modify_snat_entry_attributes(modify_snat_entry_attributes_request)
        pprint(resp)
    except ApiException as e:
        print("Exception when calling api: %s\n" % e)
