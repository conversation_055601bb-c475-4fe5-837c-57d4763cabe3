#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YOLO手写体检测脚本

功能说明：
1. 让用户选择题型（和grounding_test.py一致）
2. 让用户选择图片处理方式：
   - 白噪点：在框外添加80%密度的白色像素点（模拟马赛克）
   - 高斯模糊：对框外背景轻度模糊（radius=5-10），保留轮廓但弱化细节
   - 半透明蒙版：将框外区域覆盖50%透明度的黑色图层（类似手机截图标注效果）
   - 仅画框：除了画框外，其他区域不做任何处理
3. 从相应题型的 images 文件夹中读取所有图片
4. 使用YOLO模型检测图片中的文本/手写体区域
5. 在图片上绘制边界框并应用选择的处理方式
6. 将处理后的图片保存到YOLO_result文件夹中
7. 生成包含处理结果的summary.md文档

使用方法：
1. 安装依赖：pip install ultralytics opencv-python pypinyin
2. 运行脚本：python yolo_test.py
3. 选择题型和YOLO模型
4. 选择图片处理方式
5. 脚本会自动处理相应题型下的所有图片
"""

import os
import cv2
import datetime
import time  # 新增：用于记录处理时间
import numpy as np  # 新增：用于图像处理
import random  # 新增：用于白噪点生成
from pypinyin import pinyin, Style

def chinese_to_pinyin(chinese_text):
    """将中文转换为拼音（无声调）"""
    pinyin_list = pinyin(chinese_text, style=Style.NORMAL)
    return ''.join([p[0] for p in pinyin_list])

def get_question_type():
    """获取用户输入的题型并转换为拼音路径（和grounding_test.py一致）"""
    question_types = {
        "1": "涂卡选择题",
        "2": "涂卡判断题", 
        "3": "连线题",
        "4": "图表题",
        "5": "翻译题",
        "6": "画图题",
        "7": "数学应用题",
        "8": "数学计算题",
        "9": "简单的四则运算",
        "10": "填空题",
        "11": "判断题",
        "12": "多选题",
        "13": "单选题"
    }

    print("请选择题型：")
    for key, value in question_types.items():
        print(f"{key}. {value}")

    while True:
        user_input = input("请输入题型编号（1-13）或直接输入中文：").strip()

        # 检查数字输入
        if user_input in question_types:
            question_type = question_types[user_input]
            break
        # 检查中文输入
        elif user_input in question_types.values():
            question_type = user_input
            break
        else:
            print("输入无效，请输入 1-13 的数字或直接输入中文题型名称")

    # 转换为拼音
    pinyin_name = chinese_to_pinyin(question_type)
    print(f"选择的题型：{question_type}")
    print(f"对应的拼音路径：{pinyin_name}")

    return question_type, pinyin_name

def get_image_processing_type():
    """获取用户选择的图片处理方式"""
    processing_types = {
        "1": "白噪点",
        "2": "高斯模糊",
        "3": "半透明蒙版",
        "4": "仅画框"
    }

    print("\n请选择图片处理方式：")
    print("1. 白噪点：在框外添加80%密度的白色像素点（模拟马赛克）")
    print("2. 高斯模糊：对框外背景轻度模糊（radius=5-10），保留轮廓但弱化细节")
    print("3. 半透明蒙版：将框外区域覆盖50%透明度的黑色图层（类似手机截图标注效果）")
    print("4. 仅画框：除了画框外，其他区域不做任何处理")

    while True:
        user_input = input("请输入处理方式编号（1-4）：").strip()

        if user_input in processing_types:
            processing_type = processing_types[user_input]
            print(f"选择的处理方式：{processing_type}")
            return user_input, processing_type
        else:
            print("输入无效，请输入 1-4 的数字")

def get_yolo_model():
    """让用户选择YOLO模型"""
    models = {
        "1": ("yolov8n.pt", "YOLOv8 Nano（最快，准确率较低）"),
        "2": ("yolov8s.pt", "YOLOv8 Small（平衡速度和准确率）"),
        "3": ("yolov8m.pt", "YOLOv8 Medium（较高准确率）"),
        "4": ("yolov8l.pt", "YOLOv8 Large（高准确率，较慢）"),
        "5": ("yolov8x.pt", "YOLOv8 Extra Large（最高准确率，最慢）")
    }
    
    print("\n请选择YOLO模型：")
    for key, (model_name, description) in models.items():
        print(f"{key}. {description}")
    
    while True:
        user_input = input("请输入模型编号（1-5）：").strip()
        if user_input in models:
            model_file, description = models[user_input]
            break
        else:
            print("输入无效，请输入 1-5 的数字")
    
    print(f"选择的模型：{description}")
    return model_file, description

def get_image_files(images_dir):
    """获取images文件夹中的所有图片文件，按文件名字典序排序"""
    image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp']
    image_files = []
    if os.path.exists(images_dir):
        for filename in os.listdir(images_dir):
            if any(filename.lower().endswith(ext) for ext in image_extensions):
                image_files.append(os.path.join(images_dir, filename))
    # 按文件名字典序排序
    return sorted(image_files)

def detect_with_yolo(image_path, model, confidence_threshold=0.25):
    """
    使用YOLO模型检测图片中的对象
    返回检测到的边界框坐标列表
    """
    try:
        # 运行YOLO检测
        results = model(image_path, conf=confidence_threshold)
        
        # 提取边界框
        boxes = []
        if results and len(results) > 0:
            result = results[0]  # 取第一个结果
            
            # 获取检测框
            if result.boxes is not None:
                for box in result.boxes:
                    # 获取边界框坐标 (x1, y1, x2, y2)
                    x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                    confidence = box.conf[0].cpu().numpy()
                    class_id = int(box.cls[0].cpu().numpy())
                    
                    # 转换为整数坐标
                    x1, y1, x2, y2 = int(x1), int(y1), int(x2), int(y2)
                    
                    boxes.append({
                        'coords': (x1, y1, x2, y2),
                        'confidence': float(confidence),
                        'class_id': class_id,
                        'class_name': model.names[class_id] if hasattr(model, 'names') else f'class_{class_id}'
                    })
        
        return boxes
        
    except Exception as e:
        print(f"YOLO检测出错: {str(e)}")
        return []

def create_bbox_mask(image_shape, detections):
    """创建边界框区域的掩码，返回框内为True，框外为False的掩码"""
    h, w = image_shape[:2]
    mask = np.zeros((h, w), dtype=bool)

    for detection in detections:
        x1, y1, x2, y2 = detection['coords']

        # 确保坐标在图像范围内，并且max坐标不能小于min坐标
        x1 = max(0, min(int(x1), w-1))
        y1 = max(0, min(int(y1), h-1))
        x2 = max(x1, min(int(x2), w-1))
        y2 = max(y1, min(int(y2), h-1))

        # 在掩码中标记边界框区域（确保有效的切片范围）
        if x2 > x1 and y2 > y1:
            mask[y1:y2+1, x1:x2+1] = True

    return mask

def apply_white_noise_processing(image, bbox_mask):
    """应用白噪点处理：在框外区域添加80%密度的白色像素点"""
    processed_image = image.copy()

    # 创建框外区域掩码
    outside_mask = ~bbox_mask

    # 调试信息
    total_pixels = bbox_mask.size
    bbox_pixels = np.sum(bbox_mask)
    outside_pixels = np.sum(outside_mask)
    print(f"  [调试] 总像素: {total_pixels}, 框内像素: {bbox_pixels}, 框外像素: {outside_pixels}")

    # 检查原图框内的白色像素数量
    original_bbox_region = image[bbox_mask]
    original_white_in_bbox = np.sum(np.all(original_bbox_region == [255, 255, 255], axis=1))
    print(f"  [调试] 原图框内白色像素: {original_white_in_bbox}")

    # 获取框外区域的像素位置
    outside_positions = np.where(outside_mask)
    total_outside_pixels = len(outside_positions[0])

    if total_outside_pixels > 0:
        # 固定选择80%的框外像素添加白噪点
        noise_density = 0.8
        num_noise_pixels = int(total_outside_pixels * noise_density)

        print(f"  [调试] 将添加 {num_noise_pixels} 个白噪点到框外区域")

        # 随机选择像素位置
        indices = random.sample(range(total_outside_pixels), num_noise_pixels)
        noise_y = outside_positions[0][indices]
        noise_x = outside_positions[1][indices]

        # 添加白色噪点（只在框外区域）
        processed_image[noise_y, noise_x] = [255, 255, 255]

        # 验证：检查处理后框内的白色像素数量
        processed_bbox_region = processed_image[bbox_mask]
        processed_white_in_bbox = np.sum(np.all(processed_bbox_region == [255, 255, 255], axis=1))

        if processed_white_in_bbox > original_white_in_bbox:
            print(f"  [警告] 框内新增了 {processed_white_in_bbox - original_white_in_bbox} 个白色像素！")
        elif processed_white_in_bbox == original_white_in_bbox:
            print(f"  [正常] 框内白色像素数量未变化，白噪点只在框外")
        else:
            print(f"  [异常] 框内白色像素减少了？这不应该发生")

    return processed_image

def apply_gaussian_blur_processing(image, bbox_mask):
    """应用高斯模糊处理：对框外背景轻度模糊（radius=5-10）"""
    processed_image = image.copy()

    # 随机选择模糊半径
    blur_radius = random.randint(5, 10)
    kernel_size = blur_radius * 2 + 1

    # 对整个图像应用高斯模糊
    blurred_image = cv2.GaussianBlur(image, (kernel_size, kernel_size), 0)

    # 只在框外区域应用模糊效果
    outside_mask = ~bbox_mask
    processed_image[outside_mask] = blurred_image[outside_mask]

    return processed_image

def apply_transparent_mask_processing(image, bbox_mask):
    """应用半透明蒙版处理：将框外区域覆盖50%透明度的黑色图层"""
    processed_image = image.copy().astype(np.float32)

    # 固定透明度和蒙版颜色
    alpha = 0.5  # 50%透明度
    mask_color = 0  # 黑色

    # 创建框外区域掩码
    outside_mask = ~bbox_mask

    # 应用半透明蒙版
    processed_image[outside_mask] = processed_image[outside_mask] * (1 - alpha) + mask_color * alpha

    return processed_image.astype(np.uint8)

def draw_yolo_boxes_on_image(image_path, detections, output_path, processing_type="4"):
    """在图片上绘制YOLO检测的边界框并根据处理类型应用不同的图像处理效果"""
    # 读取原图
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError(f"无法读取图片: {image_path}")

    # 创建边界框掩码
    bbox_mask = create_bbox_mask(image.shape, detections)

    # 根据处理类型应用不同的图像处理
    if processing_type == "1":  # 白噪点
        processed_image = apply_white_noise_processing(image, bbox_mask)
    elif processing_type == "2":  # 高斯模糊
        processed_image = apply_gaussian_blur_processing(image, bbox_mask)
    elif processing_type == "3":  # 半透明蒙版
        processed_image = apply_transparent_mask_processing(image, bbox_mask)
    else:  # 仅画框（默认）
        processed_image = image.copy()

    # 定义不同颜色用于区分不同的边界框
    colors = [
        (0, 0, 255),    # 红色
        (0, 255, 0),    # 绿色
        (255, 0, 0),    # 蓝色
        (0, 255, 255),  # 黄色
        (255, 0, 255),  # 紫色
        (255, 255, 0),  # 青色
        (128, 0, 128),  # 紫色
        (255, 165, 0),  # 橙色
    ]

    # 绘制所有边界框
    for i, detection in enumerate(detections):
        x1, y1, x2, y2 = detection['coords']
        confidence = detection['confidence']
        class_name = detection['class_name']

        # 选择颜色
        color = colors[i % len(colors)]

        # 绘制边界框
        cv2.rectangle(processed_image, (x1, y1), (x2, y2), color, 2)

        # 添加标签
        label = f"{class_name}: {confidence:.2f}"
        label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.6, 2)[0]

        # 绘制标签背景
        cv2.rectangle(processed_image, (x1, y1 - label_size[1] - 10),
                     (x1 + label_size[0], y1), color, -1)

        # 绘制标签文字
        cv2.putText(processed_image, label, (x1, y1 - 5),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)

    # 保存结果图片
    cv2.imwrite(output_path, processed_image)
    return True

def process_single_image(image_path, output_dir, model, processing_type="4"):
    """处理单张图片"""
    filename = os.path.basename(image_path)
    print(f"正在处理: {filename}")

    try:
        # 记录开始时间
        start_time = time.time()

        # 使用YOLO检测
        detections = detect_with_yolo(image_path, model)

        # 记录结束时间并计算处理时间
        end_time = time.time()
        processing_time = end_time - start_time

        print(f"[{filename}] YOLO检测到 {len(detections)} 个对象，处理时间：{processing_time:.2f}秒")

        # 生成输出文件名
        bbox_filename = f"{os.path.splitext(filename)[0]}_with_yolo{os.path.splitext(filename)[1]}"
        bbox_output_path = os.path.join(output_dir, bbox_filename)

        # 绘制边界框并应用图片处理
        draw_yolo_boxes_on_image(image_path, detections, bbox_output_path, processing_type)
        
        print(f"✓ 成功处理: {filename}")
        print(f"  - 标注图保存至: {bbox_output_path}")
        
        return {
            'success': True,
            'filename': filename,
            'bbox_filename': bbox_filename,
            'detections': detections,
            'detection_count': len(detections),
            'processing_time': processing_time
        }
        
    except Exception as e:
        error_msg = f"处理图片 {filename} 时出错: {str(e)}"
        print(f"✗ {error_msg}")
        return {
            'success': False,
            'filename': filename,
            'error': str(e)
        }

def main():
    """主函数"""
    print("=" * 60)
    print("YOLO手写体/文本检测脚本")
    print("=" * 60)
    
    # 检查ultralytics是否安装
    try:
        from ultralytics import YOLO
        print("✓ ultralytics库已安装")
    except ImportError:
        print("✗ ultralytics库未安装，请运行: pip install ultralytics")
        return
    
    # 获取用户选择的题型
    question_type, pinyin_name = get_question_type()

    # 获取用户选择的YOLO模型
    model_file, model_description = get_yolo_model()

    # 获取用户选择的图片处理方式
    processing_type_id, processing_type_name = get_image_processing_type()
    
    # 构建题型相关的路径
    types_dir = "types"
    question_dir = os.path.join(types_dir, pinyin_name)
    images_dir = os.path.join(question_dir, "images")
    yolo_result_dir = os.path.join(question_dir, "YOLO_result")
    
    print(f"\n使用路径：")
    print(f"题型目录：{question_dir}")
    print(f"图片文件夹：{images_dir}")
    print(f"结果文件夹：{yolo_result_dir}")
    
    # 检查图片文件夹是否存在
    if not os.path.exists(images_dir):
        print(f"错误：图片文件夹 {images_dir} 不存在！")
        return
    
    # 检查并创建YOLO_result目录
    os.makedirs(yolo_result_dir, exist_ok=True)
    
    # 获取图片文件
    image_files = get_image_files(images_dir)
    if not image_files:
        print(f"错误：在 {images_dir} 文件夹中没有找到图片文件！")
        print("支持的格式：.jpg, .jpeg, .png, .gif, .webp, .bmp")
        return
    
    print(f"找到 {len(image_files)} 张图片")
    
    # 加载YOLO模型
    print(f"\n正在加载YOLO模型: {model_file}")
    try:
        model = YOLO(model_file)
        print("✓ 模型加载成功")
    except Exception as e:
        print(f"✗ 模型加载失败: {str(e)}")
        return
    
    # 创建输出目录
    timestamp = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    output_dir = os.path.join(yolo_result_dir, f"images_{timestamp}")
    os.makedirs(output_dir, exist_ok=True)
    print(f"输出目录: {output_dir}")
    
    # 处理所有图片
    print("\n--- 开始处理图片 ---\n")
    results = []
    success_count = 0
    total_detections = 0

    for image_path in image_files:
        result = process_single_image(image_path, output_dir, model, processing_type_id)
        results.append(result)

        if result['success']:
            success_count += 1
            total_detections += result['detection_count']

    # 生成处理报告
    print("\n" + "=" * 60)
    print("处理完成！")
    print("=" * 60)
    print(f"总计图片: {len(image_files)}")
    print(f"成功处理: {success_count}")
    print(f"失败处理: {len(image_files) - success_count}")
    print(f"成功率: {success_count/len(image_files)*100:.1f}%")
    print(f"总检测对象数: {total_detections}")
    print(f"平均每张图片检测对象数: {total_detections/success_count:.1f}" if success_count > 0 else "平均每张图片检测对象数: 0")
    print(f"结果保存在: {output_dir}")

    # 生成summary.md报告
    summary_path = os.path.join(output_dir, "summary.md")
    with open(summary_path, 'w', encoding='utf-8') as f:
        # 头部统计信息
        f.write(f"# YOLO文本/手写体检测报告\n\n")
        f.write(f"**题型**: {question_type}\n\n")
        f.write(f"**YOLO模型**: {model_description}\n\n")
        f.write(f"**运行时间**: {timestamp}\n\n")
        f.write(f"**处理统计**:\n")
        f.write(f"- 总计图片: {len(image_files)}\n")
        f.write(f"- 成功处理: {success_count}\n")
        f.write(f"- 失败处理: {len(image_files) - success_count}\n")
        f.write(f"- 成功率: {success_count/len(image_files)*100:.1f}%\n")
        f.write(f"- 总检测对象数: {total_detections}\n")
        f.write(f"- 平均每张图片检测对象数: {total_detections/success_count:.1f}\n\n" if success_count > 0 else "- 平均每张图片检测对象数: 0\n\n")

        # 详细处理结果
        f.write("## 详细处理结果\n\n")

        for i, result in enumerate(results, 1):
            f.write(f"### 第 {i} 张图片: {result['filename']}\n\n")

            if result['success']:
                # 显示处理后的图片
                f.write(f"![{result['bbox_filename']}]({result['bbox_filename']})\n\n")

                # YOLO检测内容
                f.write("**YOLO检测内容:**\n")
                f.write(f"- 检测到对象数量: {result['detection_count']}\n")
                f.write(f"- 处理时间: {result.get('processing_time', 0):.2f}秒\n")

                if result['detections']:
                    f.write("- 检测结果详情:\n")
                    for j, detection in enumerate(result['detections'], 1):
                        x1, y1, x2, y2 = detection['coords']
                        width = x2 - x1
                        height = y2 - y1
                        area = width * height
                        f.write(f"  - 对象{j}: {detection['class_name']}\n")
                        f.write(f"    - 置信度: {detection['confidence']:.3f}\n")
                        f.write(f"    - 坐标: ({x1}, {y1}, {x2}, {y2})\n")
                        f.write(f"    - 尺寸: {width}×{height}, 面积: {area}\n")
                else:
                    f.write("- 未检测到任何对象\n")

            else:
                f.write(f"**处理失败**: {result['error']}\n")

            f.write("\n" + "-" * 50 + "\n\n")

        # 添加技术说明
        f.write("## 技术说明\n\n")
        f.write("### YOLO模型说明\n")
        f.write("- **YOLOv8**: You Only Look Once v8，实时目标检测模型\n")
        f.write("- **预训练模型**: 使用COCO数据集预训练，可检测80种常见对象类别\n")
        f.write("- **检测类别**: 包括person、book、laptop、cell phone等可能与手写内容相关的对象\n\n")

        f.write("### 模型性能对比\n")
        f.write("- **Nano (n)**: 最快速度，适合实时应用\n")
        f.write("- **Small (s)**: 平衡速度和准确率\n")
        f.write("- **Medium (m)**: 较高准确率，适中速度\n")
        f.write("- **Large (l)**: 高准确率，较慢速度\n")
        f.write("- **Extra Large (x)**: 最高准确率，最慢速度\n\n")

        f.write("### 参数设置\n")
        f.write("- 置信度阈值: 0.25（可调整）\n")
        f.write("- 输入图片尺寸: 自动调整\n")
        f.write("- 检测类别: COCO数据集的80个类别\n\n")

        f.write("### 注意事项\n")
        f.write("- YOLO主要检测预定义的对象类别，可能不会直接检测\"手写体\"\n")
        f.write("- 建议关注检测到的book、paper、document等相关类别\n")
        f.write("- 如需专门检测手写体，建议使用专门训练的文本检测模型\n\n")

        # 添加结束标记
        f.write("=" * 50 + "\n")
        f.write("所有图片处理完成！\n")
        f.write("=" * 50 + "\n")

    print(f"Summary报告保存在: {summary_path}")

if __name__ == "__main__":
    main()
