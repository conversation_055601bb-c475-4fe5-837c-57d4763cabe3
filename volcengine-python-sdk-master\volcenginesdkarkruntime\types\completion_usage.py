# File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.

from pydantic import BaseModel
from typing import Optional

__all__ = ["CompletionUsage", "PromptTokensDetails"]


class PromptTokensDetails(BaseModel):
    cached_tokens: int
    """Number of tokens hit cache."""


class CompletionTokensDetails(BaseModel):
    reasoning_tokens: Optional[int] = None
    """Tokens generated by the model for reasoning."""


class CompletionUsage(BaseModel):
    completion_tokens: int
    """Number of tokens in the generated completion."""

    prompt_tokens: int
    """Number of tokens in the prompt."""

    total_tokens: int
    """Total number of tokens used in the request (prompt + completion)."""

    prompt_tokens_details: Optional[PromptTokensDetails] = None
    """Prompt tokens details."""

    completion_tokens_details: Optional[CompletionTokensDetails] = None
    """Breakdown of tokens used in a completion."""
