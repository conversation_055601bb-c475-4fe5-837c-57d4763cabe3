# Example Code generated by Beijing Volcanoengine Technology.
from __future__ import print_function
import volcenginesdkcore
import volcenginesdkvpc
from pprint import pprint
from volcenginesdkcore.rest import ApiException

if __name__ == '__main__':
    configuration = volcenginesdkcore.Configuration()
    configuration.ak = "Your AK"
    configuration.sk = "Your SK"
    configuration.region = "cn-beijing"
    # set default configuration
    volcenginesdkcore.Configuration.set_default(configuration)

    # use global default configuration
    api_instance = volcenginesdkvpc.VPCApi()
    create_ha_vip_request = volcenginesdkvpc.CreateHaVipRequest(
        ha_vip_name="havip-1",
        ip_address="192.XX.XX.10",
        subnet_id="subnet-ina9r9xnfpc08gbs****",
    )
    
    try:
        resp = api_instance.create_ha_vip(create_ha_vip_request)
        pprint(resp)
    except ApiException as e:
        print("Exception when calling api: %s\n" % e)
