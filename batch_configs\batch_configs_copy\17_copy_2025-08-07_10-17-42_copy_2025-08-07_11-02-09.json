{"batch_configs": [{"处理模式": 3, "模型ID": 1, "题型": 10, "像素增强": "n", "像素粘连": "n", "图像放大倍数": 1, "response_format": 2, "top_p": 0.1, "test_prompt": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\\\"NAN\\\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\\\"题目 1\\\": \\\"识别内容 1\\\", \\\"题目 2\\\": \\\"识别内容 2\\\", \\\"题目 3\\\": \\\"识别内容 3\\\"} ，返回的 JSON 题号必须始终从\\\"题目 1\\\"开始，依次递增。\n\n识别时要严格按照以下的批改原则：\n1.如果学生回答难以辨认时，则返回\"NAN\"。\n\n2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。", "test3_prompt": "请判断学生答案与下方正确答案是否意义相同（忽略标点符号、小数点后多余的 0 等非意义性差异），必须按照如下 JSON 格式识别：{\\\"题目 1\\\": true, \\\"题目 2\\\": false, \\\"题目 3\\\": true}，返回的批改结果数量必须与正确答案数量一致，当学生回答与正确答案意义相同时，该题目为 true，否则为 false，识别的 JSON 题号必须始终从 \\\"题目 1\\\" 开始，依次递增。\n\n图片为学生回答的图片，首先先按照学生回答的JSON和正确答案的JSON进行比较。比较完成后，将图片和最终的比较结果进行比对，看看是否需要修改回答的JSON。\n\n批改时要严格按照以下的批改原则：\n\n1.当题目为句子时，忽略句子中标点符号的影响，只需要关注句子中的单词拼写和单词顺序是否和答案一致。例如：图片上的学生答案为\"I want colourful balloons\"，正确答案为\"I want colourful balloons!\"，则返回\"true\"。\n\n2.当题目为数学填空题时，该题目的正确答案为数学数字时，只需要比较图片上该位置的学生答案和数学数字是否等价，而不需要严格要求两个答案完全一致。\n3.如果学生回答难以辨认时，则返回\"false\"。若正确答案为\"NAN\"时，则返回\"true\"。\n\n4.当题目信息中包含”画图“或”看图“的内容时，不要关注任何和画图相关的内容，只需要关注题目中答题位置括号或横线上学生书写的答案，而不需要关注画图的结果。例如：题目信息为”上图中涂黑色的面积是（9）cm²。“，则只需要比较正确答案中该题目答案和学生回答”9“是否一致即可，不需要看图片中画图的结果。\n\n5.请严格按照给出的正确答案的JSON和图片上的学生答案进行比较，严禁根据图片上的题目信息联想答案。", "round2批改模式": 2, "图像文件夹": 1, "准确率": "准确率：88.21%  （(229 - 27) / 229）"}]}