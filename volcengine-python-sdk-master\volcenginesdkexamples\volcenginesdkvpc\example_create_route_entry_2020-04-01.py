# Example Code generated by Beijing Volcanoengine Technology.
from __future__ import print_function
import volcenginesdkcore
import volcenginesdkvpc
from pprint import pprint
from volcenginesdkcore.rest import ApiException

if __name__ == '__main__':
    configuration = volcenginesdkcore.Configuration()
    configuration.ak = "Your AK"
    configuration.sk = "Your SK"
    configuration.region = "cn-beijing"
    # set default configuration
    volcenginesdkcore.Configuration.set_default(configuration)

    # use global default configuration
    api_instance = volcenginesdkvpc.VPCApi()
    create_route_entry_request = volcenginesdkvpc.CreateRouteEntryRequest(
        destination_cidr_block="8.XX.XX.8/32",
        next_hop_id="eni-2fdzbqxfwrt345oxru******",
        next_hop_type="NetworkInterface",
        route_entry_name="RouteEntry_1",
        route_table_id="vtb-2fdzao4h726f45oxruw******",
    )
    
    try:
        resp = api_instance.create_route_entry(create_route_entry_request)
        pprint(resp)
    except ApiException as e:
        print("Exception when calling api: %s\n" % e)
