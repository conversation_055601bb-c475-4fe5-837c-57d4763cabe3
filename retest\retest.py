# export ARK_API_KEY="YOUR_API_KEY" 查看API KEY

import os
import sys
import base64
import json
from volcenginesdkarkruntime import Ark
import datetime
import re
import markdown
from bs4 import BeautifulSoup
from pypinyin import pinyin, Style
from PIL import Image
import io
import multiprocessing  # 导入多进程模块
import time  # 用于记录时间
from concurrent.futures import ThreadPoolExecutor, as_completed

# 自动修正项目根路径，无论在哪运行都能找到retest/types等目录
SCRIPT_DIR = os.path.dirname(os.path.abspath(__file__))
PROJECT_ROOT = os.path.abspath(os.path.join(SCRIPT_DIR, '..'))
os.chdir(PROJECT_ROOT)
print(f"[INFO] 当前工作目录已自动切换为项目根目录: {PROJECT_ROOT}")

def image_to_base64(image_path):
    """将图片文件转换为base64编码"""
    with open(image_path, "rb") as image_file:
        image_data = image_file.read()

    # 直接处理图片，不进行增强和缩放
    image = Image.open(io.BytesIO(image_data))
    buffer = io.BytesIO()
    ext = os.path.splitext(image_path)[1].lower()
    img_format = image.format if image.format else 'JPEG'
    image.save(buffer, format=img_format)
    image_bytes = buffer.getvalue()

    if ext in ['.jpg', '.jpeg']:
        mime_type = 'image/jpeg'
    elif ext == '.png':
        mime_type = 'image/png'
    elif ext == '.gif':
        mime_type = 'image/gif'
    elif ext == '.webp':
        mime_type = 'image/webp'
    else:
        mime_type = 'image/jpeg'  # 默认使用jpeg

    encoded_string = base64.b64encode(image_bytes).decode('utf-8')
    return f"data:{mime_type};base64,{encoded_string}"

def get_image_files(images_dir):
    """获取images文件夹中的所有图片文件，按文件名字典序排序"""
    image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp']
    image_files = []

    # 确保路径存在
    if os.path.exists(images_dir):
        print(f"图片文件夹存在: {images_dir}")
        for filename in os.listdir(images_dir):
            if any(filename.lower().endswith(ext) for ext in image_extensions):
                full_path = os.path.join(images_dir, filename)
                # 确保使用规范化的路径
                norm_path = os.path.normpath(full_path)
                image_files.append(norm_path)
                print(f"找到图片: {norm_path}")
    else:
        print(f"警告: 图片文件夹不存在: {images_dir}")

    # 按文件名字典序排序
    return sorted(image_files)

def markdown_to_text(markdown_content):
    """将markdown格式转换为纯文本"""
    # 将markdown转换为HTML
    html = markdown.markdown(markdown_content)
    # 使用BeautifulSoup提取纯文本
    soup = BeautifulSoup(html, 'html.parser')
    # 获取纯文本内容
    text = soup.get_text()
    # 清理多余的空白字符
    text = re.sub(r'\n\s*\n', '\n\n', text)  # 将多个空行替换为两个换行
    text = re.sub(r'[ \t]+', ' ', text)  # 将多个空格替换为单个空格
    return text.strip()

def chinese_to_pinyin(chinese_text):
    """将中文转换为拼音（无声调）"""
    pinyin_list = pinyin(chinese_text, style=Style.NORMAL)
    return ''.join([p[0] for p in pinyin_list])

def get_model_choice():
    """让用户选择模型ID"""
    available_models = {
        "1": "doubao-seed-1-6-250615",
        "2": "doubao-seed-1-6-flash-250715",
        "3": "doubao-1-5-thinking-vision-pro-250428",
        "4": "doubao-1-5-vision-pro-32k-250115"
    }

    print("请选择模型ID：")
    for key, value in available_models.items():
        print(f"{key}. {value}")

    while True:
        user_input = input("请输入模型编号（1-4）：").strip()
        if user_input in available_models:
            selected_model = available_models[user_input]
            print(f"选择的模型：{selected_model}")
            return selected_model
        else:
            print("输入无效，请输入 1-4 的数字")


def get_max_tokens_for_model(model_id):
    """根据模型ID返回对应的max_tokens值"""
    if model_id == "doubao-1-5-vision-pro-32k-250115":  # 模型4
        return 12288  # 12K
    else:  # 模型1、2、3
        return 16384  # 16K

def get_question_type():
    """获取用户输入的题型并转换为拼音路径"""
    question_types = {
        "1": "涂卡选择题",
        "2": "涂卡判断题",
        "3": "连线题",
        "4": "图表题",
        "5": "翻译题",
        "6": "画图题",
        "7": "数学应用题",
        "8": "数学计算题",
        "9": "简单的四则运算",
        "10": "填空题",
        "11": "判断题",
        "12": "多选题",
        "13": "单选题"
    }

    print("请选择题型：")
    for key, value in question_types.items():
        print(f"{key}. {value}")

    while True:
        user_input = input("请输入题型编号（1-13）或直接输入中文：").strip()

        # 检查数字输入
        if user_input in question_types:
            question_type = question_types[user_input]
            break
        # 检查中文输入
        elif user_input in question_types.values():
            question_type = user_input
            break
        else:
            print("输入无效，请输入 1-13 的数字或直接输入中文题型名称")

    # 转换为拼音
    pinyin_name = chinese_to_pinyin(question_type)
    print(f"选择的题型：{question_type}")
    print(f"对应的拼音路径：{pinyin_name}")

    return question_type, pinyin_name

def get_repeat_count():
    """获取用户输入的重复请求次数"""
    while True:
        try:
            repeat_input = input("请输入重复请求次数（默认为1）：").strip()
            if not repeat_input:
                repeat_count = 1
            else:
                repeat_count = int(repeat_input)
            if repeat_count <= 0:
                print("重复次数必须为正整数！")
                continue
            break
        except ValueError:
            print("请输入有效的正整数！")

    print(f"设置重复请求次数：{repeat_count}")
    return repeat_count

def process_single_image_api(task):
    """
    只做API推理，输入为task字典（包含base64、图片名、prompt等）
    """
    from volcenginesdkarkruntime import Ark
    import os
    img_filename = task['img_filename']
    base64_image = task['base64_image']
    user_prompt = task['user_prompt']
    client_api_key = task['client_api_key']
    model_id = task.get('model_id', 'doubao-seed-1-6-250615')  # 支持模型参数
    index = task['index']
    image_path_prefix = task['image_path_prefix']
    repeat_count = task['repeat_count']

    sep = f"\n{'=' * 50}\n"
    info = f"处理第 {index} 张图片: {img_filename}"
    current_image_output_lines = []
    current_image_output_lines.append(sep)
    current_image_output_lines.append(info + "\n")
    current_image_output_lines.append(sep)
    current_image_output_lines.append(f"![{img_filename}]({image_path_prefix}{img_filename})\n")
    print(f"[PID {os.getpid()}] 处理第 {index} 张图片: {img_filename}，重复请求 {repeat_count} 次")

    # 创建API客户端
    client_local = Ark(
        base_url="https://ark.cn-beijing.volces.com/api/v3",
        api_key=client_api_key,
    )

    # 准备请求体，确保每次请求完全一致
    request_messages = [
        {
            "role": "user",
            "content": [
                {"type": "text", "text": user_prompt},
                {"type": "image_url", "image_url": {"url": base64_image, "detail": "high"}}
            ]
        }
    ]

    # 进行多次请求
    all_responses = []
    all_response_times = []
    all_token_usages = []

    try:
        for req_idx in range(repeat_count):
            print(f"[PID {os.getpid()}] 图片 {img_filename} 的第 {req_idx+1}/{repeat_count} 次请求")

            # 记录开始时间
            start_time = time.time()

            # 发送请求
            response = client_local.chat.completions.create(
                model=model_id,
                messages=request_messages,
                # extra_headers={'x-is-encrypted': 'true'},  # 已去除加密header
                temperature=1,
                top_p=0.7,
                max_tokens=get_max_tokens_for_model(model_id),
                thinking={"type": "disabled"},
            )

            # 记录结束时间并计算响应时间
            end_time = time.time()
            response_time = end_time - start_time
            all_response_times.append(response_time)

            # 处理响应内容
            resp_content = response.choices[0].message.content.strip()
            if resp_content.startswith("```json"):
                resp_content = resp_content[7:]
            if resp_content.startswith("```"):
                resp_content = resp_content[3:]
            if resp_content.endswith("```"):
                resp_content = resp_content[:-3]
            resp_content = resp_content.strip()
            all_responses.append(resp_content)

            # 收集token用量信息
            usage = getattr(response, 'usage', None)
            token_usage = {
                'total_tokens': usage.total_tokens if usage and hasattr(usage, 'total_tokens') else None,
                'cached_tokens': None,
                'reasoning_tokens': None
            }

            if usage:
                if hasattr(usage, 'prompt_tokens_details') and usage.prompt_tokens_details:
                    token_usage['cached_tokens'] = getattr(usage.prompt_tokens_details, 'cached_tokens', None)
                if hasattr(usage, 'completion_tokens_details') and usage.completion_tokens_details:
                    token_usage['reasoning_tokens'] = getattr(usage.completion_tokens_details, 'reasoning_tokens', None)

            all_token_usages.append(token_usage)

        # 将所有响应添加到输出中
        for req_idx in range(repeat_count):
            current_image_output_lines.append(f"### 第 {req_idx+1} 次请求响应内容：\n")
            current_image_output_lines.append("```json\n")
            current_image_output_lines.append(f"{all_responses[req_idx]}\n")
            current_image_output_lines.append("```\n")
            current_image_output_lines.append(f"第 {req_idx+1} 次请求响应时间：{all_response_times[req_idx]:.2f}秒\n")
            current_image_output_lines.append(f"第 {req_idx+1} 次请求token用量\n")
            current_image_output_lines.append(f"- total_tokens: {all_token_usages[req_idx]['total_tokens']}\n")
            current_image_output_lines.append(f"- cached_tokens: {all_token_usages[req_idx]['cached_tokens']}\n")
            current_image_output_lines.append(f"- reasoning_tokens: {all_token_usages[req_idx]['reasoning_tokens']}\n")

        # 判断多次请求输出是否一致，并加大加粗
        if repeat_count > 1:
            all_equal = all(r == all_responses[0] for r in all_responses)
            if all_equal:
                current_image_output_lines.append("\n## **多次请求输出相同**\n")
            else:
                current_image_output_lines.append("\n## **多次请求输出不同**\n")

        return {
            'success': True,
            'image_path': task['image_path'],
            'output_lines': current_image_output_lines,
            'response_content': all_responses
        }
    except Exception as e:
        err_msg = f"处理图片 {img_filename} 时出错: {str(e)}"
        print(f"[PID {os.getpid()}] 处理图片 {img_filename} 时出错: {str(e)}")
        current_image_output_lines.append(err_msg + "\n")
        return {
            'success': False,
            'image_path': task['image_path'],
            'output_lines': current_image_output_lines,
            'response_content': None
        }

if __name__ == "__main__":
    # 硬编码API Key
    client = Ark(
        # 此为默认路径，您可根据业务所在地域进行配置
        base_url="https://ark.cn-beijing.volces.com/api/v3",
        # 硬编码API Key
        api_key="36c2aa0e-8b2b-4412-bc92-d3d1cef96b1b",
    )

    # 获取用户选择的模型
    selected_model = get_model_choice()

    # 获取用户选择的题型
    question_type, pinyin_name = get_question_type()

    # 获取重复请求次数
    repeat_count = get_repeat_count()

    # 构建题型相关的路径
    types_dir = os.path.join("retest", "types")
    question_dir = os.path.join(types_dir, pinyin_name)
    images_dir = os.path.join(question_dir, "images")
    response_dir = os.path.join(question_dir, "response")
    prompt_file = os.path.join(question_dir, "prompt.md")

    print(f"\n使用路径：")
    print(f"图片文件夹：{images_dir}")
    print(f"结果文件夹：{response_dir}")
    print(f"提示词文件：{prompt_file}")

    # 检查并创建必要的目录
    os.makedirs(images_dir, exist_ok=True)
    os.makedirs(response_dir, exist_ok=True)

    # 从prompt.md文件读取提示词
    if not os.path.exists(prompt_file):
        print(f"错误：提示词文件 {prompt_file} 不存在！")
        print(f"请创建 {prompt_file} 文件并写入提示词内容")
        exit()

    try:
        with open(prompt_file, 'r', encoding='utf-8') as f:
            markdown_prompt = f.read().strip()
        print(f"已从文件 {prompt_file} 读取提示词")
        # 将markdown格式转换为纯文本
        user_prompt = markdown_to_text(markdown_prompt)
        print("已将markdown格式转换为纯文本")
    except Exception as e:
        print(f"读取提示词文件时出错：{str(e)}")
        exit()

    if not user_prompt:
        print("错误：提示词文件为空！")
        exit()

    # 获取images文件夹中的所有图片文件
    image_files = get_image_files(images_dir)

    # 生成 response 文件夹和文件名
    now = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    response_file = os.path.join(response_dir, f"{now}.md")
    output_lines = []

    output_lines.append(f"# 运行时间: {now}\n\n")
    output_lines.append(f"## 重复请求次数: {repeat_count}\n\n")

    if not image_files:
        msg1 = "images文件夹中没有找到图片文件！"
        msg2 = "支持的格式：.jpg, .jpeg, .png, .gif, .webp"
        print(msg1)
        print(msg2)
        output_lines.append(msg1 + "\n")
        output_lines.append(msg2 + "\n")
    else:
        msg1 = f"找到 {len(image_files)} 张图片，开始逐个处理..."
        msg2 = f"使用的提示词: {user_prompt}"
        print(msg1)
        print(msg2)
        output_lines.append(msg1 + "\n")
        output_lines.append(msg2 + "\n")
        print("\n--- 开始本地处理图片（增强/缩放/编码） ---\n")
        
        # 统一API Key，与test.py保持一致
        api_key = "36c2aa0e-8b2b-4412-bc92-d3d1cef96b1b"
            
        tasks = []
        for i, image_path in enumerate(image_files):
            img_filename = os.path.basename(image_path)
            try:
                base64_image = image_to_base64(image_path)
            except Exception as e:
                print(f"图片 {img_filename} 处理失败: {e}")
                base64_image = None
            tasks.append({
                'img_filename': img_filename,
                'base64_image': base64_image,
                'user_prompt': user_prompt,
                'client_api_key': api_key,  # 统一变量
                'model_id': selected_model,  # 添加模型ID
                'index': i + 1,
                'image_path_prefix': "../images/",
                'image_path': image_path,
                'repeat_count': repeat_count
            })
        
        # 过滤掉处理失败的图片
        tasks = [t for t in tasks if t['base64_image'] is not None]
        print(f"\n--- 本地处理完成，开始多线程API推理 ---\n")
        max_workers = min(20, len(tasks)) if len(tasks) > 0 else 1
        print(f"将使用 {max_workers} 个线程进行并发API推理。")
        all_processed_results = []
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_task = {executor.submit(process_single_image_api, task): task for task in tasks}
            for future in as_completed(future_to_task):
                result = future.result()
                all_processed_results.append(result)
        print("\n--- 多线程API推理完成，合并结果 ---\n")
        
        for result in all_processed_results:
            output_lines.extend(result['output_lines'])
        
        sep = f"\n{'=' * 50}\n"
        output_lines.append(sep)
        output_lines.append("所有图片处理完成！\n")
        output_lines.append(sep)
        print(sep)
        print("所有图片处理完成！")
        print(sep)

    # 写入新内容到新文件
    with open(response_file, "w", encoding="utf-8") as f:
        f.writelines(output_lines)

    print(f"结果已保存到：{response_file}")
