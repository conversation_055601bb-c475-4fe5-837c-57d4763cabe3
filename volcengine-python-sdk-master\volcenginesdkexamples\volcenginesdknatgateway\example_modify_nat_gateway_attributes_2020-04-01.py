# Example Code generated by Beijing Volcanoengine Technology.
from __future__ import print_function
import volcenginesdkcore
import volcenginesdknatgateway
from pprint import pprint
from volcenginesdkcore.rest import ApiException

if __name__ == '__main__':
    configuration = volcenginesdkcore.Configuration()
    configuration.ak = "Your AK"
    configuration.sk = "Your SK"
    configuration.region = "cn-beijing"
    # set default configuration
    volcenginesdkcore.Configuration.set_default(configuration)

    # use global default configuration
    api_instance = volcenginesdknatgateway.NATGATEWAYApi()
    modify_nat_gateway_attributes_request = volcenginesdknatgateway.ModifyNatGatewayAttributesRequest(
        nat_gateway_id="ngw-2fedgzyvtzaio59gp675l****",
        nat_gateway_name="nat-1",
        spec="Small",
    )
    
    try:
        resp = api_instance.modify_nat_gateway_attributes(modify_nat_gateway_attributes_request)
        pprint(resp)
    except ApiException as e:
        print("Exception when calling api: %s\n" % e)
