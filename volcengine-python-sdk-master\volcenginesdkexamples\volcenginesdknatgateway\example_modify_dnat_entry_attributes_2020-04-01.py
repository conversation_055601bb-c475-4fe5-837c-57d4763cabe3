# Example Code generated by Beijing Volcanoengine Technology.
from __future__ import print_function
import volcenginesdkcore
import volcenginesdknatgateway
from pprint import pprint
from volcenginesdkcore.rest import ApiException

if __name__ == '__main__':
    configuration = volcenginesdkcore.Configuration()
    configuration.ak = "Your AK"
    configuration.sk = "Your SK"
    configuration.region = "cn-beijing"
    # set default configuration
    volcenginesdkcore.Configuration.set_default(configuration)

    # use global default configuration
    api_instance = volcenginesdknatgateway.NATGATEWAYApi()
    modify_dnat_entry_attributes_request = volcenginesdknatgateway.ModifyDnatEntryAttributesRequest(
        dnat_entry_id="dnat-342abc3bc3****",
        internal_ip="192.XX.XX.88",
    )
    
    try:
        resp = api_instance.modify_dnat_entry_attributes(modify_dnat_entry_attributes_request)
        pprint(resp)
    except ApiException as e:
        print("Exception when calling api: %s\n" % e)
