{"batch_configs": [{"处理模式": 3, "模型ID": 1, "题型": 10, "像素增强": "n", "像素粘连": "n", "图像放大倍数": 1, "response_format": 2, "top_p": 0.1, "test_prompt": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。", "test3_prompt": "请判断学生答案与下方正确答案是否一致或等价，必须按照如下JSON格式识别： {\"题目1\": true, \"题目2\": false, \"题目3\": true} ，返回的批改结果数量必须与正确答案数量一致，当学生回答与下方的正确答案一致或者等价时，该题目为 true，否则为 false，识别的JSON题号必须始终从\"题目1\"开始，依次递增。", "round2批改模式": 2, "图像文件夹": 1}]}