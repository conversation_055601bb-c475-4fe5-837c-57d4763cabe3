# Example Code generated by Beijing Volcanoengine Technology.
from __future__ import print_function
import volcenginesdkcore
import volcenginesdkclb
from pprint import pprint
from volcenginesdkcore.rest import ApiException

if __name__ == '__main__':
    configuration = volcenginesdkcore.Configuration()
    configuration.ak = "Your AK"
    configuration.sk = "Your SK"
    configuration.region = "cn-beijing"
    # set default configuration
    volcenginesdkcore.Configuration.set_default(configuration)

    # use global default configuration
    api_instance = volcenginesdkclb.CLBApi()
    modify_load_balancer_attributes_request = volcenginesdkclb.ModifyLoadBalancerAttributesRequest(
        description="test",
        load_balancer_id="clb-bp1b6c719dfa08ex****",
        load_balancer_name="clb-test1",
        load_balancer_spec="small_1",
        modification_protection_reason="实例托管",
        modification_protection_status="ConsoleProtection",
    )
    
    try:
        resp = api_instance.modify_load_balancer_attributes(modify_load_balancer_attributes_request)
        pprint(resp)
    except ApiException as e:
        print("Exception when calling api: %s\n" % e)
