# Example Code generated by Beijing Volcanoengine Technology.
from __future__ import print_function
import volcenginesdkcore
import volcenginesdkvpc
from pprint import pprint
from volcenginesdkcore.rest import ApiException

if __name__ == '__main__':
    configuration = volcenginesdkcore.Configuration()
    configuration.ak = "Your AK"
    configuration.sk = "Your SK"
    configuration.region = "cn-beijing"
    # set default configuration
    volcenginesdkcore.Configuration.set_default(configuration)

    # use global default configuration
    api_instance = volcenginesdkvpc.VPCApi()
    req_egress_acl_entries = volcenginesdkvpc.EgressAclEntryForUpdateNetworkAclEntriesInput(
        description="ThisisEgressAclEntries01.",
        destination_cidr_ip="10.XX.XX.0/24",
        network_acl_entry_id="nae-2zecs97e0brcge46****",
        network_acl_entry_name="acl-01",
        policy="accept",
        port="-1/-1",
        protocol="all",
    )
    req_egress_acl_entries1 = volcenginesdkvpc.EgressAclEntryForUpdateNetworkAclEntriesInput(
        description="ThisisEgressAclEntries02.",
        destination_cidr_ip="10.XX.XX.0/24",
        network_acl_entry_id="nae-0iswk4d88jvds****",
        network_acl_entry_name="acl-02",
        policy="accept",
        port="80/80",
        protocol="icmp",
    )
    req_ingress_acl_entries = volcenginesdkvpc.IngressAclEntryForUpdateNetworkAclEntriesInput(
        description="ThisisIngressAclEntries01.",
        network_acl_entry_id="nae-2zepn32de59j8m4****",
        network_acl_entry_name="acl-3***",
        policy="accept",
        port="22/22",
        protocol="all",
        source_cidr_ip="10.XX.XX.0/24",
    )
    req_ingress_acl_entries1 = volcenginesdkvpc.IngressAclEntryForUpdateNetworkAclEntriesInput(
        description="ThisisIngressAclEntries02.",
        network_acl_entry_id="nae-xyz2dmndek90e****",
        network_acl_entry_name="acl-es***",
        policy="",
        port="80/80",
        protocol="tcp",
        source_cidr_ip="10.XX.XX.0/24",
    )
    update_network_acl_entries_request = volcenginesdkvpc.UpdateNetworkAclEntriesRequest(
        egress_acl_entries=[req_egress_acl_entries, req_egress_acl_entries1],
        ingress_acl_entries=[req_ingress_acl_entries, req_ingress_acl_entries1],
        network_acl_id="nacl-bp1fg655nh68xyz9****",
        update_egress_acl_entries=False,
        update_ingress_acl_entries=False,
    )
    
    try:
        resp = api_instance.update_network_acl_entries(update_network_acl_entries_request)
        pprint(resp)
    except ApiException as e:
        print("Exception when calling api: %s\n" % e)
