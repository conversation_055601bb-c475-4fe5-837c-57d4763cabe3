# Example Code generated by Beijing Volcanoengine Technology.
from __future__ import print_function
import volcenginesdkcore
import volcenginesdkautoscaling
from pprint import pprint
from volcenginesdkcore.rest import ApiException

if __name__ == '__main__':
    configuration = volcenginesdkcore.Configuration()
    configuration.ak = "Your AK"
    configuration.sk = "Your SK"
    configuration.region = "cn-beijing"
    # set default configuration
    volcenginesdkcore.Configuration.set_default(configuration)

    # use global default configuration
    api_instance = volcenginesdkautoscaling.AUTOSCALINGApi()
    describe_lifecycle_activities_request = volcenginesdkautoscaling.DescribeLifecycleActivitiesRequest(
        scaling_activity_id="sga-ybn0mwfy1yl8j1f6****",
    )
    
    try:
        resp = api_instance.describe_lifecycle_activities(describe_lifecycle_activities_request)
        pprint(resp)
    except ApiException as e:
        print("Exception when calling api: %s\n" % e)
