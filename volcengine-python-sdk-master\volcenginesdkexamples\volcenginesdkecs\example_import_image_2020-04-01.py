# Example Code generated by Beijing Volcanoengine Technology.
from __future__ import print_function
import volcenginesdkcore
import volcenginesdkecs
from pprint import pprint
from volcenginesdkcore.rest import ApiException

if __name__ == '__main__':
    configuration = volcenginesdkcore.Configuration()
    configuration.ak = "Your AK"
    configuration.sk = "Your SK"
    configuration.region = "cn-beijing"
    # set default configuration
    volcenginesdkcore.Configuration.set_default(configuration)

    # use global default configuration
    api_instance = volcenginesdkecs.ECSApi()
    import_image_request = volcenginesdkecs.ImportImageRequest(
        architecture="amd64",
        image_name="image-1",
        os_type="Linux",
        platform="CentOS",
        platform_version="7.6",
        url="xxx",
    )
    
    try:
        resp = api_instance.import_image(import_image_request)
        pprint(resp)
    except ApiException as e:
        print("Exception when calling api: %s\n" % e)
