# Example Code generated by Beijing Volcanoengine Technology.
from __future__ import print_function
import volcenginesdkcore
import volcenginesdkvpc
from pprint import pprint
from volcenginesdkcore.rest import ApiException

if __name__ == '__main__':
    configuration = volcenginesdkcore.Configuration()
    configuration.ak = "Your AK"
    configuration.sk = "Your SK"
    configuration.region = "cn-beijing"
    # set default configuration
    volcenginesdkcore.Configuration.set_default(configuration)

    # use global default configuration
    api_instance = volcenginesdkvpc.VPCApi()
    req_resource = volcenginesdkvpc.ResourceForAssociateNetworkAclInput(
        resource_id="subnet-67acfmxazb4p****",
    )
    req_resource1 = volcenginesdkvpc.ResourceForAssociateNetworkAclInput(
        resource_id="subnet-g655nh68xyz9****",
    )
    associate_network_acl_request = volcenginesdkvpc.AssociateNetworkAclRequest(
        network_acl_id="nacl-bp1fg655nh68xyz9****",
        resource=[req_resource, req_resource1],
    )
    
    try:
        resp = api_instance.associate_network_acl(associate_network_acl_request)
        pprint(resp)
    except ApiException as e:
        print("Exception when calling api: %s\n" % e)
