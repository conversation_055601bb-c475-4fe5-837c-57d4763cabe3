#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YOLO文本检测脚本（专门用于文本/手写体检测）

功能说明：
1. 让用户选择题型（和grounding_test.py一致）
2. 让用户选择图片处理方式：
   - 白噪点：在框外添加80%密度的白色像素点（模拟马赛克）
   - 高斯模糊：对框外背景轻度模糊（radius=5-10），保留轮廓但弱化细节
   - 半透明蒙版：将框外区域覆盖50%透明度的黑色图层（类似手机截图标注效果）
   - 仅画框：除了画框外，其他区域不做任何处理
3. 从相应题型的 images 文件夹中读取所有图片
4. 使用专门的文本检测方法结合YOLO进行检测
5. 在图片上绘制边界框并应用选择的处理方式
6. 将处理后的图片保存到YOLO_text_result文件夹中
7. 生成包含处理结果的summary.md文档

使用方法：
1. 安装依赖：pip install ultralytics opencv-python easyocr pypinyin
2. 运行脚本：python yolo_text_test.py
3. 选择题型和检测方法
4. 选择图片处理方式
5. 脚本会自动处理相应题型下的所有图片

注意：此脚本结合了YOLO的目标检测能力和专门的文本检测方法
"""

import os
import cv2
import datetime
import numpy as np
import time  # 新增：用于记录处理时间
import random  # 新增：用于白噪点生成
from pypinyin import pinyin, Style

def chinese_to_pinyin(chinese_text):
    """将中文转换为拼音（无声调）"""
    pinyin_list = pinyin(chinese_text, style=Style.NORMAL)
    return ''.join([p[0] for p in pinyin_list])

def get_question_type():
    """获取用户输入的题型并转换为拼音路径（和grounding_test.py一致）"""
    question_types = {
        "1": "涂卡选择题",
        "2": "涂卡判断题", 
        "3": "连线题",
        "4": "图表题",
        "5": "翻译题",
        "6": "画图题",
        "7": "数学应用题",
        "8": "数学计算题",
        "9": "简单的四则运算",
        "10": "填空题",
        "11": "判断题",
        "12": "多选题",
        "13": "单选题"
    }

    print("请选择题型：")
    for key, value in question_types.items():
        print(f"{key}. {value}")

    while True:
        user_input = input("请输入题型编号（1-13）或直接输入中文：").strip()

        # 检查数字输入
        if user_input in question_types:
            question_type = question_types[user_input]
            break
        # 检查中文输入
        elif user_input in question_types.values():
            question_type = user_input
            break
        else:
            print("输入无效，请输入 1-13 的数字或直接输入中文题型名称")

    # 转换为拼音
    pinyin_name = chinese_to_pinyin(question_type)
    print(f"选择的题型：{question_type}")
    print(f"对应的拼音路径：{pinyin_name}")

    return question_type, pinyin_name

def get_image_processing_type():
    """获取用户选择的图片处理方式"""
    processing_types = {
        "1": "白噪点",
        "2": "高斯模糊",
        "3": "半透明蒙版",
        "4": "仅画框"
    }

    print("\n请选择图片处理方式：")
    print("1. 白噪点：在框外添加80%密度的白色像素点（模拟马赛克）")
    print("2. 高斯模糊：对框外背景轻度模糊（radius=5-10），保留轮廓但弱化细节")
    print("3. 半透明蒙版：将框外区域覆盖50%透明度的黑色图层（类似手机截图标注效果）")
    print("4. 仅画框：除了画框外，其他区域不做任何处理")

    while True:
        user_input = input("请输入处理方式编号（1-4）：").strip()

        if user_input in processing_types:
            processing_type = processing_types[user_input]
            print(f"选择的处理方式：{processing_type}")
            return user_input, processing_type
        else:
            print("输入无效，请输入 1-4 的数字")

def get_detection_method():
    """让用户选择检测方法"""
    methods = {
        "1": "YOLO通用检测（检测所有对象类别）",
        "2": "EasyOCR文本检测（专门检测文本区域）",
        "3": "OpenCV+YOLO组合检测（推荐）"
    }
    
    print("\n请选择检测方法：")
    for key, value in methods.items():
        print(f"{key}. {value}")
    
    while True:
        user_input = input("请输入方法编号（1-3）：").strip()
        if user_input in methods:
            method = user_input
            break
        else:
            print("输入无效，请输入 1-3 的数字")
    
    print(f"选择的检测方法：{methods[method]}")
    return method, methods[method]

def get_image_files(images_dir):
    """获取images文件夹中的所有图片文件，按文件名字典序排序"""
    image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp']
    image_files = []
    if os.path.exists(images_dir):
        for filename in os.listdir(images_dir):
            if any(filename.lower().endswith(ext) for ext in image_extensions):
                image_files.append(os.path.join(images_dir, filename))
    # 按文件名字典序排序
    return sorted(image_files)

def detect_with_yolo(image_path, model, confidence_threshold=0.25):
    """使用YOLO模型检测图片中的对象"""
    try:
        results = model(image_path, conf=confidence_threshold)
        
        boxes = []
        if results and len(results) > 0:
            result = results[0]
            
            if result.boxes is not None:
                for box in result.boxes:
                    x1, y1, x2, y2 = box.xyxy[0].cpu().numpy()
                    confidence = box.conf[0].cpu().numpy()
                    class_id = int(box.cls[0].cpu().numpy())
                    
                    x1, y1, x2, y2 = int(x1), int(y1), int(x2), int(y2)
                    
                    boxes.append({
                        'coords': (x1, y1, x2, y2),
                        'confidence': float(confidence),
                        'class_id': class_id,
                        'class_name': model.names[class_id] if hasattr(model, 'names') else f'class_{class_id}',
                        'method': 'YOLO'
                    })
        
        return boxes
        
    except Exception as e:
        print(f"YOLO检测出错: {str(e)}")
        return []

def detect_with_easyocr(image_path):
    """使用EasyOCR检测文本区域"""
    try:
        import easyocr
        
        # 初始化EasyOCR读取器（支持中英文）
        reader = easyocr.Reader(['ch_sim', 'en'], gpu=False)
        
        # 检测文本
        results = reader.readtext(image_path)
        
        boxes = []
        for i, (bbox, text, confidence) in enumerate(results):
            # EasyOCR返回的bbox是四个点的坐标
            # 转换为 (x1, y1, x2, y2) 格式
            x_coords = [point[0] for point in bbox]
            y_coords = [point[1] for point in bbox]
            
            x1, y1 = int(min(x_coords)), int(min(y_coords))
            x2, y2 = int(max(x_coords)), int(max(y_coords))
            
            boxes.append({
                'coords': (x1, y1, x2, y2),
                'confidence': float(confidence),
                'class_id': 0,  # 文本类别
                'class_name': 'text',
                'text': text,
                'method': 'EasyOCR'
            })
        
        return boxes
        
    except ImportError:
        print("EasyOCR未安装，请运行: pip install easyocr")
        return []
    except Exception as e:
        print(f"EasyOCR检测出错: {str(e)}")
        return []

def detect_with_opencv(image_path):
    """使用OpenCV检测文本区域（简化版）"""
    try:
        image = cv2.imread(image_path)
        if image is None:
            return []
        
        gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
        
        # 自适应阈值
        thresh = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C, 
                                       cv2.THRESH_BINARY_INV, 11, 2)
        
        # 形态学操作
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (3, 3))
        morph = cv2.morphologyEx(thresh, cv2.MORPH_CLOSE, kernel, iterations=2)
        dilated = cv2.dilate(morph, kernel, iterations=2)
        
        # 查找轮廓
        contours, _ = cv2.findContours(dilated, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        boxes = []
        image_area = gray.shape[0] * gray.shape[1]
        min_area = max(100, image_area * 0.0002)
        max_area = image_area * 0.3
        
        for contour in contours:
            area = cv2.contourArea(contour)
            if min_area < area < max_area:
                x, y, w, h = cv2.boundingRect(contour)
                
                if w > 15 and h > 15 and w/h < 20 and h/w < 20:
                    boxes.append({
                        'coords': (x, y, x + w, y + h),
                        'confidence': 0.8,  # 固定置信度
                        'class_id': 0,
                        'class_name': 'text_region',
                        'method': 'OpenCV'
                    })
        
        return boxes
        
    except Exception as e:
        print(f"OpenCV检测出错: {str(e)}")
        return []

def merge_detections(detections_list, overlap_threshold=0.3):
    """合并多种方法的检测结果"""
    all_detections = []
    for detections in detections_list:
        all_detections.extend(detections)
    
    if not all_detections:
        return []
    
    # 按置信度排序
    all_detections.sort(key=lambda x: x['confidence'], reverse=True)
    
    merged = []
    used = [False] * len(all_detections)
    
    for i, det1 in enumerate(all_detections):
        if used[i]:
            continue
            
        x1_1, y1_1, x2_1, y2_1 = det1['coords']
        merged_det = dict(det1)
        
        for j, det2 in enumerate(all_detections[i+1:], i+1):
            if used[j]:
                continue
                
            x1_2, y1_2, x2_2, y2_2 = det2['coords']
            
            # 计算重叠
            overlap_x = max(0, min(x2_1, x2_2) - max(x1_1, x1_2))
            overlap_y = max(0, min(y2_1, y2_2) - max(y1_1, y1_2))
            overlap_area = overlap_x * overlap_y
            
            area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
            area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
            
            if overlap_area / min(area1, area2) > overlap_threshold:
                # 合并边界框
                merged_det['coords'] = (
                    min(x1_1, x1_2),
                    min(y1_1, y1_2),
                    max(x2_1, x2_2),
                    max(y2_1, y2_2)
                )
                merged_det['method'] = f"{merged_det['method']}+{det2['method']}"
                used[j] = True
        
        merged.append(merged_det)
        used[i] = True
    
    return merged

def create_bbox_mask(image_shape, detections):
    """创建边界框区域的掩码，返回框内为True，框外为False的掩码"""
    h, w = image_shape[:2]
    mask = np.zeros((h, w), dtype=bool)

    for detection in detections:
        x1, y1, x2, y2 = detection['coords']

        # 确保坐标在图像范围内，并且max坐标不能小于min坐标
        x1 = max(0, min(int(x1), w-1))
        y1 = max(0, min(int(y1), h-1))
        x2 = max(x1, min(int(x2), w-1))
        y2 = max(y1, min(int(y2), h-1))

        # 在掩码中标记边界框区域（确保有效的切片范围）
        if x2 > x1 and y2 > y1:
            mask[y1:y2+1, x1:x2+1] = True

    return mask

def apply_white_noise_processing(image, bbox_mask):
    """应用白噪点处理：在框外区域添加80%密度的白色像素点"""
    processed_image = image.copy()

    # 创建框外区域掩码
    outside_mask = ~bbox_mask

    # 调试信息
    total_pixels = bbox_mask.size
    bbox_pixels = np.sum(bbox_mask)
    outside_pixels = np.sum(outside_mask)
    print(f"  [调试] 总像素: {total_pixels}, 框内像素: {bbox_pixels}, 框外像素: {outside_pixels}")

    # 检查原图框内的白色像素数量
    original_bbox_region = image[bbox_mask]
    original_white_in_bbox = np.sum(np.all(original_bbox_region == [255, 255, 255], axis=1))
    print(f"  [调试] 原图框内白色像素: {original_white_in_bbox}")

    # 获取框外区域的像素位置
    outside_positions = np.where(outside_mask)
    total_outside_pixels = len(outside_positions[0])

    if total_outside_pixels > 0:
        # 固定选择80%的框外像素添加白噪点
        noise_density = 0.8
        num_noise_pixels = int(total_outside_pixels * noise_density)

        print(f"  [调试] 将添加 {num_noise_pixels} 个白噪点到框外区域")

        # 随机选择像素位置
        indices = random.sample(range(total_outside_pixels), num_noise_pixels)
        noise_y = outside_positions[0][indices]
        noise_x = outside_positions[1][indices]

        # 添加白色噪点（只在框外区域）
        processed_image[noise_y, noise_x] = [255, 255, 255]

        # 验证：检查处理后框内的白色像素数量
        processed_bbox_region = processed_image[bbox_mask]
        processed_white_in_bbox = np.sum(np.all(processed_bbox_region == [255, 255, 255], axis=1))

        if processed_white_in_bbox > original_white_in_bbox:
            print(f"  [警告] 框内新增了 {processed_white_in_bbox - original_white_in_bbox} 个白色像素！")
        elif processed_white_in_bbox == original_white_in_bbox:
            print(f"  [正常] 框内白色像素数量未变化，白噪点只在框外")
        else:
            print(f"  [异常] 框内白色像素减少了？这不应该发生")

    return processed_image

def apply_gaussian_blur_processing(image, bbox_mask):
    """应用高斯模糊处理：对框外背景轻度模糊（radius=5-10）"""
    processed_image = image.copy()

    # 随机选择模糊半径
    blur_radius = random.randint(5, 10)
    kernel_size = blur_radius * 2 + 1

    # 对整个图像应用高斯模糊
    blurred_image = cv2.GaussianBlur(image, (kernel_size, kernel_size), 0)

    # 只在框外区域应用模糊效果
    outside_mask = ~bbox_mask
    processed_image[outside_mask] = blurred_image[outside_mask]

    return processed_image

def apply_transparent_mask_processing(image, bbox_mask):
    """应用半透明蒙版处理：将框外区域覆盖50%透明度的黑色图层"""
    processed_image = image.copy().astype(np.float32)

    # 固定透明度和蒙版颜色
    alpha = 0.5  # 50%透明度
    mask_color = 0  # 黑色

    # 创建框外区域掩码
    outside_mask = ~bbox_mask

    # 应用半透明蒙版
    processed_image[outside_mask] = processed_image[outside_mask] * (1 - alpha) + mask_color * alpha

    return processed_image.astype(np.uint8)

def draw_detections_on_image(image_path, detections, output_path, processing_type="4"):
    """在图片上绘制检测结果并根据处理类型应用不同的图像处理效果"""
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError(f"无法读取图片: {image_path}")

    # 创建边界框掩码
    bbox_mask = create_bbox_mask(image.shape, detections)

    # 根据处理类型应用不同的图像处理
    if processing_type == "1":  # 白噪点
        processed_image = apply_white_noise_processing(image, bbox_mask)
    elif processing_type == "2":  # 高斯模糊
        processed_image = apply_gaussian_blur_processing(image, bbox_mask)
    elif processing_type == "3":  # 半透明蒙版
        processed_image = apply_transparent_mask_processing(image, bbox_mask)
    else:  # 仅画框（默认）
        processed_image = image.copy()

    colors = [
        (0, 0, 255),    # 红色
        (0, 255, 0),    # 绿色
        (255, 0, 0),    # 蓝色
        (0, 255, 255),  # 黄色
        (255, 0, 255),  # 紫色
        (255, 255, 0),  # 青色
    ]

    for i, detection in enumerate(detections):
        x1, y1, x2, y2 = detection['coords']
        confidence = detection['confidence']
        method = detection['method']
        class_name = detection['class_name']

        color = colors[i % len(colors)]

        # 绘制边界框
        cv2.rectangle(processed_image, (x1, y1), (x2, y2), color, 2)

        # 添加标签
        label = f"{class_name}({method}): {confidence:.2f}"
        if 'text' in detection and detection['text']:
            label += f" [{detection['text'][:10]}...]"

        label_size = cv2.getTextSize(label, cv2.FONT_HERSHEY_SIMPLEX, 0.5, 2)[0]

        # 绘制标签背景
        cv2.rectangle(processed_image, (x1, y1 - label_size[1] - 10),
                     (x1 + label_size[0], y1), color, -1)

        # 绘制标签文字
        cv2.putText(processed_image, label, (x1, y1 - 5),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.5, (255, 255, 255), 1)

    cv2.imwrite(output_path, processed_image)
    return True

def process_single_image(image_path, output_dir, detection_method, yolo_model=None, processing_type="4"):
    """处理单张图片"""
    filename = os.path.basename(image_path)
    print(f"正在处理: {filename}")

    try:
        # 记录开始时间
        start_time = time.time()

        detections = []

        if detection_method == "1" and yolo_model:
            # 仅使用YOLO
            detections = detect_with_yolo(image_path, yolo_model)
            method_name = "YOLO通用检测"

        elif detection_method == "2":
            # 仅使用EasyOCR
            detections = detect_with_easyocr(image_path)
            method_name = "EasyOCR文本检测"

        elif detection_method == "3":
            # 组合检测
            yolo_detections = detect_with_yolo(image_path, yolo_model) if yolo_model else []
            ocr_detections = detect_with_easyocr(image_path)
            opencv_detections = detect_with_opencv(image_path)

            # 合并结果
            detections = merge_detections([yolo_detections, ocr_detections, opencv_detections])
            method_name = "组合检测"

        # 记录结束时间并计算处理时间
        end_time = time.time()
        processing_time = end_time - start_time

        print(f"[{filename}] 使用{method_name}检测到 {len(detections)} 个区域，处理时间：{processing_time:.2f}秒")

        # 生成输出文件名
        bbox_filename = f"{os.path.splitext(filename)[0]}_with_detection{os.path.splitext(filename)[1]}"
        bbox_output_path = os.path.join(output_dir, bbox_filename)

        # 绘制检测结果并应用图片处理
        draw_detections_on_image(image_path, detections, bbox_output_path, processing_type)

        print(f"✓ 成功处理: {filename}")
        print(f"  - 标注图保存至: {bbox_output_path}")

        return {
            'success': True,
            'filename': filename,
            'bbox_filename': bbox_filename,
            'detections': detections,
            'detection_count': len(detections),
            'method': method_name,
            'processing_time': processing_time
        }

    except Exception as e:
        error_msg = f"处理图片 {filename} 时出错: {str(e)}"
        print(f"✗ {error_msg}")
        return {
            'success': False,
            'filename': filename,
            'error': str(e)
        }

def main():
    """主函数"""
    print("=" * 60)
    print("YOLO+文本检测组合脚本")
    print("=" * 60)

    # 获取用户选择的题型
    question_type, pinyin_name = get_question_type()

    # 获取用户选择的检测方法
    detection_method, method_description = get_detection_method()

    # 获取用户选择的图片处理方式
    processing_type_id, processing_type_name = get_image_processing_type()

    # 构建题型相关的路径
    types_dir = "types"
    question_dir = os.path.join(types_dir, pinyin_name)
    images_dir = os.path.join(question_dir, "images")
    yolo_text_result_dir = os.path.join(question_dir, "YOLO_text_result")

    print(f"\n使用路径：")
    print(f"题型目录：{question_dir}")
    print(f"图片文件夹：{images_dir}")
    print(f"结果文件夹：{yolo_text_result_dir}")

    # 检查图片文件夹是否存在
    if not os.path.exists(images_dir):
        print(f"错误：图片文件夹 {images_dir} 不存在！")
        return

    # 检查并创建YOLO_text_result目录
    os.makedirs(yolo_text_result_dir, exist_ok=True)

    # 获取图片文件
    image_files = get_image_files(images_dir)
    if not image_files:
        print(f"错误：在 {images_dir} 文件夹中没有找到图片文件！")
        print("支持的格式：.jpg, .jpeg, .png, .gif, .webp, .bmp")
        return

    print(f"找到 {len(image_files)} 张图片")

    # 加载YOLO模型（如果需要）
    yolo_model = None
    if detection_method in ["1", "3"]:
        try:
            from ultralytics import YOLO
            print(f"\n正在加载YOLO模型...")
            yolo_model = YOLO('yolov8n.pt')  # 使用最快的模型
            print("✓ YOLO模型加载成功")
        except ImportError:
            print("✗ ultralytics库未安装，请运行: pip install ultralytics")
            if detection_method == "1":
                return
        except Exception as e:
            print(f"✗ YOLO模型加载失败: {str(e)}")
            if detection_method == "1":
                return

    # 创建输出目录
    timestamp = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    output_dir = os.path.join(yolo_text_result_dir, f"images_{timestamp}")
    os.makedirs(output_dir, exist_ok=True)
    print(f"输出目录: {output_dir}")

    # 处理所有图片
    print("\n--- 开始处理图片 ---\n")
    results = []
    success_count = 0
    total_detections = 0

    for image_path in image_files:
        result = process_single_image(image_path, output_dir, detection_method, yolo_model, processing_type_id)
        results.append(result)

        if result['success']:
            success_count += 1
            total_detections += result['detection_count']

    # 生成处理报告
    print("\n" + "=" * 60)
    print("处理完成！")
    print("=" * 60)
    print(f"总计图片: {len(image_files)}")
    print(f"成功处理: {success_count}")
    print(f"失败处理: {len(image_files) - success_count}")
    print(f"成功率: {success_count/len(image_files)*100:.1f}%")
    print(f"总检测区域数: {total_detections}")
    print(f"平均每张图片检测区域数: {total_detections/success_count:.1f}" if success_count > 0 else "平均每张图片检测区域数: 0")
    print(f"结果保存在: {output_dir}")

    # 生成summary.md报告
    summary_path = os.path.join(output_dir, "summary.md")
    with open(summary_path, 'w', encoding='utf-8') as f:
        # 头部统计信息
        f.write(f"# YOLO+文本检测组合报告\n\n")
        f.write(f"**题型**: {question_type}\n\n")
        f.write(f"**检测方法**: {method_description}\n\n")
        f.write(f"**运行时间**: {timestamp}\n\n")
        f.write(f"**处理统计**:\n")
        f.write(f"- 总计图片: {len(image_files)}\n")
        f.write(f"- 成功处理: {success_count}\n")
        f.write(f"- 失败处理: {len(image_files) - success_count}\n")
        f.write(f"- 成功率: {success_count/len(image_files)*100:.1f}%\n")
        f.write(f"- 总检测区域数: {total_detections}\n")
        f.write(f"- 平均每张图片检测区域数: {total_detections/success_count:.1f}\n\n" if success_count > 0 else "- 平均每张图片检测区域数: 0\n\n")

        # 详细处理结果
        f.write("## 详细处理结果\n\n")

        for i, result in enumerate(results, 1):
            f.write(f"### 第 {i} 张图片: {result['filename']}\n\n")

            if result['success']:
                # 显示处理后的图片
                f.write(f"![{result['bbox_filename']}]({result['bbox_filename']})\n\n")

                # 检测内容
                f.write("**检测内容:**\n")
                f.write(f"- 使用方法: {result.get('method', '未知')}\n")
                f.write(f"- 检测到区域数量: {result['detection_count']}\n")
                f.write(f"- 处理时间: {result.get('processing_time', 0):.2f}秒\n")

                if result['detections']:
                    f.write("- 检测结果详情:\n")
                    for j, detection in enumerate(result['detections'], 1):
                        x1, y1, x2, y2 = detection['coords']
                        width = x2 - x1
                        height = y2 - y1
                        area = width * height
                        f.write(f"  - 区域{j}: {detection['class_name']} ({detection['method']})\n")
                        f.write(f"    - 置信度: {detection['confidence']:.3f}\n")
                        f.write(f"    - 坐标: ({x1}, {y1}, {x2}, {y2})\n")
                        f.write(f"    - 尺寸: {width}×{height}, 面积: {area}\n")
                        if 'text' in detection and detection['text']:
                            f.write(f"    - 识别文本: {detection['text']}\n")
                else:
                    f.write("- 未检测到任何区域\n")

            else:
                f.write(f"**处理失败**: {result['error']}\n")

            f.write("\n" + "-" * 50 + "\n\n")

        # 添加技术说明
        f.write("## 技术说明\n\n")
        f.write("### 检测方法对比\n")
        f.write("1. **YOLO通用检测**: 检测预定义的80种对象类别，速度快但可能遗漏文本\n")
        f.write("2. **EasyOCR文本检测**: 专门检测和识别文本，准确率高但速度较慢\n")
        f.write("3. **组合检测**: 结合多种方法，提供最全面的检测结果\n\n")

        f.write("### 优势分析\n")
        f.write("- **YOLO**: 实时检测，适合检测包含文本的对象（如书本、纸张）\n")
        f.write("- **EasyOCR**: 专业文本检测，可识别文本内容，支持多语言\n")
        f.write("- **OpenCV**: 基础图像处理，快速检测文本区域轮廓\n\n")

        f.write("### 建议使用场景\n")
        f.write("- **清晰手写体**: 推荐使用EasyOCR或组合检测\n")
        f.write("- **复杂背景**: 推荐使用组合检测\n")
        f.write("- **实时处理**: 推荐使用YOLO或OpenCV\n\n")

        # 添加结束标记
        f.write("=" * 50 + "\n")
        f.write("所有图片处理完成！\n")
        f.write("=" * 50 + "\n")

    print(f"Summary报告保存在: {summary_path}")

if __name__ == "__main__":
    main()
