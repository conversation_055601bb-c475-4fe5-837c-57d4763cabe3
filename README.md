# Doubao Test 工具集

一个基于火山引擎豆包大模型的图像处理工具集，包含多个功能脚本：

## 脚本概览

### 🤖 AI 模型相关脚本
1. **main.py** - 主控制脚本，支持手动输入和批处理配置两种模式
2. **one_stage_test.py** - 单阶段一站式图片处理和答题分析工具
3. **test.py** - 豆包大模型批量图片处理和答题分析工具（双阶段第一阶段）
4. **test2.py** - test.py的源码安装版本（使用本地volcengine-python-sdk）
5. **test3.py** - test.py的增强版本，支持更多图像处理功能
6. **grounding_test.py** - 豆包视觉定位 Grounding 功能测试工具

### 🔧 工具脚本
7. **fix_json_numbers.py** - JSON题号修复工具，统一题号格式
8. **manual_bbox_tool.py** - 人工画框工具，交互式手动绘制边界框
9. **roboflow_yolo_tool.py** - Roboflow YOLO标注数据处理工具

### 🖼️ 计算机视觉脚本
10. **OpenCV_test.py** - OpenCV手写体检测脚本
11. **yolo_test.py** - YOLO目标检测脚本
12. **yolo_text_test.py** - YOLO文本检测专用脚本

### 🔄 测试工具
13. **retest/retest.py** - API重复请求一致性测试工具
14. **check_all_response_templates.py** - Round2 Response Template 检查和修正工具

## 详细功能介绍

### 🤖 AI 模型相关脚本

#### main.py - 主控制脚本
- 🎛️ **双模式支持**：支持手动输入和批处理配置两种运行模式
- 📋 **批处理配置**：从JSON配置文件读取批处理任务，自动依次执行
- 🔄 **流程控制**：统一管理单阶段和双阶段处理流程
- 📊 **执行总结**：提供详细的批处理执行结果和文档位置信息
- 🛡️ **错误处理**：单个批处理失败不影响其他批处理的执行
- 📁 **文档跟踪**：自动跟踪和报告所有生成的输出文档位置
- 🎯 **智能提示词**：支持从Markdown文件读取自定义提示词，支持文本和文件两种格式
- ⚙️ **API参数控制**：支持自定义temperature、top_p、max_tokens等API参数
- 📝 **响应格式选择**：支持text和json_object两种响应格式
- 🔧 **配置管理**：支持命令行参数管理配置文件（--list、--rename）
- 📋 **配置副本**：自动创建包含实际文本内容的配置副本，便于调试和记录

#### one_stage_test.py - 单阶段一站式处理
- 🎯 **一站式处理**：将图片处理和答题分析合并为单个步骤
- 📝 **智能提示**：结合图片、提示词和正确答案进行推理
- 🔍 **自动纠错**：自动对比正确答案，生成错题分析和纠错文档
- 🖼️ **图像增强**：支持灰度阀门与像素增强处理
- 🔗 **像素粘连**：支持黑色像素粘连处理，增强图像连通性
- ⚡ **多进程处理**：支持并行处理提升效率
- 📊 **详细统计**：提供准确率计算和错题详细分析
- 🎯 **自定义提示词**：支持从配置文件传入自定义提示词
- ⚙️ **API参数控制**：支持自定义temperature、top_p、max_tokens等API参数
- 📝 **响应格式选择**：支持text和json_object两种响应格式
- 🔧 **灰度阀门控制**：支持自定义灰度阀门值（0-255范围）

#### test.py - 豆包大模型批量图片处理
- 🖼️ **批量图片处理**：自动处理 `images` 文件夹中的所有图片
- 📝 **Markdown 提示词**：支持从 `prompt.md` 文件读取提示词，自动转换为纯文本
- 📊 **结果对比**：自动与模板文件对比，计算准确率并标记错题
- 📁 **结果管理**：自动生成带时间戳的结果文件，保存在 `response` 文件夹
- 🔍 **错题分析**：自动识别和报告处理结果中的差异
- 🎨 **图像增强**：支持图像二值化处理，增强标记对比度
- 🔗 **像素粘连**：支持黑色像素粘连处理，填充"黑-白-黑"之间的白色像素，增强连通性
- 🎯 **自定义提示词**：支持从配置文件传入自定义提示词
- ⚙️ **API参数控制**：支持自定义temperature、top_p、max_tokens等API参数
- 📝 **响应格式选择**：支持text和json_object两种响应格式
- 🔧 **灰度阀门控制**：支持自定义灰度阀门值（0-255范围）
- 📋 **请求体优化**：输出中自动省略base64图像数据，提高可读性

#### test2.py - 源码安装版本
- 📦 **本地SDK**：使用项目内的 `volcengine-python-sdk-master` 源码
- 🔧 **兼容性**：解决直接安装SDK失败的问题
- ⚡ **多进程处理**：支持并行处理提升效率
- 🎯 **功能一致**：与test.py功能完全相同
- 🔄 **批改模式**：支持大模型批改和JSON比对两种批改模式
- 🎯 **自定义提示词**：支持从配置文件传入自定义提示词
- ⚙️ **API参数控制**：支持自定义temperature、top_p、max_tokens等API参数
- 📝 **响应格式选择**：支持text和json_object两种响应格式
- 📋 **请求体优化**：输出中自动省略base64图像数据，提高可读性

#### test3.py - 增强版本
- 🖼️ **高级图像处理**：更多图像增强选项和参数调节，包括黑色像素粘连处理
- 📊 **详细统计**：更丰富的处理统计信息
- 🔄 **灵活配置**：支持更多自定义参数
- 🚀 **性能优化**：优化的多进程处理机制
- 🎯 **自定义提示词**：支持从配置文件传入自定义提示词
- ⚙️ **API参数控制**：支持自定义temperature、top_p、max_tokens等API参数
- 📝 **响应格式选择**：支持text和json_object两种响应格式
- 🔧 **灰度阀门控制**：支持自定义灰度阀门值（0-255范围）
- 📋 **请求体优化**：输出中自动省略base64图像数据，提高可读性

#### grounding_test.py - 视觉定位功能
- 🎯 **视觉定位**：批量测试豆包的"视觉定位 Grounding"功能
- 🔲 **边界框绘制**：自动解析坐标并在图片上绘制边界框（支持多个边界框）
- 🚀 **多线程处理**：使用多进程并行调用API，大幅提升处理速度
- 📋 **详细报告**：生成包含token用量和处理统计的详细报告
- 🎨 **颜色区分**：不同边界框使用不同颜色，便于区分

### 🔧 工具脚本

#### fix_json_numbers.py - JSON题号修复工具
- 🔢 **题号统一**：将response_template.md中的JSON题号统一为"题目1"、"题目2"格式
- 📝 **自动修复**：自动识别和修复不规范的题号格式
- 🎯 **题型支持**：支持所有13种题型的处理
- ✅ **安全操作**：修改前自动备份原文件

#### manual_bbox_tool.py - 人工画框工具
- 🖱️ **交互式绘制**：提供鼠标拖拽绘制边界框的交互界面
- 🎨 **多种处理方式**：支持白噪点、高斯模糊、半透明蒙版、仅画框四种处理模式
- 🔲 **多框支持**：支持在单张图片上绘制多个边界框，不同颜色区分
- ⌨️ **快捷操作**：支持'r'重置、'n'下一张、'q'退出等快捷键操作
- 📊 **详细报告**：生成包含坐标信息和处理统计的summary.md文档
- 💾 **结果保存**：自动保存处理后的图片到manual_result文件夹

#### roboflow_yolo_tool.py - Roboflow YOLO标注数据处理工具
- 📋 **YOLO格式解析**：自动解析YOLO格式标注文件（.txt），支持归一化坐标转换
- 🎯 **数据集处理**：批量处理train文件夹中的Roboflow标注数据集
- 🎨 **多种处理方式**：支持白噪点、高斯模糊、半透明蒙版、仅画框四种处理模式
- 🖌️ **框样式自定义**：支持4种框粗细（细、中等、粗、很粗）和4种框颜色（黑色、黄色、红色、绿色）
- 📁 **标准结构**：支持标准的train/images和train/labels文件夹结构
- 🔢 **类别标注**：显示每个边界框的类别ID和详细坐标信息
- 📊 **处理统计**：生成包含图片尺寸、标注框数量等详细信息的报告

### 🖼️ 计算机视觉脚本

#### OpenCV_test.py - OpenCV手写体检测
- ✍️ **手写体检测**：使用OpenCV检测图片中的手写体区域
- 🔲 **边界框标注**：在检测到的区域绘制边界框
- 📊 **处理报告**：生成详细的检测结果报告
- 🎨 **可视化结果**：保存标注后的图片到OpenCV_result文件夹

#### yolo_test.py - YOLO目标检测
- 🤖 **YOLO检测**：使用YOLO模型进行目标检测
- 📝 **文本识别**：专门针对文本/手写体区域检测
- 🎯 **模型选择**：支持多种YOLO模型（yolov8n.pt, yolov8x.pt等）
- 📋 **结果保存**：保存检测结果到YOLO_result文件夹

#### yolo_text_test.py - YOLO文本检测专用
- 📝 **专业文本检测**：结合YOLO和专门的文本检测方法
- 🔍 **多种检测方式**：支持YOLO、EasyOCR、OpenCV等多种检测方法
- 🎨 **高质量标注**：更精确的文本区域边界框绘制
- 📊 **详细分析**：生成包含检测置信度的详细报告

### 🔄 测试工具

#### retest/retest.py - API重复请求一致性测试工具
- 🔄 **重复请求测试**：对同一张图片进行多次API请求，测试结果一致性
- 📊 **一致性分析**：自动判断多次请求的输出是否一致，并在报告中标注
- ⚡ **多线程处理**：支持多线程并发推理，提升处理效率
- 📝 **详细记录**：记录每次请求的响应内容、响应时间和token用量
- 🎯 **题型兼容**：支持与主程序相同的13种题型选择
- 📁 **独立目录**：使用独立的retest/types目录结构，不影响主程序数据

#### check_all_response_templates.py - Round2 Response Template 检查和修正工具
- 🔍 **自动检查**：检查round2_response_template.md中模型判断的准确性
- 🎯 **智能比对**：比对学生答案和正确答案，验证模型判断是否正确
- 🛠️ **交互式修正**：提供三种修正选项（全部修改、跳过、部分修改）
- 📝 **实时更新**：自动更新round2_response_template.md文件中的错误判断
- 📊 **错误汇总**：显示所有未修改的错误，便于后续处理
- 🎨 **中文界面**：友好的中文题型显示和操作提示
- 🔧 **格式兼容**：自动处理不同的JSON键名格式差异

## 安装依赖

### 方法一：直接安装（推荐）

**基础依赖（两个程序都需要）：**
```bash
pip install 'volcengine-python-sdk[ark]' markdown beautifulsoup4 pypinyin
```

**计算机视觉脚本额外依赖：**
```bash
pip install opencv-python numpy
```

**人工画框和Roboflow工具额外依赖：**
```bash
pip install opencv-python numpy pypinyin
```

### 方法二：源码安装（当直接安装失败时）

如果上述命令执行失败（通常是因为路径过长或其他环境问题），请参考 [源码安装 volcengine-python-sdk 并启用 [ark] 功能.md](./源码安装%20volcengine-python-sdk%20并启用%20[ark]%20功能.md) 文档进行源码安装。

源码安装步骤概述：
1. 使用项目中的 `volcengine-python-sdk-master` 源码
2. 从源码构建并安装基础包
3. 手动安装 `[ark]` 功能所需的额外依赖
4. 验证安装是否成功

## 使用方法

### main.py - 主控制脚本（推荐）

#### 手动输入模式
1. **运行主脚本**：
   ```bash
   python main.py
   ```
2. **选择输入模式**：选择"1. 手动输入"
3. **选择处理模式**：选择单阶段或双阶段处理
4. **按提示操作**：根据选择的模式，程序会自动调用相应的脚本

#### 批处理配置模式
1. **准备配置文件**：在`batch_configs`文件夹下创建JSON配置文件，格式如下：
   ```json
   {
     "batch_configs": [
       {
         "处理模式": 1,
         "round2图片": 1,
         "round2批改模式": 1,
         "模型ID": 1,
         "题型": 13,
         "图像文件夹": 1,
         "像素增强": "y",
         "像素粘连": "y",
         "图像放大倍数": 2,
         "response_format": "json_object",
         "temperature": 0.8,
         "top_p": 0.9,
         "max_tokens": 16384,
         "gray_threshold": 200,
         "test_prompt": "你是一个专业的批改老师...",
         "test2_prompt": "prompt.md",
         "test3_prompt": "请仔细分析图片...",
         "one_stage_test_prompt": "one_stage_prompt.md"
       }
     ]
   }
   ```
2. **运行主脚本**：
   ```bash
   python main.py
   ```
3. **选择批处理模式**：选择"2. batch_configs读取"
4. **自动执行**：程序会自动读取最新的配置文件并依次执行所有批处理任务

#### 命令行参数支持
```bash
# 列出所有可用的配置文件
python main.py --list

# 重命名配置文件为数字序列（1.json, 2.json等）
python main.py --rename

# 指定特定的配置文件
python main.py --config 配置文件名.json
```

#### 配置参数说明
- **处理模式**：1=单阶段，2=双阶段不发图片，3=双阶段发图片
- **round2图片**：1=是，2=否（仅双阶段时有效）
- **round2批改模式**：1=大模型批改，2=JSON比对（仅双阶段不发图片时有效）
- **模型ID**：1-4对应不同的豆包模型
- **题型**：1-13对应不同题型（如13=单选题）
- **图像文件夹**：1-7对应不同的图像文件夹（如1=images）
- **像素增强**："y"=是，"n"=否
- **像素粘连**："y"=采用黑色像素粘连，"n"=不采用（默认值为"n"）
- **图像放大倍数**：数字，如2、4、6等
- **response_format**："text"=文本格式，"json_object"=JSON对象格式
- **temperature**：API温度参数（0.0-2.0，默认1.0）
- **top_p**：API top_p参数（0.0-1.0，默认0.7）
- **max_tokens**：最大token数（根据模型自动设置）
- **gray_threshold**：灰度阀门值（0-255，默认200）
- **自定义提示词**：支持直接文本或.md文件路径（如"prompt.md"）

### one_stage_test.py - 单阶段一站式处理

1. **准备文件**：
   - 将图片放入对应题型的 `types/[题型拼音]/images` 文件夹
   - 在 `types/[题型拼音]/one_stage_prompt.md` 中写入提示词
   - 在 `types/[题型拼音]/response/answer.md` 中准备正确答案
2. **运行程序**：
   ```bash
   python one_stage_test.py
   ```
3. **选择参数**：根据提示选择模型、题型、图像文件夹、像素增强、像素粘连等参数
4. **查看结果**：结果保存在 `types/[题型拼音]/one_stage_response` 文件夹

### test.py - 图片处理和答题分析

1. **选择题型**：运行程序后，根据提示选择题型（支持多种题型）
2. **准备图片**：将需要处理的图片放入对应题型的 `types/[题型拼音]/images` 文件夹
3. **设置提示词**：在对应题型的 `types/[题型拼音]/prompt.md` 文件中写入提示词（支持Markdown格式）
4. **配置API密钥**：在 `test.py` 中设置你的火山引擎API密钥
5. **运行程序**：
   ```bash
   python test.py
   ```

### grounding_test.py - 视觉定位功能测试

1. **选择题型**：运行程序后，根据提示选择题型（和test.py保持一致）
2. **准备图片**：将需要处理的图片放入相应题型的 `images` 文件夹
3. **设置提示词**：在相应题型目录下创建 `grounding_prompt.md` 文件并编写提示词
4. **运行程序**：
   ```bash
   python grounding_test.py
   ```

### manual_bbox_tool.py - 人工画框工具

1. **选择题型**：运行程序后，根据提示选择题型（和其他脚本保持一致）
2. **选择处理方式**：选择图片处理方式（白噪点、高斯模糊、半透明蒙版、仅画框）
3. **准备图片**：将需要处理的图片放入相应题型的 `images` 文件夹
4. **运行程序**：
   ```bash
   python manual_bbox_tool.py
   ```
5. **交互操作**：
   - 按住鼠标左键拖拽绘制边界框
   - 按 'r' 重置当前图片的所有框
   - 按 'n' 进入下一张图片
   - 按 'q' 退出程序

### roboflow_yolo_tool.py - Roboflow YOLO标注数据处理

1. **选择题型**：运行程序后，根据提示选择题型（和其他脚本保持一致）
2. **选择处理方式**：选择图片处理方式（白噪点、高斯模糊、半透明蒙版、仅画框）
3. **选择框样式**：
   - **框粗细**：选择框的粗细（细、中等、粗、很粗）
   - **框颜色**：选择框的颜色（黑色、黄色、红色、绿色）
4. **准备数据**：确保相应题型目录下有train文件夹，包含：
   - `train/images/` - 存放图片文件
   - `train/labels/` - 存放YOLO格式标注文件（.txt）
5. **运行程序**：
   ```bash
   python roboflow_yolo_tool.py
   ```

### retest.py - API重复请求一致性测试

1. **选择题型**：运行程序后，根据提示选择题型（支持多种题型，和test.py一致）
2. **准备图片**：将需要处理的图片放入对应题型的 `retest/types/[题型拼音]/images` 文件夹
3. **设置提示词**：在对应题型的 `retest/types/[题型拼音]/prompt.md` 文件中写入提示词（支持Markdown格式）
4. **设置重复次数**：运行时根据提示输入每张图片的API重复请求次数（默认为1，可自定义）
5. **运行程序**：
   ```bash
   python retest/retest.py
   ```

#### 输出说明
- 程序会在对应题型的 `retest/types/[题型拼音]/response` 文件夹中生成带时间戳的Markdown文件
- 每张图片会进行多次API请求，输出每次请求的响应内容、响应时间和token用量
- 自动判断多次请求输出是否一致，并在报告中标注
- 支持多线程并发推理，提升处理效率

### check_all_response_templates.py - Round2 Response Template 检查和修正

1. **运行程序**：
   ```bash
   python check_all_response_templates.py
   ```
2. **选择题型**：从8个可用题型中选择要检查的题型（显示中文名称）
3. **查看错误**：程序会自动检查并显示所有错误，按组分类显示
4. **交互式修正**：对每组错误，选择处理方式：
   - **选项1**：修改该组的所有错误
   - **选项2**：跳过该组，不修改任何错误
   - **选项3**：修改部分题目（输入题目编号，用空格分隔）
5. **查看汇总**：处理完成后，显示所有未修改的错误列表

#### 检查逻辑
- **数据源**：
  - 学生答案：`types/[题型]/response/response_template.md` 中的"响应内容"
  - 正确答案：`types/[题型]/response/answer.md` 中的"响应内容"
  - 模型判断：`types/[题型]/round2_response/response_template.md` 中的"模型回答"
- **比对规则**：按顺序比对JSON中的value，忽略键名差异
- **判断标准**：学生答案与正确答案字符串完全相同 → 期望判断为true，否则为false

#### 使用示例
```
第246组 (题目2, 题目3):
  题目2: '1/3' vs '1/3' -> 模型:False, 应该:True
  题目3: '1/10' vs '0.1' -> 模型:True, 应该:False
是否修改第246组的错误? (1=修改, 2=不修改, 3=修改部分题目): 3
可修改的题目: 2, 3
请输入需要修改的题目编号（用空格分隔）: 2
✅ 成功修改第246组中的 1/1 个选中题目
```

## 输出说明

### main.py 输出

#### 手动输入模式
- 输出与选择的具体脚本相同（one_stage_test.py、test.py等）

#### 批处理配置模式
- **执行过程**：实时显示每个批处理配置的执行状态和参数
- **配置副本**：自动创建包含实际文本内容的配置副本到`batch_configs/batch_configs_copy/`目录
- **最终总结**：显示详细的批处理执行总结，包括：
  - 总批处理数量、成功数量、失败数量和成功率
  - 成功执行的批处理详细信息和生成的文档位置
  - 失败的批处理错误信息
  - 每个输出文档的完整路径和最新文件名
- **智能提示词处理**：自动识别Markdown文件格式的提示词并转换为文本内容

### one_stage_test.py 输出
- 程序会在对应题型的 `types/[题型拼音]/one_stage_response` 文件夹中生成带时间戳的Markdown文件
- 每个图片的处理结果包含：
  - 准确率统计（置于顶部）
  - 错题详细信息
  - 模型ID和图像文件夹信息
  - 使用的提示词
  - 图片显示
  - API请求体（自动省略base64图像数据）
  - API响应内容（JSON格式）
  - 处理时间和token用量
- **自动纠错功能**：
  - 与正确答案自动对比，计算准确率
  - 错题会在文件顶部列出
  - 生成详细的错题分析报告（不再复制错误图片）

### test.py 输出
- 程序会在对应题型的 `types/[题型拼音]/response` 文件夹中生成带时间戳的Markdown文件
- 每个图片的处理结果包含：
  - 准确率统计（置于顶部）
  - 错题详细信息
  - 模型ID和图像文件夹信息
  - 使用的提示词
  - 图片显示
  - API请求体（自动省略base64图像数据）
  - API响应内容（JSON格式）
- 自动计算准确率并与模板对比
- 错题会在文件顶部列出

### grounding_test.py 输出
- 程序会在对应题型的 `types/[题型拼音]/grounding_result` 文件夹中生成按时间命名的文件夹
- 每次运行的结果包含：
  - **标注图**：绘制了边界框的图片（文件名后缀 `_with_bbox`）
  - **处理报告**：`summary.md` 详细记录处理结果，包含：
    - 题型信息和运行时间
    - 使用的提示词
    - 处理统计（成功率、总token消耗）
    - 每张图片的API响应内容和token用量
    - 标注后的图片预览

### manual_bbox_tool.py 输出
- 程序会在对应题型的 `types/[题型拼音]/manual_result` 文件夹中生成按时间命名的文件夹
- 每次运行的结果包含：
  - **处理后图片**：应用了选择处理方式的图片（文件名后缀 `_with_bbox`）
  - **处理报告**：`summary.md` 详细记录处理结果，包含：
    - 题型信息和处理方式
    - 处理统计（成功处理数、总边界框数）
    - 每张图片的边界框坐标和详细信息

### roboflow_yolo_tool.py 输出
- 程序会在对应题型的 `types/[题型拼音]/roboflow_yolo_result` 文件夹中生成按时间命名的文件夹
- 每次运行的结果包含：
  - **标注图片**：绘制了YOLO标注框的图片（文件名后缀 `_with_roboflow_yolo`）
  - **处理报告**：`summary.md` 详细记录处理结果，包含：
    - 题型信息和处理方式
    - 框样式信息（粗细和颜色）
    - 处理统计（成功处理数、总标注框数）
    - 每张图片的标注框坐标、类别ID和详细信息

### retest.py 输出
- 程序会在对应题型的 `retest/types/[题型拼音]/response` 文件夹中生成带时间戳的Markdown文件
- 每张图片的处理结果包含：
  - 重复请求次数设置
  - 每次请求的API响应内容、响应时间和token用量
  - 一致性分析结果（是否所有请求返回相同结果）

### check_all_response_templates.py 输出
- **实时显示**：逐组显示错误信息和修正进度
- **修正反馈**：每次修正后显示成功修改的题目数量
- **错误汇总**：最后显示所有未修改的错误列表，便于后续处理
- **文件更新**：自动更新 `types/[题型]/round2_response/response_template.md` 文件
- **中文界面**：友好的中文提示和题型显示

## grounding_test.py 提示词格式

grounding_prompt.md 文件支持 Markdown 格式，脚本会自动转换为纯文本。

示例提示词（针对选择题）：
```markdown
# 视觉定位 Grounding 提示词

请仔细观察图片中的选择题，框出每个选项（A、B、C、D等）的位置，输出每个选项的 bounding box 坐标。

要求：
1. 识别图片中的所有选择题选项
2. 为每个选项返回边界框坐标
3. 坐标格式：使用 `<bbox>x_min y_min x_max y_max</bbox>` 标签包裹
4. 坐标范围为 0-1000
5. 如果有多个选项，请分别标注每个选项的位置

示例输出格式：
选项A: <bbox>100 200 300 250</bbox>
选项B: <bbox>100 300 300 350</bbox>
选项C: <bbox>100 400 300 450</bbox>
选项D: <bbox>100 500 300 550</bbox>
```

### 边界框绘制特性
- 支持多个边界框，用不同颜色区分
- 每个边界框都有标签（Box1, Box2, ...）
- 颜色顺序：红色、绿色、蓝色、黄色、紫色、青色等

### 性能特性
- **多线程处理**：使用多进程并行调用API，大幅提升处理速度
- **进程数量**：自动检测CPU核心数，默认使用所有可用核心
- **内存优化**：先完成图片编码，再进行并行API调用
- **错误处理**：单张图片失败不影响其他图片的处理

## 注意事项

### 通用注意事项
- 支持的图片格式：JPG, JPEG, PNG, GIF, WEBP, BMP
- 图片按文件名字典序处理
- 确保网络连接正常以访问火山引擎API
- 题型选择支持中文输入或数字选择
- 中文题型会自动转换为拼音作为文件夹名
- 支持的题型：涂卡选择题、涂卡判断题、连线题、图表题、翻译题、画图题、数学应用题、数学计算题、简单的四则运算、填空题、判断题、多选题、单选题
- API密钥已在脚本中硬编码（两个程序保持一致）

### main.py 特定注意事项
- **推荐使用**：作为统一入口，管理所有处理流程
- **批处理配置**：JSON配置文件需要放在`batch_configs`文件夹下，程序会自动选择时间最晚的文件
- **参数映射**：JSON中的数字会自动映射为对应的字符串参数（如模型ID、题型等）
- **错误处理**：单个批处理失败不会影响其他批处理的执行
- **文档跟踪**：会自动跟踪所有生成的输出文档位置并在最后汇总显示
- **提示词支持**：支持直接文本和Markdown文件两种格式，以`.md`结尾的会从`batch_configs/prompt/`目录读取
- **配置副本**：自动创建包含实际文本内容的配置副本到`batch_configs/batch_configs_copy/`目录
- **命令行参数**：支持`--list`列出配置、`--rename`重命名配置、`--config`指定配置文件
- **API参数控制**：支持自定义temperature、top_p、max_tokens等API参数
- **批改模式选择**：双阶段不发图片模式支持选择大模型批改或JSON比对

### one_stage_test.py 特定注意事项
- **一站式处理**：将图片处理和答题分析合并为单个步骤，效率更高
- **必需文件**：需要准备`one_stage_prompt.md`（提示词）和`response/answer.md`（正确答案）
- **自动纠错**：会自动与正确答案对比，生成错题分析
- **错题处理**：错误图片会自动复制到专门的错误文件夹，便于后续分析
- **图像增强**：支持灰度阀门与像素增强，可根据图片质量选择是否启用
- **像素粘连**：在选择图像增强后，可进一步选择是否采用黑色像素粘连处理，用于填充分离的黑色像素之间的空隙，增强图像连通性
- **自定义提示词**：支持从main脚本传入自定义提示词，优先级高于默认文件
- **API参数控制**：支持自定义temperature、top_p、max_tokens等API参数
- **响应格式选择**：支持text和json_object两种响应格式
- **请求体优化**：输出中自动省略base64图像数据，提高可读性

### test.py 特定注意事项
- 首次运行会自动创建模板文件
- **错题判断规则**：当模板文件（response_template.md）中某题目的JSON响应为 `{}` 时，该题目会被自动跳过比较，无论模型返回什么结果都默认判对，不会加入错题列表
- **自定义提示词**：支持从main脚本传入自定义提示词，优先级高于默认文件
- **API参数控制**：支持自定义temperature、top_p、max_tokens等API参数
- **响应格式选择**：支持text和json_object两种响应格式
- **请求体优化**：输出中自动省略base64图像数据，提高可读性
- **双阶段支持**：可通过命令行参数`--stage dual`指定为双阶段模式

### test2.py 特定注意事项
- **源码安装**：使用项目内的`volcengine-python-sdk-master`源码，解决直接安装SDK失败的问题
- **批改模式**：支持大模型批改和JSON比对两种批改模式，可在交互模式中选择或通过配置文件指定
- **多进程处理**：支持并行处理提升效率，特别适合大批量数据处理
- **配置文件支持**：支持通过`--config`参数从配置文件读取参数
- **自定义提示词**：支持从main脚本传入自定义提示词
- **API参数控制**：支持自定义temperature、top_p、max_tokens等API参数
- **响应格式选择**：支持text和json_object两种响应格式
- **请求体优化**：输出中自动省略base64图像数据，提高可读性

### test3.py 特定注意事项
- **增强版功能**：相比test.py和test2.py，提供更多图像处理选项和参数调节
- **图像上下文**：第二阶段处理时会发送图片，适合需要图像上下文的复杂分析场景
- **配置文件支持**：支持通过`--config`参数从配置文件读取参数
- **自定义提示词**：支持从main脚本传入自定义提示词
- **API参数控制**：支持自定义temperature、top_p、max_tokens等API参数
- **响应格式选择**：支持text和json_object两种响应格式
- **灰度阀门控制**：支持自定义灰度阀门值（0-255范围）
- **请求体优化**：输出中自动省略base64图像数据，提高可读性
- **多进程优化**：优化的多进程处理机制，提升处理效率

### grounding_test.py 特定注意事项
- 坐标范围为 0-1000，脚本会自动缩放到实际图片尺寸
- 如果API返回格式不正确，会跳过该图片并记录错误
- **只保存标注后的图片**，不保存原图，节省存储空间
- 如果提示词文件不存在，脚本会提示创建
- 如果坐标解析失败，会跳过该坐标并继续处理其他坐标

### manual_bbox_tool.py 特定注意事项
- 需要手动操作，每张图片都需要用户交互绘制边界框
- 支持多种图像处理方式，可根据需要选择合适的处理效果
- 绘制的边界框会实时显示，支持重置和重新绘制
- 如果不绘制任何边界框直接按'n'，该图片会被跳过

### roboflow_yolo_tool.py 特定注意事项
- 需要标准的Roboflow数据集结构（train/images和train/labels文件夹）
- 标注文件必须是YOLO格式（class_id center_x center_y width height，归一化坐标）
- 图片和标注文件需要同名（除扩展名外）
- 如果标注文件不存在或格式错误，会跳过该图片并记录错误
- **框样式选项**：
  - 框粗细：细(1像素)、中等(2像素)、粗(3像素)、很粗(5像素)
  - 框颜色：黑色、黄色、红色、绿色（BGR格式）

### retest.py 特定注意事项
- 使用独立的retest/types目录结构，不会影响主程序的数据
- 重复请求次数可自定义，建议根据测试需要合理设置
- 多线程处理可能会增加API调用频率，注意API限制
- 一致性判断基于字符串完全匹配，对格式敏感

### check_all_response_templates.py 特定注意事项
- **数据要求**：需要存在对应题型的response_template.md、answer.md和round2_response_template.md文件
- **备份建议**：修改前建议备份round2_response_template.md文件
- **键名兼容**：自动处理"题目1"和"题目 1"等不同键名格式
- **JSON格式**：要求所有JSON格式正确，会自动跳过格式错误的组
- **修改范围**：只修改模型判断结果，不修改其他内容
- **错误检测**：基于字符串完全匹配，不处理数学等价性（如"1/2"与"0.5"视为不同）

## 🔗 黑色像素粘连功能详解

### 功能概述
黑色像素粘连功能是一项图像预处理技术，用于增强图像中分离的黑色像素区域的连通性。该功能特别适用于处理手写体、涂卡等场景，可以有效改善图像识别的准确性。

### 算法原理
参考`1.py`脚本中的实现，采用以下算法：

#### 横向扫描
- 检测"黑-白-白-白-黑"的像素模式
- 将中间的三个白色像素填充为黑色
- 增强水平方向的连通性

#### 纵向扫描
- 检测"黑-白-白-白-黑"的像素模式
- 将中间的三个白色像素填充为黑色
- 增强垂直方向的连通性

### 使用场景
- **涂卡识别**：连接分离的涂卡标记，提高识别准确率
- **手写体处理**：增强手写字符的连通性
- **图像清理**：填补扫描或拍照过程中产生的细小间隙

### 配置方式

#### 交互模式
1. 选择"是否采用'灰度阀门与像素增强'处理？" → 选择"y"
2. 继续选择"是否采用'黑色像素粘连'处理？" → 选择"y"或"n"

#### batch_configs模式
在JSON配置文件中添加"像素粘连"字段：
```json
{
  "batch_configs": [
    {
      "像素增强": "y",
      "像素粘连": "y",
      "其他参数": "..."
    }
  ]
}
```

#### 参数说明
- **像素粘连**: "y"=采用黑色像素粘连，"n"=不采用黑色像素粘连
- **默认值**: "n"（不采用像素粘连）
- **前置条件**: 必须先启用"像素增强"功能

### 支持的脚本
- ✅ **test.py**: 完全支持，包括交互模式和批处理模式
- ✅ **test3.py**: 完全支持，包括交互模式和配置文件模式
- ✅ **one_stage_test.py**: 完全支持，包括交互模式和参数传递
- ✅ **main.py**: 完全支持批处理配置中的像素粘连字段

### 输出标识
启用像素粘连后，输出文件会在顶部显示相应标识：
- **含像素粘连**: `使用'灰度阀门与像素增强'处理（含黑色像素粘连）`
- **不含像素粘连**: `使用'灰度阀门与像素增强'处理（不含黑色像素粘连）`

### 注意事项
- 像素粘连功能仅在启用"像素增强"时可用
- 该功能会修改图像的像素值，可能影响某些特定的识别场景
- 建议根据实际图像质量和识别需求选择是否启用
- 默认不启用，确保向后兼容性

## 许可证

MIT License