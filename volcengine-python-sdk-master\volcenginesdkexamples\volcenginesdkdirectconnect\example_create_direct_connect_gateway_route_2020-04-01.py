# Example Code generated by Beijing Volcanoengine Technology.
from __future__ import print_function
import volcenginesdkcore
import volcenginesdkdirectconnect
from pprint import pprint
from volcenginesdkcore.rest import ApiException

if __name__ == '__main__':
    configuration = volcenginesdkcore.Configuration()
    configuration.ak = "Your AK"
    configuration.sk = "Your SK"
    configuration.region = "cn-beijing"
    # set default configuration
    volcenginesdkcore.Configuration.set_default(configuration)

    # use global default configuration
    api_instance = volcenginesdkdirectconnect.DIRECTCONNECTApi()
    create_direct_connect_gateway_route_request = volcenginesdkdirectconnect.CreateDirectConnectGatewayRouteRequest(
        destination_cidr_block="172.XX.XX.0/24",
        direct_connect_gateway_id="dcg-2fe3zsmkshs59g****",
        next_hop_id="dcv-7qthudw0ll6jmc****",
    )
    
    try:
        resp = api_instance.create_direct_connect_gateway_route(create_direct_connect_gateway_route_request)
        pprint(resp)
    except ApiException as e:
        print("Exception when calling api: %s\n" % e)
