from typing import List

from .._models import BaseModel
from typing_extensions import Literal

__all__ = ["CreateTokenizationResponse", "Tokenization"]


class Tokenization(BaseModel):
    index: int
    """The index of text"""

    object: Literal["tokenization"]
    """The object type, which is always "tokenization" """

    total_tokens: int
    """The total number of tokens generated by the model."""

    token_ids: List[int]
    """The list of ids of tokens generated by the model."""

    offset_mapping: List[List[int]]
    """The list of offsets of tokens generated by the model."""


class CreateTokenizationResponse(BaseModel):
    id: str
    """The list of tokens generated by the model."""

    created: int
    """The Unix timestamp (in seconds) of when the chat completion was created."""

    model: str
    """The model to generate the completion."""

    object: Literal["list"]
    """The object type, which is always `list`."""

    data: List[Tokenization]
    """The list of tokenization generated by the model."""
