#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修改JSON题号脚本
将response_template.md文件中所有JSON的题号都改为从"题目1"开始，依次递增
不管原始JSON的key是什么格式，都会统一改为"题目1"、"题目2"、"题目3"...
"""

import re
import json
import os
from pypinyin import pinyin, Style

def chinese_to_pinyin(chinese_text):
    """将中文转换为拼音（无声调）"""
    pinyin_list = pinyin(chinese_text, style=Style.NORMAL)
    return ''.join([p[0] for p in pinyin_list])

def get_question_type():
    """获取用户输入的题型并转换为拼音路径"""
    question_types = {
        "1": "涂卡选择题",
        "2": "涂卡判断题",
        "3": "连线题", 
        "4": "图表题",
        "5": "翻译题",
        "6": "画图题",
        "7": "数学应用题",
        "8": "数学计算题",
        "9": "简单的四则运算",
        "10": "填空题",
        "11": "判断题",
        "12": "多选题",
        "13": "单选题"
    }
    
    print("请选择题型：")
    for key, value in question_types.items():
        print(f"{key}. {value}")
    
    while True:
        user_input = input("请输入题型编号（1-13）或直接输入中文：").strip()
        
        # 检查数字输入
        if user_input in question_types:
            question_type = question_types[user_input]
            break
        # 检查中文输入
        elif user_input in question_types.values():
            question_type = user_input
            break
        else:
            print("输入无效，请输入 1-13 的数字或直接输入中文题型名称")
    
    # 转换为拼音
    pinyin_name = chinese_to_pinyin(question_type)
    print(f"选择的题型：{question_type}")
    print(f"对应的拼音路径：{pinyin_name}")
    
    return question_type, pinyin_name

def fix_json_numbers(file_path):
    """
    修改文件中所有JSON的题号，使其从"题目1"开始，依次递增
    不管原始JSON的key是什么格式，都会统一改为"题目1"、"题目2"、"题目3"...
    
    Args:
        file_path: 要修改的文件路径
    """
    
    # 读取文件内容
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 匹配所有JSON代码块
    json_pattern = r'```json\s*(\{.*?\})\s*```'
    
    def replace_json(match):
        json_str = match.group(1)
        try:
            # 解析JSON
            data = json.loads(json_str)
            
            # 如果JSON为空或只有一个键值对，直接返回原JSON
            if len(data) <= 1:
                return match.group(0)
            
            # 解析题号并排序
            def extract_question_number(key):
                """从题号中提取数字部分"""
                import re
                # 匹配数字部分
                match = re.search(r'\d+', str(key))
                if match:
                    return int(match.group())
                return 0  # 如果没有数字，返回0
            
            # 按照题号数字排序
            sorted_items = sorted(data.items(), key=lambda x: extract_question_number(x[0]))
            values = [item[1] for item in sorted_items]
            
            # 创建新的JSON对象，题号从1开始
            new_data = {}
            for i, value in enumerate(values, 1):
                new_key = f"题目{i}"
                new_data[new_key] = value
            
            # 转换为JSON字符串
            new_json_str = json.dumps(new_data, ensure_ascii=False, separators=(',', ':'))
            
            # 返回新的代码块
            return f'```json\n{new_json_str}\n```'
            
        except json.JSONDecodeError as e:
            print(f"JSON解析错误: {e}")
            print(f"问题JSON: {json_str}")
            return match.group(0)  # 返回原内容
        except Exception as e:
            print(f"处理JSON时出错: {e}")
            return match.group(0)  # 返回原内容
    
    # 替换所有JSON代码块
    new_content = re.sub(json_pattern, replace_json, content, flags=re.DOTALL)
    
    # 写回文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print(f"已成功修改文件: {file_path}")

def main():
    """主函数"""
    # 获取用户选择的题型
    question_type, pinyin_name = get_question_type()
    
    # 构建题型相关的路径
    types_dir = "types"
    question_dir = os.path.join(types_dir, pinyin_name)
    response_dir = os.path.join(question_dir, "response")
    template_file = os.path.join(response_dir, "response_template.md")
    
    print(f"\n使用路径：")
    print(f"题型文件夹：{question_dir}")
    print(f"结果文件夹：{response_dir}")
    print(f"模板文件：{template_file}")
    
    # 检查文件是否存在
    if not os.path.exists(template_file):
        print(f"错误：模板文件不存在: {template_file}")
        print(f"请确保在 {response_dir} 文件夹中存在 response_template.md 文件")
        return
    
    print(f"\n开始处理文件: {template_file}")
    
    # 备份原文件
    backup_path = template_file + ".backup"
    try:
        with open(template_file, 'r', encoding='utf-8') as f:
            content = f.read()
        with open(backup_path, 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"已创建备份文件: {backup_path}")
    except Exception as e:
        print(f"创建备份文件时出错: {e}")
        return
    
    # 修改JSON题号
    try:
        fix_json_numbers(template_file)
        print("处理完成！")
        print(f"所有JSON的题号已修改为从'题目1'开始，依次递增")
        print(f"排序方式：按照数字顺序（题目1、题目2、题目3...题目10），而不是字符串顺序")
    except Exception as e:
        print(f"处理文件时出错: {e}")
        # 恢复备份
        try:
            with open(backup_path, 'r', encoding='utf-8') as f:
                content = f.read()
            with open(template_file, 'w', encoding='utf-8') as f:
                f.write(content)
            print("已恢复原文件")
        except Exception as restore_error:
            print(f"恢复文件时出错: {restore_error}")

if __name__ == "__main__":
    main() 