# Example Code generated by Beijing Volcanoengine Technology.
from __future__ import print_function
import volcenginesdkcore
import volcenginesdkecs
from pprint import pprint
from volcenginesdkcore.rest import ApiException

if __name__ == '__main__':
    configuration = volcenginesdkcore.Configuration()
    configuration.ak = "Your AK"
    configuration.sk = "Your SK"
    configuration.region = "cn-beijing"
    # set default configuration
    volcenginesdkcore.Configuration.set_default(configuration)

    # use global default configuration
    api_instance = volcenginesdkecs.ECSApi()
    modify_instance_deployment_request = volcenginesdkecs.ModifyInstanceDeploymentRequest(
        deployment_set_id="dps-yc1o9aahks5m57nk****",
        instance_id="i-3tigy72q3u3vj0x2****",
    )
    
    try:
        resp = api_instance.modify_instance_deployment(modify_instance_deployment_request)
        pprint(resp)
    except ApiException as e:
        print("Exception when calling api: %s\n" % e)
