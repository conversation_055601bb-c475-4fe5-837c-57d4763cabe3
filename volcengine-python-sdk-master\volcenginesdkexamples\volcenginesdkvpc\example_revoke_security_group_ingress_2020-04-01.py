# Example Code generated by Beijing Volcanoengine Technology.
from __future__ import print_function
import volcenginesdkcore
import volcenginesdkvpc
from pprint import pprint
from volcenginesdkcore.rest import ApiException

if __name__ == '__main__':
    configuration = volcenginesdkcore.Configuration()
    configuration.ak = "Your AK"
    configuration.sk = "Your SK"
    configuration.region = "cn-beijing"
    # set default configuration
    volcenginesdkcore.Configuration.set_default(configuration)

    # use global default configuration
    api_instance = volcenginesdkvpc.VPCApi()
    revoke_security_group_ingress_request = volcenginesdkvpc.RevokeSecurityGroupIngressRequest(
        cidr_ip="10.XX.XX.0/8",
        policy="accept",
        port_end=22,
        port_start=22,
        priority=1,
        protocol="tcp",
        security_group_id="sg-bp67acfmxazb4p****",
    )
    
    try:
        resp = api_instance.revoke_security_group_ingress(revoke_security_group_ingress_request)
        pprint(resp)
    except ApiException as e:
        print("Exception when calling api: %s\n" % e)
