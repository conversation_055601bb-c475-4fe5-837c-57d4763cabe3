#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
人工画框工具

功能说明：
1. 让用户选择题型（和其他脚本一致）
2. 让用户选择图片处理方式（白噪点、高斯模糊、半透明蒙版、仅画框）
3. 从相应题型的 images 文件夹中读取所有图片
4. 提供交互式界面让用户手动绘制边界框
5. 在图片上绘制边界框并应用选择的处理方式
6. 将处理后的图片保存到manual_result文件夹中
7. 生成包含处理结果的summary.md文档

使用方法：
1. 安装依赖：pip install opencv-python numpy pypinyin
2. 运行脚本：python manual_bbox_tool.py
3. 选择题型和图片处理方式
4. 对每张图片：
   - 按住鼠标左键拖拽绘制边界框
   - 按 'r' 重置当前图片的所有框
   - 按 'n' 进入下一张图片
   - 按 'q' 退出程序
"""

import os
import cv2
import numpy as np
import datetime
import time
import random
from pypinyin import pinyin, Style

# 全局变量
drawing = False
start_x, start_y = -1, -1
current_boxes = []
temp_image = None
original_image = None

def chinese_to_pinyin(chinese_text):
    """将中文转换为拼音（无声调）"""
    pinyin_list = pinyin(chinese_text, style=Style.NORMAL)
    return ''.join([p[0] for p in pinyin_list])

def get_question_type():
    """获取用户输入的题型并转换为拼音路径"""
    question_types = {
        "1": "涂卡选择题",
        "2": "涂卡判断题", 
        "3": "连线题",
        "4": "图表题",
        "5": "翻译题",
        "6": "画图题",
        "7": "数学应用题",
        "8": "数学计算题",
        "9": "简单的四则运算",
        "10": "填空题",
        "11": "判断题",
        "12": "多选题",
        "13": "单选题"
    }

    print("请选择题型：")
    for key, value in question_types.items():
        print(f"{key}. {value}")

    while True:
        user_input = input("请输入题型编号（1-13）：").strip()
        if user_input in question_types:
            question_type = question_types[user_input]
            break
        else:
            print("输入无效，请输入 1-13 的数字")

    pinyin_name = chinese_to_pinyin(question_type)
    print(f"选择的题型：{question_type}")
    print(f"对应的拼音路径：{pinyin_name}")
    return question_type, pinyin_name

def get_image_processing_type():
    """获取用户选择的图片处理方式"""
    processing_types = {
        "1": "白噪点",
        "2": "高斯模糊", 
        "3": "半透明蒙版",
        "4": "仅画框"
    }

    print("\n请选择图片处理方式：")
    print("1. 白噪点：在框外随机添加80%密度的白色像素点")
    print("2. 高斯模糊：对框外背景轻度模糊（radius=5-10）")
    print("3. 半透明蒙版：将框外区域覆盖50%透明度的黑色图层")
    print("4. 仅画框：除了画框外，其他区域不做任何处理")

    while True:
        user_input = input("请输入处理方式编号（1-4）：").strip()
        if user_input in processing_types:
            processing_type = processing_types[user_input]
            print(f"选择的处理方式：{processing_type}")
            return user_input, processing_type
        else:
            print("输入无效，请输入 1-4 的数字")

def mouse_callback(event, x, y, flags, param):
    """鼠标回调函数，处理绘制边界框"""
    global drawing, start_x, start_y, current_boxes, temp_image, original_image
    
    if event == cv2.EVENT_LBUTTONDOWN:
        drawing = True
        start_x, start_y = x, y
        temp_image = original_image.copy()
        
    elif event == cv2.EVENT_MOUSEMOVE:
        if drawing:
            temp_img = original_image.copy()
            # 绘制已有的框
            for box in current_boxes:
                cv2.rectangle(temp_img, (box[0], box[1]), (box[2], box[3]), (0, 255, 0), 2)
            # 绘制当前正在画的框
            cv2.rectangle(temp_img, (start_x, start_y), (x, y), (0, 0, 255), 2)
            cv2.imshow('Manual BBox Tool', temp_img)
            
    elif event == cv2.EVENT_LBUTTONUP:
        if drawing:
            drawing = False
            # 确保坐标顺序正确
            x_min = min(start_x, x)
            y_min = min(start_y, y)
            x_max = max(start_x, x)
            y_max = max(start_y, y)
            
            # 只有当框有一定大小时才添加
            if abs(x_max - x_min) > 10 and abs(y_max - y_min) > 10:
                current_boxes.append((x_min, y_min, x_max, y_max))
                print(f"添加边界框: ({x_min}, {y_min}, {x_max}, {y_max})")
            
            # 重新绘制所有框
            temp_image = original_image.copy()
            for i, box in enumerate(current_boxes):
                color = [(0, 0, 255), (0, 255, 0), (255, 0, 0), (0, 255, 255), 
                        (255, 0, 255), (255, 255, 0)][i % 6]
                cv2.rectangle(temp_image, (box[0], box[1]), (box[2], box[3]), color, 2)
                cv2.putText(temp_image, f"Box{i+1}", (box[0], box[1]-10),
                           cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
            cv2.imshow('Manual BBox Tool', temp_image)

def create_bbox_mask(image_shape, boxes):
    """创建边界框区域的掩码"""
    h, w = image_shape[:2]
    mask = np.zeros((h, w), dtype=bool)
    
    for x_min, y_min, x_max, y_max in boxes:
        x_min = max(0, min(x_min, w-1))
        y_min = max(0, min(y_min, h-1))
        x_max = max(x_min, min(x_max, w-1))
        y_max = max(y_min, min(y_max, h-1))
        
        if x_max > x_min and y_max > y_min:
            mask[y_min:y_max+1, x_min:x_max+1] = True
    
    return mask

def apply_white_noise_processing(image, bbox_mask):
    """应用白噪点处理"""
    processed_image = image.copy()
    outside_mask = ~bbox_mask
    outside_positions = np.where(outside_mask)
    total_outside_pixels = len(outside_positions[0])
    
    if total_outside_pixels > 0:
        noise_density = 0.8
        num_noise_pixels = int(total_outside_pixels * noise_density)
        indices = random.sample(range(total_outside_pixels), num_noise_pixels)
        noise_y = outside_positions[0][indices]
        noise_x = outside_positions[1][indices]
        processed_image[noise_y, noise_x] = [255, 255, 255]
    
    return processed_image

def apply_gaussian_blur_processing(image, bbox_mask):
    """应用高斯模糊处理"""
    processed_image = image.copy()
    blur_radius = random.randint(5, 10)
    kernel_size = blur_radius * 2 + 1
    blurred_image = cv2.GaussianBlur(image, (kernel_size, kernel_size), 0)
    outside_mask = ~bbox_mask
    processed_image[outside_mask] = blurred_image[outside_mask]
    return processed_image

def apply_transparent_mask_processing(image, bbox_mask):
    """应用半透明蒙版处理"""
    processed_image = image.copy().astype(np.float32)
    alpha = 0.5
    mask_color = 0
    outside_mask = ~bbox_mask
    processed_image[outside_mask] = processed_image[outside_mask] * (1 - alpha) + mask_color * alpha
    return processed_image.astype(np.uint8)

def process_image_with_manual_boxes(image_path, boxes, output_path, processing_type="4"):
    """处理图片并应用选择的处理方式"""
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError(f"无法读取图片: {image_path}")
    
    bbox_mask = create_bbox_mask(image.shape, boxes)
    
    # 根据处理类型应用不同的图像处理
    if processing_type == "1":
        processed_image = apply_white_noise_processing(image, bbox_mask)
    elif processing_type == "2":
        processed_image = apply_gaussian_blur_processing(image, bbox_mask)
    elif processing_type == "3":
        processed_image = apply_transparent_mask_processing(image, bbox_mask)
    else:
        processed_image = image.copy()
    
    # 绘制边界框
    colors = [(0, 0, 255), (0, 255, 0), (255, 0, 0), (0, 255, 255), 
              (255, 0, 255), (255, 255, 0)]
    
    for i, (x_min, y_min, x_max, y_max) in enumerate(boxes):
        color = colors[i % len(colors)]
        cv2.rectangle(processed_image, (x_min, y_min), (x_max, y_max), color, 2)
        cv2.putText(processed_image, f"Manual{i+1}", (x_min, y_min - 10),
                   cv2.FONT_HERSHEY_SIMPLEX, 0.6, color, 2)
    
    cv2.imwrite(output_path, processed_image)
    return True

def manual_bbox_tool():
    """主函数"""
    global current_boxes, temp_image, original_image

    print("=== 人工画框工具 ===")

    # 获取题型和处理方式
    question_type, pinyin_name = get_question_type()
    processing_type_id, processing_type = get_image_processing_type()

    # 设置路径
    types_dir = "types"
    question_dir = os.path.join(types_dir, pinyin_name)
    images_dir = os.path.join(question_dir, "images")

    if not os.path.exists(images_dir):
        print(f"错误：找不到图片目录 {images_dir}")
        return

    # 获取图片文件
    image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp']
    image_files = []
    for filename in os.listdir(images_dir):
        if any(filename.lower().endswith(ext) for ext in image_extensions):
            image_files.append(os.path.join(images_dir, filename))

    image_files.sort()
    print(f"找到 {len(image_files)} 张图片")

    # 创建输出目录
    timestamp = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
    output_dir = os.path.join(question_dir, "manual_result", f"images_{timestamp}")
    os.makedirs(output_dir, exist_ok=True)

    print(f"\n操作说明：")
    print("- 按住鼠标左键拖拽绘制边界框")
    print("- 按 'r' 重置当前图片的所有框")
    print("- 按 'n' 进入下一张图片")
    print("- 按 'q' 退出程序")
    print(f"\n开始处理，输出目录: {output_dir}")

    results = []

    for i, image_path in enumerate(image_files):
        filename = os.path.basename(image_path)
        print(f"\n处理第 {i+1}/{len(image_files)} 张图片: {filename}")

        # 重置变量
        current_boxes = []
        original_image = cv2.imread(image_path)
        temp_image = original_image.copy()

        # 创建窗口并设置鼠标回调
        cv2.namedWindow('Manual BBox Tool', cv2.WINDOW_NORMAL)
        cv2.setMouseCallback('Manual BBox Tool', mouse_callback)
        cv2.imshow('Manual BBox Tool', original_image)

        while True:
            key = cv2.waitKey(1) & 0xFF
            if key == ord('r'):  # 重置
                current_boxes = []
                temp_image = original_image.copy()
                cv2.imshow('Manual BBox Tool', temp_image)
                print("已重置所有边界框")
            elif key == ord('n'):  # 下一张
                break
            elif key == ord('q'):  # 退出
                cv2.destroyAllWindows()
                return

        # 保存结果
        if current_boxes:
            bbox_filename = f"{os.path.splitext(filename)[0]}_with_bbox{os.path.splitext(filename)[1]}"
            bbox_output_path = os.path.join(output_dir, bbox_filename)

            start_time = time.time()
            process_image_with_manual_boxes(image_path, current_boxes, bbox_output_path, processing_type_id)
            end_time = time.time()

            result = {
                'success': True,
                'filename': filename,
                'bbox_filename': bbox_filename,
                'boxes': current_boxes,
                'box_count': len(current_boxes),
                'processing_time': end_time - start_time
            }
            print(f"✓ 成功处理: {filename}, 绘制了 {len(current_boxes)} 个边界框")
        else:
            result = {
                'success': False,
                'filename': filename,
                'error': '未绘制任何边界框'
            }
            print(f"✗ 跳过: {filename} (未绘制边界框)")

        results.append(result)

    cv2.destroyAllWindows()

    # 生成summary.md
    generate_summary(results, output_dir, question_type, processing_type)

def generate_summary(results, output_dir, question_type, processing_type):
    """生成处理结果的summary.md文档"""
    summary_path = os.path.join(output_dir, "summary.md")

    with open(summary_path, 'w', encoding='utf-8') as f:
        f.write(f"# 人工画框处理结果\n\n")
        f.write(f"**题型**: {question_type}\n")
        f.write(f"**处理方式**: {processing_type}\n")
        f.write(f"**处理时间**: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

        success_count = sum(1 for r in results if r['success'])
        total_boxes = sum(r.get('box_count', 0) for r in results if r['success'])

        f.write(f"**处理统计**:\n")
        f.write(f"- 总图片数: {len(results)}\n")
        f.write(f"- 成功处理: {success_count}\n")
        f.write(f"- 总边界框数: {total_boxes}\n\n")

        f.write("## 处理结果\n\n")

        for i, result in enumerate(results, 1):
            f.write(f"### 第 {i} 张图片: {result['filename']}\n\n")

            if result['success']:
                f.write(f"![{result['bbox_filename']}]({result['bbox_filename']})\n\n")
                f.write("**人工画框内容:**\n")
                f.write(f"- 手动绘制边界框数量: {result['box_count']}\n")
                f.write(f"- 处理时间: {result.get('processing_time', 0):.2f}秒\n")

                if result['boxes']:
                    f.write("- 边界框坐标 (x_min, y_min, x_max, y_max):\n")
                    for j, (x_min, y_min, x_max, y_max) in enumerate(result['boxes'], 1):
                        width = x_max - x_min
                        height = y_max - y_min
                        area = width * height
                        f.write(f"  - 区域{j}: ({x_min}, {y_min}, {x_max}, {y_max}) - 尺寸: {width}×{height}, 面积: {area}\n")
            else:
                f.write(f"**错误**: {result.get('error', '未知错误')}\n")

            f.write("\n---\n\n")

    print(f"\n✓ 处理完成！summary.md 已保存至: {summary_path}")

if __name__ == "__main__":
    manual_bbox_tool()
