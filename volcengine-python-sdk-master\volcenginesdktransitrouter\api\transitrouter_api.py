# coding: utf-8

"""
    transitrouter

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


from __future__ import absolute_import

import re  # noqa: F401

# python 2 and python 3 compatibility library
import six

import volcenginesdkcore


class TRANSITROUTERApi(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    Ref: https://github.com/swagger-api/swagger-codegen
    """

    def __init__(self, api_client=None):
        if api_client is None:
            api_client = volcenginesdkcore.ApiClient()
        self.api_client = api_client

    def accept_shared_transit_router(self, body, **kwargs):  # noqa: E501
        """accept_shared_transit_router  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.accept_shared_transit_router(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param AcceptSharedTransitRouterRequest body: (required)
        :return: AcceptSharedTransitRouterResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.accept_shared_transit_router_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.accept_shared_transit_router_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def accept_shared_transit_router_with_http_info(self, body, **kwargs):  # noqa: E501
        """accept_shared_transit_router  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.accept_shared_transit_router_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param AcceptSharedTransitRouterRequest body: (required)
        :return: AcceptSharedTransitRouterResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method accept_shared_transit_router" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `accept_shared_transit_router`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/AcceptSharedTransitRouter/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='AcceptSharedTransitRouterResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def associate_transit_router_attachment_to_route_table(self, body, **kwargs):  # noqa: E501
        """associate_transit_router_attachment_to_route_table  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.associate_transit_router_attachment_to_route_table(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param AssociateTransitRouterAttachmentToRouteTableRequest body: (required)
        :return: AssociateTransitRouterAttachmentToRouteTableResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.associate_transit_router_attachment_to_route_table_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.associate_transit_router_attachment_to_route_table_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def associate_transit_router_attachment_to_route_table_with_http_info(self, body, **kwargs):  # noqa: E501
        """associate_transit_router_attachment_to_route_table  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.associate_transit_router_attachment_to_route_table_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param AssociateTransitRouterAttachmentToRouteTableRequest body: (required)
        :return: AssociateTransitRouterAttachmentToRouteTableResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method associate_transit_router_attachment_to_route_table" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `associate_transit_router_attachment_to_route_table`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/AssociateTransitRouterAttachmentToRouteTable/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='AssociateTransitRouterAttachmentToRouteTableResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def associate_transit_router_forward_policy_table_to_attachment(self, body, **kwargs):  # noqa: E501
        """associate_transit_router_forward_policy_table_to_attachment  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.associate_transit_router_forward_policy_table_to_attachment(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param AssociateTransitRouterForwardPolicyTableToAttachmentRequest body: (required)
        :return: AssociateTransitRouterForwardPolicyTableToAttachmentResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.associate_transit_router_forward_policy_table_to_attachment_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.associate_transit_router_forward_policy_table_to_attachment_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def associate_transit_router_forward_policy_table_to_attachment_with_http_info(self, body, **kwargs):  # noqa: E501
        """associate_transit_router_forward_policy_table_to_attachment  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.associate_transit_router_forward_policy_table_to_attachment_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param AssociateTransitRouterForwardPolicyTableToAttachmentRequest body: (required)
        :return: AssociateTransitRouterForwardPolicyTableToAttachmentResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method associate_transit_router_forward_policy_table_to_attachment" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `associate_transit_router_forward_policy_table_to_attachment`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/AssociateTransitRouterForwardPolicyTableToAttachment/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='AssociateTransitRouterForwardPolicyTableToAttachmentResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def associate_transit_router_route_policy_to_route_table(self, body, **kwargs):  # noqa: E501
        """associate_transit_router_route_policy_to_route_table  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.associate_transit_router_route_policy_to_route_table(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param AssociateTransitRouterRoutePolicyToRouteTableRequest body: (required)
        :return: AssociateTransitRouterRoutePolicyToRouteTableResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.associate_transit_router_route_policy_to_route_table_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.associate_transit_router_route_policy_to_route_table_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def associate_transit_router_route_policy_to_route_table_with_http_info(self, body, **kwargs):  # noqa: E501
        """associate_transit_router_route_policy_to_route_table  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.associate_transit_router_route_policy_to_route_table_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param AssociateTransitRouterRoutePolicyToRouteTableRequest body: (required)
        :return: AssociateTransitRouterRoutePolicyToRouteTableResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method associate_transit_router_route_policy_to_route_table" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `associate_transit_router_route_policy_to_route_table`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/AssociateTransitRouterRoutePolicyToRouteTable/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='AssociateTransitRouterRoutePolicyToRouteTableResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def associate_transit_router_traffic_qos_marking_policy_to_attachment(self, body, **kwargs):  # noqa: E501
        """associate_transit_router_traffic_qos_marking_policy_to_attachment  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.associate_transit_router_traffic_qos_marking_policy_to_attachment(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param AssociateTransitRouterTrafficQosMarkingPolicyToAttachmentRequest body: (required)
        :return: AssociateTransitRouterTrafficQosMarkingPolicyToAttachmentResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.associate_transit_router_traffic_qos_marking_policy_to_attachment_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.associate_transit_router_traffic_qos_marking_policy_to_attachment_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def associate_transit_router_traffic_qos_marking_policy_to_attachment_with_http_info(self, body, **kwargs):  # noqa: E501
        """associate_transit_router_traffic_qos_marking_policy_to_attachment  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.associate_transit_router_traffic_qos_marking_policy_to_attachment_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param AssociateTransitRouterTrafficQosMarkingPolicyToAttachmentRequest body: (required)
        :return: AssociateTransitRouterTrafficQosMarkingPolicyToAttachmentResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method associate_transit_router_traffic_qos_marking_policy_to_attachment" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `associate_transit_router_traffic_qos_marking_policy_to_attachment`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/AssociateTransitRouterTrafficQosMarkingPolicyToAttachment/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='AssociateTransitRouterTrafficQosMarkingPolicyToAttachmentResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def associate_transit_router_traffic_qos_queue_policy_to_attachment(self, body, **kwargs):  # noqa: E501
        """associate_transit_router_traffic_qos_queue_policy_to_attachment  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.associate_transit_router_traffic_qos_queue_policy_to_attachment(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param AssociateTransitRouterTrafficQosQueuePolicyToAttachmentRequest body: (required)
        :return: AssociateTransitRouterTrafficQosQueuePolicyToAttachmentResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.associate_transit_router_traffic_qos_queue_policy_to_attachment_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.associate_transit_router_traffic_qos_queue_policy_to_attachment_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def associate_transit_router_traffic_qos_queue_policy_to_attachment_with_http_info(self, body, **kwargs):  # noqa: E501
        """associate_transit_router_traffic_qos_queue_policy_to_attachment  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.associate_transit_router_traffic_qos_queue_policy_to_attachment_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param AssociateTransitRouterTrafficQosQueuePolicyToAttachmentRequest body: (required)
        :return: AssociateTransitRouterTrafficQosQueuePolicyToAttachmentResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method associate_transit_router_traffic_qos_queue_policy_to_attachment" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `associate_transit_router_traffic_qos_queue_policy_to_attachment`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/AssociateTransitRouterTrafficQosQueuePolicyToAttachment/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='AssociateTransitRouterTrafficQosQueuePolicyToAttachmentResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def create_transit_router(self, body, **kwargs):  # noqa: E501
        """create_transit_router  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_transit_router(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param CreateTransitRouterRequest body: (required)
        :return: CreateTransitRouterResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.create_transit_router_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.create_transit_router_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def create_transit_router_with_http_info(self, body, **kwargs):  # noqa: E501
        """create_transit_router  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_transit_router_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param CreateTransitRouterRequest body: (required)
        :return: CreateTransitRouterResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method create_transit_router" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `create_transit_router`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/CreateTransitRouter/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='CreateTransitRouterResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def create_transit_router_bandwidth_package(self, body, **kwargs):  # noqa: E501
        """create_transit_router_bandwidth_package  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_transit_router_bandwidth_package(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param CreateTransitRouterBandwidthPackageRequest body: (required)
        :return: CreateTransitRouterBandwidthPackageResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.create_transit_router_bandwidth_package_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.create_transit_router_bandwidth_package_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def create_transit_router_bandwidth_package_with_http_info(self, body, **kwargs):  # noqa: E501
        """create_transit_router_bandwidth_package  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_transit_router_bandwidth_package_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param CreateTransitRouterBandwidthPackageRequest body: (required)
        :return: CreateTransitRouterBandwidthPackageResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method create_transit_router_bandwidth_package" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `create_transit_router_bandwidth_package`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/CreateTransitRouterBandwidthPackage/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='CreateTransitRouterBandwidthPackageResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def create_transit_router_direct_connect_gateway_attachment(self, body, **kwargs):  # noqa: E501
        """create_transit_router_direct_connect_gateway_attachment  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_transit_router_direct_connect_gateway_attachment(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param CreateTransitRouterDirectConnectGatewayAttachmentRequest body: (required)
        :return: CreateTransitRouterDirectConnectGatewayAttachmentResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.create_transit_router_direct_connect_gateway_attachment_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.create_transit_router_direct_connect_gateway_attachment_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def create_transit_router_direct_connect_gateway_attachment_with_http_info(self, body, **kwargs):  # noqa: E501
        """create_transit_router_direct_connect_gateway_attachment  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_transit_router_direct_connect_gateway_attachment_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param CreateTransitRouterDirectConnectGatewayAttachmentRequest body: (required)
        :return: CreateTransitRouterDirectConnectGatewayAttachmentResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method create_transit_router_direct_connect_gateway_attachment" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `create_transit_router_direct_connect_gateway_attachment`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/CreateTransitRouterDirectConnectGatewayAttachment/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='CreateTransitRouterDirectConnectGatewayAttachmentResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def create_transit_router_forward_policy_entry(self, body, **kwargs):  # noqa: E501
        """create_transit_router_forward_policy_entry  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_transit_router_forward_policy_entry(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param CreateTransitRouterForwardPolicyEntryRequest body: (required)
        :return: CreateTransitRouterForwardPolicyEntryResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.create_transit_router_forward_policy_entry_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.create_transit_router_forward_policy_entry_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def create_transit_router_forward_policy_entry_with_http_info(self, body, **kwargs):  # noqa: E501
        """create_transit_router_forward_policy_entry  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_transit_router_forward_policy_entry_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param CreateTransitRouterForwardPolicyEntryRequest body: (required)
        :return: CreateTransitRouterForwardPolicyEntryResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method create_transit_router_forward_policy_entry" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `create_transit_router_forward_policy_entry`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/CreateTransitRouterForwardPolicyEntry/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='CreateTransitRouterForwardPolicyEntryResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def create_transit_router_forward_policy_table(self, body, **kwargs):  # noqa: E501
        """create_transit_router_forward_policy_table  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_transit_router_forward_policy_table(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param CreateTransitRouterForwardPolicyTableRequest body: (required)
        :return: CreateTransitRouterForwardPolicyTableResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.create_transit_router_forward_policy_table_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.create_transit_router_forward_policy_table_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def create_transit_router_forward_policy_table_with_http_info(self, body, **kwargs):  # noqa: E501
        """create_transit_router_forward_policy_table  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_transit_router_forward_policy_table_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param CreateTransitRouterForwardPolicyTableRequest body: (required)
        :return: CreateTransitRouterForwardPolicyTableResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method create_transit_router_forward_policy_table" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `create_transit_router_forward_policy_table`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/CreateTransitRouterForwardPolicyTable/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='CreateTransitRouterForwardPolicyTableResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def create_transit_router_grant_rule(self, body, **kwargs):  # noqa: E501
        """create_transit_router_grant_rule  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_transit_router_grant_rule(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param CreateTransitRouterGrantRuleRequest body: (required)
        :return: CreateTransitRouterGrantRuleResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.create_transit_router_grant_rule_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.create_transit_router_grant_rule_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def create_transit_router_grant_rule_with_http_info(self, body, **kwargs):  # noqa: E501
        """create_transit_router_grant_rule  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_transit_router_grant_rule_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param CreateTransitRouterGrantRuleRequest body: (required)
        :return: CreateTransitRouterGrantRuleResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method create_transit_router_grant_rule" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `create_transit_router_grant_rule`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/CreateTransitRouterGrantRule/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='CreateTransitRouterGrantRuleResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def create_transit_router_peer_attachment(self, body, **kwargs):  # noqa: E501
        """create_transit_router_peer_attachment  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_transit_router_peer_attachment(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param CreateTransitRouterPeerAttachmentRequest body: (required)
        :return: CreateTransitRouterPeerAttachmentResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.create_transit_router_peer_attachment_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.create_transit_router_peer_attachment_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def create_transit_router_peer_attachment_with_http_info(self, body, **kwargs):  # noqa: E501
        """create_transit_router_peer_attachment  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_transit_router_peer_attachment_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param CreateTransitRouterPeerAttachmentRequest body: (required)
        :return: CreateTransitRouterPeerAttachmentResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method create_transit_router_peer_attachment" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `create_transit_router_peer_attachment`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/CreateTransitRouterPeerAttachment/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='CreateTransitRouterPeerAttachmentResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def create_transit_router_route_entry(self, body, **kwargs):  # noqa: E501
        """create_transit_router_route_entry  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_transit_router_route_entry(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param CreateTransitRouterRouteEntryRequest body: (required)
        :return: CreateTransitRouterRouteEntryResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.create_transit_router_route_entry_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.create_transit_router_route_entry_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def create_transit_router_route_entry_with_http_info(self, body, **kwargs):  # noqa: E501
        """create_transit_router_route_entry  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_transit_router_route_entry_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param CreateTransitRouterRouteEntryRequest body: (required)
        :return: CreateTransitRouterRouteEntryResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method create_transit_router_route_entry" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `create_transit_router_route_entry`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/CreateTransitRouterRouteEntry/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='CreateTransitRouterRouteEntryResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def create_transit_router_route_policy_entry(self, body, **kwargs):  # noqa: E501
        """create_transit_router_route_policy_entry  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_transit_router_route_policy_entry(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param CreateTransitRouterRoutePolicyEntryRequest body: (required)
        :return: CreateTransitRouterRoutePolicyEntryResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.create_transit_router_route_policy_entry_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.create_transit_router_route_policy_entry_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def create_transit_router_route_policy_entry_with_http_info(self, body, **kwargs):  # noqa: E501
        """create_transit_router_route_policy_entry  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_transit_router_route_policy_entry_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param CreateTransitRouterRoutePolicyEntryRequest body: (required)
        :return: CreateTransitRouterRoutePolicyEntryResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method create_transit_router_route_policy_entry" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `create_transit_router_route_policy_entry`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/CreateTransitRouterRoutePolicyEntry/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='CreateTransitRouterRoutePolicyEntryResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def create_transit_router_route_policy_table(self, body, **kwargs):  # noqa: E501
        """create_transit_router_route_policy_table  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_transit_router_route_policy_table(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param CreateTransitRouterRoutePolicyTableRequest body: (required)
        :return: CreateTransitRouterRoutePolicyTableResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.create_transit_router_route_policy_table_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.create_transit_router_route_policy_table_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def create_transit_router_route_policy_table_with_http_info(self, body, **kwargs):  # noqa: E501
        """create_transit_router_route_policy_table  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_transit_router_route_policy_table_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param CreateTransitRouterRoutePolicyTableRequest body: (required)
        :return: CreateTransitRouterRoutePolicyTableResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method create_transit_router_route_policy_table" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `create_transit_router_route_policy_table`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/CreateTransitRouterRoutePolicyTable/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='CreateTransitRouterRoutePolicyTableResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def create_transit_router_route_table(self, body, **kwargs):  # noqa: E501
        """create_transit_router_route_table  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_transit_router_route_table(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param CreateTransitRouterRouteTableRequest body: (required)
        :return: CreateTransitRouterRouteTableResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.create_transit_router_route_table_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.create_transit_router_route_table_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def create_transit_router_route_table_with_http_info(self, body, **kwargs):  # noqa: E501
        """create_transit_router_route_table  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_transit_router_route_table_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param CreateTransitRouterRouteTableRequest body: (required)
        :return: CreateTransitRouterRouteTableResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method create_transit_router_route_table" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `create_transit_router_route_table`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/CreateTransitRouterRouteTable/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='CreateTransitRouterRouteTableResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def create_transit_router_traffic_qos_marking_entry(self, body, **kwargs):  # noqa: E501
        """create_transit_router_traffic_qos_marking_entry  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_transit_router_traffic_qos_marking_entry(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param CreateTransitRouterTrafficQosMarkingEntryRequest body: (required)
        :return: CreateTransitRouterTrafficQosMarkingEntryResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.create_transit_router_traffic_qos_marking_entry_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.create_transit_router_traffic_qos_marking_entry_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def create_transit_router_traffic_qos_marking_entry_with_http_info(self, body, **kwargs):  # noqa: E501
        """create_transit_router_traffic_qos_marking_entry  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_transit_router_traffic_qos_marking_entry_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param CreateTransitRouterTrafficQosMarkingEntryRequest body: (required)
        :return: CreateTransitRouterTrafficQosMarkingEntryResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method create_transit_router_traffic_qos_marking_entry" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `create_transit_router_traffic_qos_marking_entry`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/CreateTransitRouterTrafficQosMarkingEntry/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='CreateTransitRouterTrafficQosMarkingEntryResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def create_transit_router_traffic_qos_marking_policy(self, body, **kwargs):  # noqa: E501
        """create_transit_router_traffic_qos_marking_policy  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_transit_router_traffic_qos_marking_policy(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param CreateTransitRouterTrafficQosMarkingPolicyRequest body: (required)
        :return: CreateTransitRouterTrafficQosMarkingPolicyResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.create_transit_router_traffic_qos_marking_policy_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.create_transit_router_traffic_qos_marking_policy_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def create_transit_router_traffic_qos_marking_policy_with_http_info(self, body, **kwargs):  # noqa: E501
        """create_transit_router_traffic_qos_marking_policy  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_transit_router_traffic_qos_marking_policy_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param CreateTransitRouterTrafficQosMarkingPolicyRequest body: (required)
        :return: CreateTransitRouterTrafficQosMarkingPolicyResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method create_transit_router_traffic_qos_marking_policy" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `create_transit_router_traffic_qos_marking_policy`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/CreateTransitRouterTrafficQosMarkingPolicy/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='CreateTransitRouterTrafficQosMarkingPolicyResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def create_transit_router_traffic_qos_queue_entry(self, body, **kwargs):  # noqa: E501
        """create_transit_router_traffic_qos_queue_entry  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_transit_router_traffic_qos_queue_entry(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param CreateTransitRouterTrafficQosQueueEntryRequest body: (required)
        :return: CreateTransitRouterTrafficQosQueueEntryResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.create_transit_router_traffic_qos_queue_entry_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.create_transit_router_traffic_qos_queue_entry_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def create_transit_router_traffic_qos_queue_entry_with_http_info(self, body, **kwargs):  # noqa: E501
        """create_transit_router_traffic_qos_queue_entry  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_transit_router_traffic_qos_queue_entry_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param CreateTransitRouterTrafficQosQueueEntryRequest body: (required)
        :return: CreateTransitRouterTrafficQosQueueEntryResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method create_transit_router_traffic_qos_queue_entry" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `create_transit_router_traffic_qos_queue_entry`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/CreateTransitRouterTrafficQosQueueEntry/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='CreateTransitRouterTrafficQosQueueEntryResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def create_transit_router_traffic_qos_queue_policy(self, body, **kwargs):  # noqa: E501
        """create_transit_router_traffic_qos_queue_policy  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_transit_router_traffic_qos_queue_policy(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param CreateTransitRouterTrafficQosQueuePolicyRequest body: (required)
        :return: CreateTransitRouterTrafficQosQueuePolicyResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.create_transit_router_traffic_qos_queue_policy_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.create_transit_router_traffic_qos_queue_policy_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def create_transit_router_traffic_qos_queue_policy_with_http_info(self, body, **kwargs):  # noqa: E501
        """create_transit_router_traffic_qos_queue_policy  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_transit_router_traffic_qos_queue_policy_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param CreateTransitRouterTrafficQosQueuePolicyRequest body: (required)
        :return: CreateTransitRouterTrafficQosQueuePolicyResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method create_transit_router_traffic_qos_queue_policy" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `create_transit_router_traffic_qos_queue_policy`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/CreateTransitRouterTrafficQosQueuePolicy/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='CreateTransitRouterTrafficQosQueuePolicyResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def create_transit_router_vpc_attachment(self, body, **kwargs):  # noqa: E501
        """create_transit_router_vpc_attachment  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_transit_router_vpc_attachment(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param CreateTransitRouterVpcAttachmentRequest body: (required)
        :return: CreateTransitRouterVpcAttachmentResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.create_transit_router_vpc_attachment_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.create_transit_router_vpc_attachment_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def create_transit_router_vpc_attachment_with_http_info(self, body, **kwargs):  # noqa: E501
        """create_transit_router_vpc_attachment  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_transit_router_vpc_attachment_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param CreateTransitRouterVpcAttachmentRequest body: (required)
        :return: CreateTransitRouterVpcAttachmentResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method create_transit_router_vpc_attachment" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `create_transit_router_vpc_attachment`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/CreateTransitRouterVpcAttachment/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='CreateTransitRouterVpcAttachmentResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def create_transit_router_vpn_attachment(self, body, **kwargs):  # noqa: E501
        """create_transit_router_vpn_attachment  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_transit_router_vpn_attachment(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param CreateTransitRouterVpnAttachmentRequest body: (required)
        :return: CreateTransitRouterVpnAttachmentResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.create_transit_router_vpn_attachment_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.create_transit_router_vpn_attachment_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def create_transit_router_vpn_attachment_with_http_info(self, body, **kwargs):  # noqa: E501
        """create_transit_router_vpn_attachment  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_transit_router_vpn_attachment_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param CreateTransitRouterVpnAttachmentRequest body: (required)
        :return: CreateTransitRouterVpnAttachmentResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method create_transit_router_vpn_attachment" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `create_transit_router_vpn_attachment`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/CreateTransitRouterVpnAttachment/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='CreateTransitRouterVpnAttachmentResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def delete_transit_router(self, body, **kwargs):  # noqa: E501
        """delete_transit_router  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_transit_router(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DeleteTransitRouterRequest body: (required)
        :return: DeleteTransitRouterResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.delete_transit_router_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.delete_transit_router_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def delete_transit_router_with_http_info(self, body, **kwargs):  # noqa: E501
        """delete_transit_router  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_transit_router_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DeleteTransitRouterRequest body: (required)
        :return: DeleteTransitRouterResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method delete_transit_router" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `delete_transit_router`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/DeleteTransitRouter/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='DeleteTransitRouterResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def delete_transit_router_bandwidth_package(self, body, **kwargs):  # noqa: E501
        """delete_transit_router_bandwidth_package  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_transit_router_bandwidth_package(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DeleteTransitRouterBandwidthPackageRequest body: (required)
        :return: DeleteTransitRouterBandwidthPackageResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.delete_transit_router_bandwidth_package_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.delete_transit_router_bandwidth_package_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def delete_transit_router_bandwidth_package_with_http_info(self, body, **kwargs):  # noqa: E501
        """delete_transit_router_bandwidth_package  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_transit_router_bandwidth_package_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DeleteTransitRouterBandwidthPackageRequest body: (required)
        :return: DeleteTransitRouterBandwidthPackageResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method delete_transit_router_bandwidth_package" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `delete_transit_router_bandwidth_package`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/DeleteTransitRouterBandwidthPackage/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='DeleteTransitRouterBandwidthPackageResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def delete_transit_router_direct_connect_gateway_attachment(self, body, **kwargs):  # noqa: E501
        """delete_transit_router_direct_connect_gateway_attachment  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_transit_router_direct_connect_gateway_attachment(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DeleteTransitRouterDirectConnectGatewayAttachmentRequest body: (required)
        :return: DeleteTransitRouterDirectConnectGatewayAttachmentResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.delete_transit_router_direct_connect_gateway_attachment_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.delete_transit_router_direct_connect_gateway_attachment_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def delete_transit_router_direct_connect_gateway_attachment_with_http_info(self, body, **kwargs):  # noqa: E501
        """delete_transit_router_direct_connect_gateway_attachment  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_transit_router_direct_connect_gateway_attachment_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DeleteTransitRouterDirectConnectGatewayAttachmentRequest body: (required)
        :return: DeleteTransitRouterDirectConnectGatewayAttachmentResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method delete_transit_router_direct_connect_gateway_attachment" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `delete_transit_router_direct_connect_gateway_attachment`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/DeleteTransitRouterDirectConnectGatewayAttachment/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='DeleteTransitRouterDirectConnectGatewayAttachmentResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def delete_transit_router_forward_policy_entry(self, body, **kwargs):  # noqa: E501
        """delete_transit_router_forward_policy_entry  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_transit_router_forward_policy_entry(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DeleteTransitRouterForwardPolicyEntryRequest body: (required)
        :return: DeleteTransitRouterForwardPolicyEntryResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.delete_transit_router_forward_policy_entry_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.delete_transit_router_forward_policy_entry_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def delete_transit_router_forward_policy_entry_with_http_info(self, body, **kwargs):  # noqa: E501
        """delete_transit_router_forward_policy_entry  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_transit_router_forward_policy_entry_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DeleteTransitRouterForwardPolicyEntryRequest body: (required)
        :return: DeleteTransitRouterForwardPolicyEntryResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method delete_transit_router_forward_policy_entry" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `delete_transit_router_forward_policy_entry`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/DeleteTransitRouterForwardPolicyEntry/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='DeleteTransitRouterForwardPolicyEntryResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def delete_transit_router_forward_policy_table(self, body, **kwargs):  # noqa: E501
        """delete_transit_router_forward_policy_table  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_transit_router_forward_policy_table(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DeleteTransitRouterForwardPolicyTableRequest body: (required)
        :return: DeleteTransitRouterForwardPolicyTableResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.delete_transit_router_forward_policy_table_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.delete_transit_router_forward_policy_table_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def delete_transit_router_forward_policy_table_with_http_info(self, body, **kwargs):  # noqa: E501
        """delete_transit_router_forward_policy_table  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_transit_router_forward_policy_table_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DeleteTransitRouterForwardPolicyTableRequest body: (required)
        :return: DeleteTransitRouterForwardPolicyTableResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method delete_transit_router_forward_policy_table" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `delete_transit_router_forward_policy_table`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/DeleteTransitRouterForwardPolicyTable/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='DeleteTransitRouterForwardPolicyTableResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def delete_transit_router_grant_rule(self, body, **kwargs):  # noqa: E501
        """delete_transit_router_grant_rule  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_transit_router_grant_rule(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DeleteTransitRouterGrantRuleRequest body: (required)
        :return: DeleteTransitRouterGrantRuleResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.delete_transit_router_grant_rule_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.delete_transit_router_grant_rule_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def delete_transit_router_grant_rule_with_http_info(self, body, **kwargs):  # noqa: E501
        """delete_transit_router_grant_rule  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_transit_router_grant_rule_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DeleteTransitRouterGrantRuleRequest body: (required)
        :return: DeleteTransitRouterGrantRuleResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method delete_transit_router_grant_rule" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `delete_transit_router_grant_rule`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/DeleteTransitRouterGrantRule/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='DeleteTransitRouterGrantRuleResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def delete_transit_router_peer_attachment(self, body, **kwargs):  # noqa: E501
        """delete_transit_router_peer_attachment  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_transit_router_peer_attachment(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DeleteTransitRouterPeerAttachmentRequest body: (required)
        :return: DeleteTransitRouterPeerAttachmentResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.delete_transit_router_peer_attachment_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.delete_transit_router_peer_attachment_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def delete_transit_router_peer_attachment_with_http_info(self, body, **kwargs):  # noqa: E501
        """delete_transit_router_peer_attachment  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_transit_router_peer_attachment_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DeleteTransitRouterPeerAttachmentRequest body: (required)
        :return: DeleteTransitRouterPeerAttachmentResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method delete_transit_router_peer_attachment" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `delete_transit_router_peer_attachment`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/DeleteTransitRouterPeerAttachment/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='DeleteTransitRouterPeerAttachmentResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def delete_transit_router_route_entry(self, body, **kwargs):  # noqa: E501
        """delete_transit_router_route_entry  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_transit_router_route_entry(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DeleteTransitRouterRouteEntryRequest body: (required)
        :return: DeleteTransitRouterRouteEntryResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.delete_transit_router_route_entry_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.delete_transit_router_route_entry_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def delete_transit_router_route_entry_with_http_info(self, body, **kwargs):  # noqa: E501
        """delete_transit_router_route_entry  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_transit_router_route_entry_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DeleteTransitRouterRouteEntryRequest body: (required)
        :return: DeleteTransitRouterRouteEntryResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method delete_transit_router_route_entry" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `delete_transit_router_route_entry`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/DeleteTransitRouterRouteEntry/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='DeleteTransitRouterRouteEntryResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def delete_transit_router_route_policy_entry(self, body, **kwargs):  # noqa: E501
        """delete_transit_router_route_policy_entry  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_transit_router_route_policy_entry(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DeleteTransitRouterRoutePolicyEntryRequest body: (required)
        :return: DeleteTransitRouterRoutePolicyEntryResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.delete_transit_router_route_policy_entry_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.delete_transit_router_route_policy_entry_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def delete_transit_router_route_policy_entry_with_http_info(self, body, **kwargs):  # noqa: E501
        """delete_transit_router_route_policy_entry  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_transit_router_route_policy_entry_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DeleteTransitRouterRoutePolicyEntryRequest body: (required)
        :return: DeleteTransitRouterRoutePolicyEntryResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method delete_transit_router_route_policy_entry" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `delete_transit_router_route_policy_entry`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/DeleteTransitRouterRoutePolicyEntry/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='DeleteTransitRouterRoutePolicyEntryResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def delete_transit_router_route_policy_table(self, body, **kwargs):  # noqa: E501
        """delete_transit_router_route_policy_table  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_transit_router_route_policy_table(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DeleteTransitRouterRoutePolicyTableRequest body: (required)
        :return: DeleteTransitRouterRoutePolicyTableResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.delete_transit_router_route_policy_table_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.delete_transit_router_route_policy_table_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def delete_transit_router_route_policy_table_with_http_info(self, body, **kwargs):  # noqa: E501
        """delete_transit_router_route_policy_table  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_transit_router_route_policy_table_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DeleteTransitRouterRoutePolicyTableRequest body: (required)
        :return: DeleteTransitRouterRoutePolicyTableResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method delete_transit_router_route_policy_table" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `delete_transit_router_route_policy_table`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/DeleteTransitRouterRoutePolicyTable/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='DeleteTransitRouterRoutePolicyTableResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def delete_transit_router_route_table(self, body, **kwargs):  # noqa: E501
        """delete_transit_router_route_table  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_transit_router_route_table(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DeleteTransitRouterRouteTableRequest body: (required)
        :return: DeleteTransitRouterRouteTableResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.delete_transit_router_route_table_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.delete_transit_router_route_table_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def delete_transit_router_route_table_with_http_info(self, body, **kwargs):  # noqa: E501
        """delete_transit_router_route_table  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_transit_router_route_table_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DeleteTransitRouterRouteTableRequest body: (required)
        :return: DeleteTransitRouterRouteTableResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method delete_transit_router_route_table" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `delete_transit_router_route_table`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/DeleteTransitRouterRouteTable/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='DeleteTransitRouterRouteTableResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def delete_transit_router_traffic_qos_marking_entry(self, body, **kwargs):  # noqa: E501
        """delete_transit_router_traffic_qos_marking_entry  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_transit_router_traffic_qos_marking_entry(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DeleteTransitRouterTrafficQosMarkingEntryRequest body: (required)
        :return: DeleteTransitRouterTrafficQosMarkingEntryResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.delete_transit_router_traffic_qos_marking_entry_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.delete_transit_router_traffic_qos_marking_entry_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def delete_transit_router_traffic_qos_marking_entry_with_http_info(self, body, **kwargs):  # noqa: E501
        """delete_transit_router_traffic_qos_marking_entry  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_transit_router_traffic_qos_marking_entry_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DeleteTransitRouterTrafficQosMarkingEntryRequest body: (required)
        :return: DeleteTransitRouterTrafficQosMarkingEntryResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method delete_transit_router_traffic_qos_marking_entry" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `delete_transit_router_traffic_qos_marking_entry`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/DeleteTransitRouterTrafficQosMarkingEntry/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='DeleteTransitRouterTrafficQosMarkingEntryResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def delete_transit_router_traffic_qos_marking_policy(self, body, **kwargs):  # noqa: E501
        """delete_transit_router_traffic_qos_marking_policy  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_transit_router_traffic_qos_marking_policy(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DeleteTransitRouterTrafficQosMarkingPolicyRequest body: (required)
        :return: DeleteTransitRouterTrafficQosMarkingPolicyResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.delete_transit_router_traffic_qos_marking_policy_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.delete_transit_router_traffic_qos_marking_policy_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def delete_transit_router_traffic_qos_marking_policy_with_http_info(self, body, **kwargs):  # noqa: E501
        """delete_transit_router_traffic_qos_marking_policy  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_transit_router_traffic_qos_marking_policy_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DeleteTransitRouterTrafficQosMarkingPolicyRequest body: (required)
        :return: DeleteTransitRouterTrafficQosMarkingPolicyResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method delete_transit_router_traffic_qos_marking_policy" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `delete_transit_router_traffic_qos_marking_policy`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/DeleteTransitRouterTrafficQosMarkingPolicy/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='DeleteTransitRouterTrafficQosMarkingPolicyResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def delete_transit_router_traffic_qos_queue_entry(self, body, **kwargs):  # noqa: E501
        """delete_transit_router_traffic_qos_queue_entry  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_transit_router_traffic_qos_queue_entry(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DeleteTransitRouterTrafficQosQueueEntryRequest body: (required)
        :return: DeleteTransitRouterTrafficQosQueueEntryResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.delete_transit_router_traffic_qos_queue_entry_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.delete_transit_router_traffic_qos_queue_entry_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def delete_transit_router_traffic_qos_queue_entry_with_http_info(self, body, **kwargs):  # noqa: E501
        """delete_transit_router_traffic_qos_queue_entry  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_transit_router_traffic_qos_queue_entry_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DeleteTransitRouterTrafficQosQueueEntryRequest body: (required)
        :return: DeleteTransitRouterTrafficQosQueueEntryResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method delete_transit_router_traffic_qos_queue_entry" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `delete_transit_router_traffic_qos_queue_entry`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/DeleteTransitRouterTrafficQosQueueEntry/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='DeleteTransitRouterTrafficQosQueueEntryResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def delete_transit_router_traffic_qos_queue_policy(self, body, **kwargs):  # noqa: E501
        """delete_transit_router_traffic_qos_queue_policy  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_transit_router_traffic_qos_queue_policy(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DeleteTransitRouterTrafficQosQueuePolicyRequest body: (required)
        :return: DeleteTransitRouterTrafficQosQueuePolicyResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.delete_transit_router_traffic_qos_queue_policy_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.delete_transit_router_traffic_qos_queue_policy_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def delete_transit_router_traffic_qos_queue_policy_with_http_info(self, body, **kwargs):  # noqa: E501
        """delete_transit_router_traffic_qos_queue_policy  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_transit_router_traffic_qos_queue_policy_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DeleteTransitRouterTrafficQosQueuePolicyRequest body: (required)
        :return: DeleteTransitRouterTrafficQosQueuePolicyResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method delete_transit_router_traffic_qos_queue_policy" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `delete_transit_router_traffic_qos_queue_policy`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/DeleteTransitRouterTrafficQosQueuePolicy/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='DeleteTransitRouterTrafficQosQueuePolicyResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def delete_transit_router_vpc_attachment(self, body, **kwargs):  # noqa: E501
        """delete_transit_router_vpc_attachment  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_transit_router_vpc_attachment(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DeleteTransitRouterVpcAttachmentRequest body: (required)
        :return: DeleteTransitRouterVpcAttachmentResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.delete_transit_router_vpc_attachment_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.delete_transit_router_vpc_attachment_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def delete_transit_router_vpc_attachment_with_http_info(self, body, **kwargs):  # noqa: E501
        """delete_transit_router_vpc_attachment  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_transit_router_vpc_attachment_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DeleteTransitRouterVpcAttachmentRequest body: (required)
        :return: DeleteTransitRouterVpcAttachmentResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method delete_transit_router_vpc_attachment" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `delete_transit_router_vpc_attachment`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/DeleteTransitRouterVpcAttachment/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='DeleteTransitRouterVpcAttachmentResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def delete_transit_router_vpn_attachment(self, body, **kwargs):  # noqa: E501
        """delete_transit_router_vpn_attachment  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_transit_router_vpn_attachment(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DeleteTransitRouterVpnAttachmentRequest body: (required)
        :return: DeleteTransitRouterVpnAttachmentResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.delete_transit_router_vpn_attachment_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.delete_transit_router_vpn_attachment_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def delete_transit_router_vpn_attachment_with_http_info(self, body, **kwargs):  # noqa: E501
        """delete_transit_router_vpn_attachment  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_transit_router_vpn_attachment_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DeleteTransitRouterVpnAttachmentRequest body: (required)
        :return: DeleteTransitRouterVpnAttachmentResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method delete_transit_router_vpn_attachment" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `delete_transit_router_vpn_attachment`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/DeleteTransitRouterVpnAttachment/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='DeleteTransitRouterVpnAttachmentResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def describe_transit_router_attachments(self, body, **kwargs):  # noqa: E501
        """describe_transit_router_attachments  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.describe_transit_router_attachments(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DescribeTransitRouterAttachmentsRequest body: (required)
        :return: DescribeTransitRouterAttachmentsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.describe_transit_router_attachments_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.describe_transit_router_attachments_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def describe_transit_router_attachments_with_http_info(self, body, **kwargs):  # noqa: E501
        """describe_transit_router_attachments  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.describe_transit_router_attachments_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DescribeTransitRouterAttachmentsRequest body: (required)
        :return: DescribeTransitRouterAttachmentsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method describe_transit_router_attachments" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `describe_transit_router_attachments`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/DescribeTransitRouterAttachments/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='DescribeTransitRouterAttachmentsResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def describe_transit_router_bandwidth_packages(self, body, **kwargs):  # noqa: E501
        """describe_transit_router_bandwidth_packages  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.describe_transit_router_bandwidth_packages(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DescribeTransitRouterBandwidthPackagesRequest body: (required)
        :return: DescribeTransitRouterBandwidthPackagesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.describe_transit_router_bandwidth_packages_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.describe_transit_router_bandwidth_packages_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def describe_transit_router_bandwidth_packages_with_http_info(self, body, **kwargs):  # noqa: E501
        """describe_transit_router_bandwidth_packages  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.describe_transit_router_bandwidth_packages_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DescribeTransitRouterBandwidthPackagesRequest body: (required)
        :return: DescribeTransitRouterBandwidthPackagesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method describe_transit_router_bandwidth_packages" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `describe_transit_router_bandwidth_packages`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/DescribeTransitRouterBandwidthPackages/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='DescribeTransitRouterBandwidthPackagesResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def describe_transit_router_bandwidth_packages_billing(self, body, **kwargs):  # noqa: E501
        """describe_transit_router_bandwidth_packages_billing  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.describe_transit_router_bandwidth_packages_billing(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DescribeTransitRouterBandwidthPackagesBillingRequest body: (required)
        :return: DescribeTransitRouterBandwidthPackagesBillingResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.describe_transit_router_bandwidth_packages_billing_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.describe_transit_router_bandwidth_packages_billing_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def describe_transit_router_bandwidth_packages_billing_with_http_info(self, body, **kwargs):  # noqa: E501
        """describe_transit_router_bandwidth_packages_billing  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.describe_transit_router_bandwidth_packages_billing_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DescribeTransitRouterBandwidthPackagesBillingRequest body: (required)
        :return: DescribeTransitRouterBandwidthPackagesBillingResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method describe_transit_router_bandwidth_packages_billing" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `describe_transit_router_bandwidth_packages_billing`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/DescribeTransitRouterBandwidthPackagesBilling/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='DescribeTransitRouterBandwidthPackagesBillingResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def describe_transit_router_direct_connect_gateway_attachments(self, body, **kwargs):  # noqa: E501
        """describe_transit_router_direct_connect_gateway_attachments  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.describe_transit_router_direct_connect_gateway_attachments(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DescribeTransitRouterDirectConnectGatewayAttachmentsRequest body: (required)
        :return: DescribeTransitRouterDirectConnectGatewayAttachmentsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.describe_transit_router_direct_connect_gateway_attachments_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.describe_transit_router_direct_connect_gateway_attachments_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def describe_transit_router_direct_connect_gateway_attachments_with_http_info(self, body, **kwargs):  # noqa: E501
        """describe_transit_router_direct_connect_gateway_attachments  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.describe_transit_router_direct_connect_gateway_attachments_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DescribeTransitRouterDirectConnectGatewayAttachmentsRequest body: (required)
        :return: DescribeTransitRouterDirectConnectGatewayAttachmentsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method describe_transit_router_direct_connect_gateway_attachments" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `describe_transit_router_direct_connect_gateway_attachments`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/DescribeTransitRouterDirectConnectGatewayAttachments/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='DescribeTransitRouterDirectConnectGatewayAttachmentsResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def describe_transit_router_forward_policy_entries(self, body, **kwargs):  # noqa: E501
        """describe_transit_router_forward_policy_entries  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.describe_transit_router_forward_policy_entries(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DescribeTransitRouterForwardPolicyEntriesRequest body: (required)
        :return: DescribeTransitRouterForwardPolicyEntriesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.describe_transit_router_forward_policy_entries_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.describe_transit_router_forward_policy_entries_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def describe_transit_router_forward_policy_entries_with_http_info(self, body, **kwargs):  # noqa: E501
        """describe_transit_router_forward_policy_entries  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.describe_transit_router_forward_policy_entries_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DescribeTransitRouterForwardPolicyEntriesRequest body: (required)
        :return: DescribeTransitRouterForwardPolicyEntriesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method describe_transit_router_forward_policy_entries" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `describe_transit_router_forward_policy_entries`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/DescribeTransitRouterForwardPolicyEntries/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='DescribeTransitRouterForwardPolicyEntriesResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def describe_transit_router_forward_policy_tables(self, body, **kwargs):  # noqa: E501
        """describe_transit_router_forward_policy_tables  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.describe_transit_router_forward_policy_tables(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DescribeTransitRouterForwardPolicyTablesRequest body: (required)
        :return: DescribeTransitRouterForwardPolicyTablesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.describe_transit_router_forward_policy_tables_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.describe_transit_router_forward_policy_tables_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def describe_transit_router_forward_policy_tables_with_http_info(self, body, **kwargs):  # noqa: E501
        """describe_transit_router_forward_policy_tables  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.describe_transit_router_forward_policy_tables_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DescribeTransitRouterForwardPolicyTablesRequest body: (required)
        :return: DescribeTransitRouterForwardPolicyTablesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method describe_transit_router_forward_policy_tables" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `describe_transit_router_forward_policy_tables`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/DescribeTransitRouterForwardPolicyTables/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='DescribeTransitRouterForwardPolicyTablesResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def describe_transit_router_grant_rules(self, body, **kwargs):  # noqa: E501
        """describe_transit_router_grant_rules  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.describe_transit_router_grant_rules(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DescribeTransitRouterGrantRulesRequest body: (required)
        :return: DescribeTransitRouterGrantRulesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.describe_transit_router_grant_rules_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.describe_transit_router_grant_rules_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def describe_transit_router_grant_rules_with_http_info(self, body, **kwargs):  # noqa: E501
        """describe_transit_router_grant_rules  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.describe_transit_router_grant_rules_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DescribeTransitRouterGrantRulesRequest body: (required)
        :return: DescribeTransitRouterGrantRulesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method describe_transit_router_grant_rules" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `describe_transit_router_grant_rules`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/DescribeTransitRouterGrantRules/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='DescribeTransitRouterGrantRulesResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def describe_transit_router_peer_attachments(self, body, **kwargs):  # noqa: E501
        """describe_transit_router_peer_attachments  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.describe_transit_router_peer_attachments(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DescribeTransitRouterPeerAttachmentsRequest body: (required)
        :return: DescribeTransitRouterPeerAttachmentsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.describe_transit_router_peer_attachments_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.describe_transit_router_peer_attachments_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def describe_transit_router_peer_attachments_with_http_info(self, body, **kwargs):  # noqa: E501
        """describe_transit_router_peer_attachments  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.describe_transit_router_peer_attachments_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DescribeTransitRouterPeerAttachmentsRequest body: (required)
        :return: DescribeTransitRouterPeerAttachmentsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method describe_transit_router_peer_attachments" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `describe_transit_router_peer_attachments`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/DescribeTransitRouterPeerAttachments/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='DescribeTransitRouterPeerAttachmentsResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def describe_transit_router_regions(self, body, **kwargs):  # noqa: E501
        """describe_transit_router_regions  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.describe_transit_router_regions(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DescribeTransitRouterRegionsRequest body: (required)
        :return: DescribeTransitRouterRegionsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.describe_transit_router_regions_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.describe_transit_router_regions_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def describe_transit_router_regions_with_http_info(self, body, **kwargs):  # noqa: E501
        """describe_transit_router_regions  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.describe_transit_router_regions_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DescribeTransitRouterRegionsRequest body: (required)
        :return: DescribeTransitRouterRegionsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method describe_transit_router_regions" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `describe_transit_router_regions`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/DescribeTransitRouterRegions/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='DescribeTransitRouterRegionsResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def describe_transit_router_route_entries(self, body, **kwargs):  # noqa: E501
        """describe_transit_router_route_entries  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.describe_transit_router_route_entries(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DescribeTransitRouterRouteEntriesRequest body: (required)
        :return: DescribeTransitRouterRouteEntriesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.describe_transit_router_route_entries_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.describe_transit_router_route_entries_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def describe_transit_router_route_entries_with_http_info(self, body, **kwargs):  # noqa: E501
        """describe_transit_router_route_entries  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.describe_transit_router_route_entries_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DescribeTransitRouterRouteEntriesRequest body: (required)
        :return: DescribeTransitRouterRouteEntriesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method describe_transit_router_route_entries" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `describe_transit_router_route_entries`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/DescribeTransitRouterRouteEntries/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='DescribeTransitRouterRouteEntriesResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def describe_transit_router_route_policy_entries(self, body, **kwargs):  # noqa: E501
        """describe_transit_router_route_policy_entries  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.describe_transit_router_route_policy_entries(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DescribeTransitRouterRoutePolicyEntriesRequest body: (required)
        :return: DescribeTransitRouterRoutePolicyEntriesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.describe_transit_router_route_policy_entries_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.describe_transit_router_route_policy_entries_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def describe_transit_router_route_policy_entries_with_http_info(self, body, **kwargs):  # noqa: E501
        """describe_transit_router_route_policy_entries  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.describe_transit_router_route_policy_entries_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DescribeTransitRouterRoutePolicyEntriesRequest body: (required)
        :return: DescribeTransitRouterRoutePolicyEntriesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method describe_transit_router_route_policy_entries" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `describe_transit_router_route_policy_entries`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/DescribeTransitRouterRoutePolicyEntries/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='DescribeTransitRouterRoutePolicyEntriesResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def describe_transit_router_route_policy_tables(self, body, **kwargs):  # noqa: E501
        """describe_transit_router_route_policy_tables  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.describe_transit_router_route_policy_tables(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DescribeTransitRouterRoutePolicyTablesRequest body: (required)
        :return: DescribeTransitRouterRoutePolicyTablesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.describe_transit_router_route_policy_tables_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.describe_transit_router_route_policy_tables_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def describe_transit_router_route_policy_tables_with_http_info(self, body, **kwargs):  # noqa: E501
        """describe_transit_router_route_policy_tables  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.describe_transit_router_route_policy_tables_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DescribeTransitRouterRoutePolicyTablesRequest body: (required)
        :return: DescribeTransitRouterRoutePolicyTablesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method describe_transit_router_route_policy_tables" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `describe_transit_router_route_policy_tables`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/DescribeTransitRouterRoutePolicyTables/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='DescribeTransitRouterRoutePolicyTablesResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def describe_transit_router_route_table_associations(self, body, **kwargs):  # noqa: E501
        """describe_transit_router_route_table_associations  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.describe_transit_router_route_table_associations(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DescribeTransitRouterRouteTableAssociationsRequest body: (required)
        :return: DescribeTransitRouterRouteTableAssociationsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.describe_transit_router_route_table_associations_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.describe_transit_router_route_table_associations_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def describe_transit_router_route_table_associations_with_http_info(self, body, **kwargs):  # noqa: E501
        """describe_transit_router_route_table_associations  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.describe_transit_router_route_table_associations_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DescribeTransitRouterRouteTableAssociationsRequest body: (required)
        :return: DescribeTransitRouterRouteTableAssociationsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method describe_transit_router_route_table_associations" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `describe_transit_router_route_table_associations`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/DescribeTransitRouterRouteTableAssociations/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='DescribeTransitRouterRouteTableAssociationsResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def describe_transit_router_route_table_propagations(self, body, **kwargs):  # noqa: E501
        """describe_transit_router_route_table_propagations  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.describe_transit_router_route_table_propagations(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DescribeTransitRouterRouteTablePropagationsRequest body: (required)
        :return: DescribeTransitRouterRouteTablePropagationsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.describe_transit_router_route_table_propagations_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.describe_transit_router_route_table_propagations_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def describe_transit_router_route_table_propagations_with_http_info(self, body, **kwargs):  # noqa: E501
        """describe_transit_router_route_table_propagations  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.describe_transit_router_route_table_propagations_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DescribeTransitRouterRouteTablePropagationsRequest body: (required)
        :return: DescribeTransitRouterRouteTablePropagationsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method describe_transit_router_route_table_propagations" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `describe_transit_router_route_table_propagations`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/DescribeTransitRouterRouteTablePropagations/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='DescribeTransitRouterRouteTablePropagationsResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def describe_transit_router_route_tables(self, body, **kwargs):  # noqa: E501
        """describe_transit_router_route_tables  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.describe_transit_router_route_tables(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DescribeTransitRouterRouteTablesRequest body: (required)
        :return: DescribeTransitRouterRouteTablesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.describe_transit_router_route_tables_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.describe_transit_router_route_tables_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def describe_transit_router_route_tables_with_http_info(self, body, **kwargs):  # noqa: E501
        """describe_transit_router_route_tables  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.describe_transit_router_route_tables_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DescribeTransitRouterRouteTablesRequest body: (required)
        :return: DescribeTransitRouterRouteTablesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method describe_transit_router_route_tables" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `describe_transit_router_route_tables`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/DescribeTransitRouterRouteTables/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='DescribeTransitRouterRouteTablesResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def describe_transit_router_traffic_qos_marking_entries(self, body, **kwargs):  # noqa: E501
        """describe_transit_router_traffic_qos_marking_entries  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.describe_transit_router_traffic_qos_marking_entries(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DescribeTransitRouterTrafficQosMarkingEntriesRequest body: (required)
        :return: DescribeTransitRouterTrafficQosMarkingEntriesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.describe_transit_router_traffic_qos_marking_entries_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.describe_transit_router_traffic_qos_marking_entries_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def describe_transit_router_traffic_qos_marking_entries_with_http_info(self, body, **kwargs):  # noqa: E501
        """describe_transit_router_traffic_qos_marking_entries  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.describe_transit_router_traffic_qos_marking_entries_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DescribeTransitRouterTrafficQosMarkingEntriesRequest body: (required)
        :return: DescribeTransitRouterTrafficQosMarkingEntriesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method describe_transit_router_traffic_qos_marking_entries" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `describe_transit_router_traffic_qos_marking_entries`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/DescribeTransitRouterTrafficQosMarkingEntries/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='DescribeTransitRouterTrafficQosMarkingEntriesResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def describe_transit_router_traffic_qos_marking_policies(self, body, **kwargs):  # noqa: E501
        """describe_transit_router_traffic_qos_marking_policies  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.describe_transit_router_traffic_qos_marking_policies(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DescribeTransitRouterTrafficQosMarkingPoliciesRequest body: (required)
        :return: DescribeTransitRouterTrafficQosMarkingPoliciesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.describe_transit_router_traffic_qos_marking_policies_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.describe_transit_router_traffic_qos_marking_policies_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def describe_transit_router_traffic_qos_marking_policies_with_http_info(self, body, **kwargs):  # noqa: E501
        """describe_transit_router_traffic_qos_marking_policies  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.describe_transit_router_traffic_qos_marking_policies_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DescribeTransitRouterTrafficQosMarkingPoliciesRequest body: (required)
        :return: DescribeTransitRouterTrafficQosMarkingPoliciesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method describe_transit_router_traffic_qos_marking_policies" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `describe_transit_router_traffic_qos_marking_policies`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/DescribeTransitRouterTrafficQosMarkingPolicies/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='DescribeTransitRouterTrafficQosMarkingPoliciesResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def describe_transit_router_traffic_qos_queue_entries(self, body, **kwargs):  # noqa: E501
        """describe_transit_router_traffic_qos_queue_entries  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.describe_transit_router_traffic_qos_queue_entries(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DescribeTransitRouterTrafficQosQueueEntriesRequest body: (required)
        :return: DescribeTransitRouterTrafficQosQueueEntriesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.describe_transit_router_traffic_qos_queue_entries_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.describe_transit_router_traffic_qos_queue_entries_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def describe_transit_router_traffic_qos_queue_entries_with_http_info(self, body, **kwargs):  # noqa: E501
        """describe_transit_router_traffic_qos_queue_entries  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.describe_transit_router_traffic_qos_queue_entries_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DescribeTransitRouterTrafficQosQueueEntriesRequest body: (required)
        :return: DescribeTransitRouterTrafficQosQueueEntriesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method describe_transit_router_traffic_qos_queue_entries" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `describe_transit_router_traffic_qos_queue_entries`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/DescribeTransitRouterTrafficQosQueueEntries/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='DescribeTransitRouterTrafficQosQueueEntriesResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def describe_transit_router_traffic_qos_queue_policies(self, body, **kwargs):  # noqa: E501
        """describe_transit_router_traffic_qos_queue_policies  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.describe_transit_router_traffic_qos_queue_policies(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DescribeTransitRouterTrafficQosQueuePoliciesRequest body: (required)
        :return: DescribeTransitRouterTrafficQosQueuePoliciesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.describe_transit_router_traffic_qos_queue_policies_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.describe_transit_router_traffic_qos_queue_policies_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def describe_transit_router_traffic_qos_queue_policies_with_http_info(self, body, **kwargs):  # noqa: E501
        """describe_transit_router_traffic_qos_queue_policies  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.describe_transit_router_traffic_qos_queue_policies_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DescribeTransitRouterTrafficQosQueuePoliciesRequest body: (required)
        :return: DescribeTransitRouterTrafficQosQueuePoliciesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method describe_transit_router_traffic_qos_queue_policies" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `describe_transit_router_traffic_qos_queue_policies`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/DescribeTransitRouterTrafficQosQueuePolicies/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='DescribeTransitRouterTrafficQosQueuePoliciesResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def describe_transit_router_vpc_attachments(self, body, **kwargs):  # noqa: E501
        """describe_transit_router_vpc_attachments  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.describe_transit_router_vpc_attachments(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DescribeTransitRouterVpcAttachmentsRequest body: (required)
        :return: DescribeTransitRouterVpcAttachmentsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.describe_transit_router_vpc_attachments_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.describe_transit_router_vpc_attachments_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def describe_transit_router_vpc_attachments_with_http_info(self, body, **kwargs):  # noqa: E501
        """describe_transit_router_vpc_attachments  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.describe_transit_router_vpc_attachments_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DescribeTransitRouterVpcAttachmentsRequest body: (required)
        :return: DescribeTransitRouterVpcAttachmentsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method describe_transit_router_vpc_attachments" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `describe_transit_router_vpc_attachments`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/DescribeTransitRouterVpcAttachments/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='DescribeTransitRouterVpcAttachmentsResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def describe_transit_router_vpn_attachments(self, body, **kwargs):  # noqa: E501
        """describe_transit_router_vpn_attachments  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.describe_transit_router_vpn_attachments(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DescribeTransitRouterVpnAttachmentsRequest body: (required)
        :return: DescribeTransitRouterVpnAttachmentsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.describe_transit_router_vpn_attachments_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.describe_transit_router_vpn_attachments_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def describe_transit_router_vpn_attachments_with_http_info(self, body, **kwargs):  # noqa: E501
        """describe_transit_router_vpn_attachments  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.describe_transit_router_vpn_attachments_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DescribeTransitRouterVpnAttachmentsRequest body: (required)
        :return: DescribeTransitRouterVpnAttachmentsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method describe_transit_router_vpn_attachments" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `describe_transit_router_vpn_attachments`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/DescribeTransitRouterVpnAttachments/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='DescribeTransitRouterVpnAttachmentsResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def describe_transit_routers(self, body, **kwargs):  # noqa: E501
        """describe_transit_routers  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.describe_transit_routers(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DescribeTransitRoutersRequest body: (required)
        :return: DescribeTransitRoutersResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.describe_transit_routers_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.describe_transit_routers_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def describe_transit_routers_with_http_info(self, body, **kwargs):  # noqa: E501
        """describe_transit_routers  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.describe_transit_routers_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DescribeTransitRoutersRequest body: (required)
        :return: DescribeTransitRoutersResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method describe_transit_routers" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `describe_transit_routers`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/DescribeTransitRouters/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='DescribeTransitRoutersResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def disable_transit_router_route_table_propagation(self, body, **kwargs):  # noqa: E501
        """disable_transit_router_route_table_propagation  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.disable_transit_router_route_table_propagation(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DisableTransitRouterRouteTablePropagationRequest body: (required)
        :return: DisableTransitRouterRouteTablePropagationResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.disable_transit_router_route_table_propagation_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.disable_transit_router_route_table_propagation_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def disable_transit_router_route_table_propagation_with_http_info(self, body, **kwargs):  # noqa: E501
        """disable_transit_router_route_table_propagation  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.disable_transit_router_route_table_propagation_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DisableTransitRouterRouteTablePropagationRequest body: (required)
        :return: DisableTransitRouterRouteTablePropagationResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method disable_transit_router_route_table_propagation" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `disable_transit_router_route_table_propagation`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/DisableTransitRouterRouteTablePropagation/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='DisableTransitRouterRouteTablePropagationResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def dissociate_transit_router_attachment_from_route_table(self, body, **kwargs):  # noqa: E501
        """dissociate_transit_router_attachment_from_route_table  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.dissociate_transit_router_attachment_from_route_table(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DissociateTransitRouterAttachmentFromRouteTableRequest body: (required)
        :return: DissociateTransitRouterAttachmentFromRouteTableResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.dissociate_transit_router_attachment_from_route_table_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.dissociate_transit_router_attachment_from_route_table_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def dissociate_transit_router_attachment_from_route_table_with_http_info(self, body, **kwargs):  # noqa: E501
        """dissociate_transit_router_attachment_from_route_table  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.dissociate_transit_router_attachment_from_route_table_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DissociateTransitRouterAttachmentFromRouteTableRequest body: (required)
        :return: DissociateTransitRouterAttachmentFromRouteTableResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method dissociate_transit_router_attachment_from_route_table" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `dissociate_transit_router_attachment_from_route_table`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/DissociateTransitRouterAttachmentFromRouteTable/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='DissociateTransitRouterAttachmentFromRouteTableResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def dissociate_transit_router_forward_policy_table_from_attachment(self, body, **kwargs):  # noqa: E501
        """dissociate_transit_router_forward_policy_table_from_attachment  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.dissociate_transit_router_forward_policy_table_from_attachment(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DissociateTransitRouterForwardPolicyTableFromAttachmentRequest body: (required)
        :return: DissociateTransitRouterForwardPolicyTableFromAttachmentResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.dissociate_transit_router_forward_policy_table_from_attachment_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.dissociate_transit_router_forward_policy_table_from_attachment_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def dissociate_transit_router_forward_policy_table_from_attachment_with_http_info(self, body, **kwargs):  # noqa: E501
        """dissociate_transit_router_forward_policy_table_from_attachment  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.dissociate_transit_router_forward_policy_table_from_attachment_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DissociateTransitRouterForwardPolicyTableFromAttachmentRequest body: (required)
        :return: DissociateTransitRouterForwardPolicyTableFromAttachmentResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method dissociate_transit_router_forward_policy_table_from_attachment" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `dissociate_transit_router_forward_policy_table_from_attachment`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/DissociateTransitRouterForwardPolicyTableFromAttachment/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='DissociateTransitRouterForwardPolicyTableFromAttachmentResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def dissociate_transit_router_route_policy_from_route_table(self, body, **kwargs):  # noqa: E501
        """dissociate_transit_router_route_policy_from_route_table  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.dissociate_transit_router_route_policy_from_route_table(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DissociateTransitRouterRoutePolicyFromRouteTableRequest body: (required)
        :return: DissociateTransitRouterRoutePolicyFromRouteTableResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.dissociate_transit_router_route_policy_from_route_table_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.dissociate_transit_router_route_policy_from_route_table_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def dissociate_transit_router_route_policy_from_route_table_with_http_info(self, body, **kwargs):  # noqa: E501
        """dissociate_transit_router_route_policy_from_route_table  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.dissociate_transit_router_route_policy_from_route_table_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DissociateTransitRouterRoutePolicyFromRouteTableRequest body: (required)
        :return: DissociateTransitRouterRoutePolicyFromRouteTableResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method dissociate_transit_router_route_policy_from_route_table" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `dissociate_transit_router_route_policy_from_route_table`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/DissociateTransitRouterRoutePolicyFromRouteTable/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='DissociateTransitRouterRoutePolicyFromRouteTableResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def dissociate_transit_router_traffic_qos_marking_policy_from_attachment(self, body, **kwargs):  # noqa: E501
        """dissociate_transit_router_traffic_qos_marking_policy_from_attachment  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.dissociate_transit_router_traffic_qos_marking_policy_from_attachment(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DissociateTransitRouterTrafficQosMarkingPolicyFromAttachmentRequest body: (required)
        :return: DissociateTransitRouterTrafficQosMarkingPolicyFromAttachmentResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.dissociate_transit_router_traffic_qos_marking_policy_from_attachment_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.dissociate_transit_router_traffic_qos_marking_policy_from_attachment_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def dissociate_transit_router_traffic_qos_marking_policy_from_attachment_with_http_info(self, body, **kwargs):  # noqa: E501
        """dissociate_transit_router_traffic_qos_marking_policy_from_attachment  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.dissociate_transit_router_traffic_qos_marking_policy_from_attachment_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DissociateTransitRouterTrafficQosMarkingPolicyFromAttachmentRequest body: (required)
        :return: DissociateTransitRouterTrafficQosMarkingPolicyFromAttachmentResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method dissociate_transit_router_traffic_qos_marking_policy_from_attachment" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `dissociate_transit_router_traffic_qos_marking_policy_from_attachment`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/DissociateTransitRouterTrafficQosMarkingPolicyFromAttachment/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='DissociateTransitRouterTrafficQosMarkingPolicyFromAttachmentResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def dissociate_transit_router_traffic_qos_queue_policy_from_attachment(self, body, **kwargs):  # noqa: E501
        """dissociate_transit_router_traffic_qos_queue_policy_from_attachment  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.dissociate_transit_router_traffic_qos_queue_policy_from_attachment(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DissociateTransitRouterTrafficQosQueuePolicyFromAttachmentRequest body: (required)
        :return: DissociateTransitRouterTrafficQosQueuePolicyFromAttachmentResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.dissociate_transit_router_traffic_qos_queue_policy_from_attachment_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.dissociate_transit_router_traffic_qos_queue_policy_from_attachment_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def dissociate_transit_router_traffic_qos_queue_policy_from_attachment_with_http_info(self, body, **kwargs):  # noqa: E501
        """dissociate_transit_router_traffic_qos_queue_policy_from_attachment  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.dissociate_transit_router_traffic_qos_queue_policy_from_attachment_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DissociateTransitRouterTrafficQosQueuePolicyFromAttachmentRequest body: (required)
        :return: DissociateTransitRouterTrafficQosQueuePolicyFromAttachmentResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method dissociate_transit_router_traffic_qos_queue_policy_from_attachment" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `dissociate_transit_router_traffic_qos_queue_policy_from_attachment`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/DissociateTransitRouterTrafficQosQueuePolicyFromAttachment/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='DissociateTransitRouterTrafficQosQueuePolicyFromAttachmentResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def enable_transit_router_route_table_propagation(self, body, **kwargs):  # noqa: E501
        """enable_transit_router_route_table_propagation  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.enable_transit_router_route_table_propagation(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param EnableTransitRouterRouteTablePropagationRequest body: (required)
        :return: EnableTransitRouterRouteTablePropagationResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.enable_transit_router_route_table_propagation_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.enable_transit_router_route_table_propagation_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def enable_transit_router_route_table_propagation_with_http_info(self, body, **kwargs):  # noqa: E501
        """enable_transit_router_route_table_propagation  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.enable_transit_router_route_table_propagation_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param EnableTransitRouterRouteTablePropagationRequest body: (required)
        :return: EnableTransitRouterRouteTablePropagationResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method enable_transit_router_route_table_propagation" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `enable_transit_router_route_table_propagation`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/EnableTransitRouterRouteTablePropagation/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='EnableTransitRouterRouteTablePropagationResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def list_tags_for_resources(self, body, **kwargs):  # noqa: E501
        """list_tags_for_resources  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_tags_for_resources(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param ListTagsForResourcesRequest body: (required)
        :return: ListTagsForResourcesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.list_tags_for_resources_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.list_tags_for_resources_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def list_tags_for_resources_with_http_info(self, body, **kwargs):  # noqa: E501
        """list_tags_for_resources  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.list_tags_for_resources_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param ListTagsForResourcesRequest body: (required)
        :return: ListTagsForResourcesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method list_tags_for_resources" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `list_tags_for_resources`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/ListTagsForResources/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='ListTagsForResourcesResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def modify_transit_router_attributes(self, body, **kwargs):  # noqa: E501
        """modify_transit_router_attributes  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.modify_transit_router_attributes(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param ModifyTransitRouterAttributesRequest body: (required)
        :return: ModifyTransitRouterAttributesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.modify_transit_router_attributes_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.modify_transit_router_attributes_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def modify_transit_router_attributes_with_http_info(self, body, **kwargs):  # noqa: E501
        """modify_transit_router_attributes  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.modify_transit_router_attributes_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param ModifyTransitRouterAttributesRequest body: (required)
        :return: ModifyTransitRouterAttributesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method modify_transit_router_attributes" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `modify_transit_router_attributes`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/ModifyTransitRouterAttributes/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='ModifyTransitRouterAttributesResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def modify_transit_router_bandwidth_package_attributes(self, body, **kwargs):  # noqa: E501
        """modify_transit_router_bandwidth_package_attributes  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.modify_transit_router_bandwidth_package_attributes(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param ModifyTransitRouterBandwidthPackageAttributesRequest body: (required)
        :return: ModifyTransitRouterBandwidthPackageAttributesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.modify_transit_router_bandwidth_package_attributes_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.modify_transit_router_bandwidth_package_attributes_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def modify_transit_router_bandwidth_package_attributes_with_http_info(self, body, **kwargs):  # noqa: E501
        """modify_transit_router_bandwidth_package_attributes  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.modify_transit_router_bandwidth_package_attributes_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param ModifyTransitRouterBandwidthPackageAttributesRequest body: (required)
        :return: ModifyTransitRouterBandwidthPackageAttributesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method modify_transit_router_bandwidth_package_attributes" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `modify_transit_router_bandwidth_package_attributes`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/ModifyTransitRouterBandwidthPackageAttributes/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='ModifyTransitRouterBandwidthPackageAttributesResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def modify_transit_router_direct_connect_gateway_attachment_attributes(self, body, **kwargs):  # noqa: E501
        """modify_transit_router_direct_connect_gateway_attachment_attributes  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.modify_transit_router_direct_connect_gateway_attachment_attributes(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param ModifyTransitRouterDirectConnectGatewayAttachmentAttributesRequest body: (required)
        :return: ModifyTransitRouterDirectConnectGatewayAttachmentAttributesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.modify_transit_router_direct_connect_gateway_attachment_attributes_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.modify_transit_router_direct_connect_gateway_attachment_attributes_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def modify_transit_router_direct_connect_gateway_attachment_attributes_with_http_info(self, body, **kwargs):  # noqa: E501
        """modify_transit_router_direct_connect_gateway_attachment_attributes  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.modify_transit_router_direct_connect_gateway_attachment_attributes_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param ModifyTransitRouterDirectConnectGatewayAttachmentAttributesRequest body: (required)
        :return: ModifyTransitRouterDirectConnectGatewayAttachmentAttributesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method modify_transit_router_direct_connect_gateway_attachment_attributes" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `modify_transit_router_direct_connect_gateway_attachment_attributes`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/ModifyTransitRouterDirectConnectGatewayAttachmentAttributes/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='ModifyTransitRouterDirectConnectGatewayAttachmentAttributesResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def modify_transit_router_forward_policy_entry_attributes(self, body, **kwargs):  # noqa: E501
        """modify_transit_router_forward_policy_entry_attributes  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.modify_transit_router_forward_policy_entry_attributes(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param ModifyTransitRouterForwardPolicyEntryAttributesRequest body: (required)
        :return: ModifyTransitRouterForwardPolicyEntryAttributesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.modify_transit_router_forward_policy_entry_attributes_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.modify_transit_router_forward_policy_entry_attributes_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def modify_transit_router_forward_policy_entry_attributes_with_http_info(self, body, **kwargs):  # noqa: E501
        """modify_transit_router_forward_policy_entry_attributes  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.modify_transit_router_forward_policy_entry_attributes_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param ModifyTransitRouterForwardPolicyEntryAttributesRequest body: (required)
        :return: ModifyTransitRouterForwardPolicyEntryAttributesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method modify_transit_router_forward_policy_entry_attributes" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `modify_transit_router_forward_policy_entry_attributes`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/ModifyTransitRouterForwardPolicyEntryAttributes/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='ModifyTransitRouterForwardPolicyEntryAttributesResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def modify_transit_router_forward_policy_table_association(self, body, **kwargs):  # noqa: E501
        """modify_transit_router_forward_policy_table_association  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.modify_transit_router_forward_policy_table_association(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param ModifyTransitRouterForwardPolicyTableAssociationRequest body: (required)
        :return: ModifyTransitRouterForwardPolicyTableAssociationResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.modify_transit_router_forward_policy_table_association_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.modify_transit_router_forward_policy_table_association_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def modify_transit_router_forward_policy_table_association_with_http_info(self, body, **kwargs):  # noqa: E501
        """modify_transit_router_forward_policy_table_association  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.modify_transit_router_forward_policy_table_association_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param ModifyTransitRouterForwardPolicyTableAssociationRequest body: (required)
        :return: ModifyTransitRouterForwardPolicyTableAssociationResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method modify_transit_router_forward_policy_table_association" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `modify_transit_router_forward_policy_table_association`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/ModifyTransitRouterForwardPolicyTableAssociation/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='ModifyTransitRouterForwardPolicyTableAssociationResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def modify_transit_router_forward_policy_table_attributes(self, body, **kwargs):  # noqa: E501
        """modify_transit_router_forward_policy_table_attributes  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.modify_transit_router_forward_policy_table_attributes(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param ModifyTransitRouterForwardPolicyTableAttributesRequest body: (required)
        :return: ModifyTransitRouterForwardPolicyTableAttributesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.modify_transit_router_forward_policy_table_attributes_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.modify_transit_router_forward_policy_table_attributes_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def modify_transit_router_forward_policy_table_attributes_with_http_info(self, body, **kwargs):  # noqa: E501
        """modify_transit_router_forward_policy_table_attributes  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.modify_transit_router_forward_policy_table_attributes_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param ModifyTransitRouterForwardPolicyTableAttributesRequest body: (required)
        :return: ModifyTransitRouterForwardPolicyTableAttributesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method modify_transit_router_forward_policy_table_attributes" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `modify_transit_router_forward_policy_table_attributes`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/ModifyTransitRouterForwardPolicyTableAttributes/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='ModifyTransitRouterForwardPolicyTableAttributesResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def modify_transit_router_grant_rule_attributes(self, body, **kwargs):  # noqa: E501
        """modify_transit_router_grant_rule_attributes  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.modify_transit_router_grant_rule_attributes(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param ModifyTransitRouterGrantRuleAttributesRequest body: (required)
        :return: ModifyTransitRouterGrantRuleAttributesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.modify_transit_router_grant_rule_attributes_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.modify_transit_router_grant_rule_attributes_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def modify_transit_router_grant_rule_attributes_with_http_info(self, body, **kwargs):  # noqa: E501
        """modify_transit_router_grant_rule_attributes  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.modify_transit_router_grant_rule_attributes_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param ModifyTransitRouterGrantRuleAttributesRequest body: (required)
        :return: ModifyTransitRouterGrantRuleAttributesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method modify_transit_router_grant_rule_attributes" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `modify_transit_router_grant_rule_attributes`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/ModifyTransitRouterGrantRuleAttributes/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='ModifyTransitRouterGrantRuleAttributesResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def modify_transit_router_peer_attachment_attributes(self, body, **kwargs):  # noqa: E501
        """modify_transit_router_peer_attachment_attributes  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.modify_transit_router_peer_attachment_attributes(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param ModifyTransitRouterPeerAttachmentAttributesRequest body: (required)
        :return: ModifyTransitRouterPeerAttachmentAttributesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.modify_transit_router_peer_attachment_attributes_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.modify_transit_router_peer_attachment_attributes_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def modify_transit_router_peer_attachment_attributes_with_http_info(self, body, **kwargs):  # noqa: E501
        """modify_transit_router_peer_attachment_attributes  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.modify_transit_router_peer_attachment_attributes_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param ModifyTransitRouterPeerAttachmentAttributesRequest body: (required)
        :return: ModifyTransitRouterPeerAttachmentAttributesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method modify_transit_router_peer_attachment_attributes" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `modify_transit_router_peer_attachment_attributes`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/ModifyTransitRouterPeerAttachmentAttributes/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='ModifyTransitRouterPeerAttachmentAttributesResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def modify_transit_router_route_entry_attributes(self, body, **kwargs):  # noqa: E501
        """modify_transit_router_route_entry_attributes  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.modify_transit_router_route_entry_attributes(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param ModifyTransitRouterRouteEntryAttributesRequest body: (required)
        :return: ModifyTransitRouterRouteEntryAttributesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.modify_transit_router_route_entry_attributes_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.modify_transit_router_route_entry_attributes_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def modify_transit_router_route_entry_attributes_with_http_info(self, body, **kwargs):  # noqa: E501
        """modify_transit_router_route_entry_attributes  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.modify_transit_router_route_entry_attributes_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param ModifyTransitRouterRouteEntryAttributesRequest body: (required)
        :return: ModifyTransitRouterRouteEntryAttributesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method modify_transit_router_route_entry_attributes" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `modify_transit_router_route_entry_attributes`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/ModifyTransitRouterRouteEntryAttributes/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='ModifyTransitRouterRouteEntryAttributesResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def modify_transit_router_route_policy_association(self, body, **kwargs):  # noqa: E501
        """modify_transit_router_route_policy_association  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.modify_transit_router_route_policy_association(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param ModifyTransitRouterRoutePolicyAssociationRequest body: (required)
        :return: ModifyTransitRouterRoutePolicyAssociationResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.modify_transit_router_route_policy_association_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.modify_transit_router_route_policy_association_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def modify_transit_router_route_policy_association_with_http_info(self, body, **kwargs):  # noqa: E501
        """modify_transit_router_route_policy_association  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.modify_transit_router_route_policy_association_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param ModifyTransitRouterRoutePolicyAssociationRequest body: (required)
        :return: ModifyTransitRouterRoutePolicyAssociationResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method modify_transit_router_route_policy_association" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `modify_transit_router_route_policy_association`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/ModifyTransitRouterRoutePolicyAssociation/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='ModifyTransitRouterRoutePolicyAssociationResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def modify_transit_router_route_policy_entry_attributes(self, body, **kwargs):  # noqa: E501
        """modify_transit_router_route_policy_entry_attributes  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.modify_transit_router_route_policy_entry_attributes(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param ModifyTransitRouterRoutePolicyEntryAttributesRequest body: (required)
        :return: ModifyTransitRouterRoutePolicyEntryAttributesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.modify_transit_router_route_policy_entry_attributes_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.modify_transit_router_route_policy_entry_attributes_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def modify_transit_router_route_policy_entry_attributes_with_http_info(self, body, **kwargs):  # noqa: E501
        """modify_transit_router_route_policy_entry_attributes  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.modify_transit_router_route_policy_entry_attributes_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param ModifyTransitRouterRoutePolicyEntryAttributesRequest body: (required)
        :return: ModifyTransitRouterRoutePolicyEntryAttributesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method modify_transit_router_route_policy_entry_attributes" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `modify_transit_router_route_policy_entry_attributes`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/ModifyTransitRouterRoutePolicyEntryAttributes/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='ModifyTransitRouterRoutePolicyEntryAttributesResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def modify_transit_router_route_policy_table_attributes(self, body, **kwargs):  # noqa: E501
        """modify_transit_router_route_policy_table_attributes  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.modify_transit_router_route_policy_table_attributes(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param ModifyTransitRouterRoutePolicyTableAttributesRequest body: (required)
        :return: ModifyTransitRouterRoutePolicyTableAttributesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.modify_transit_router_route_policy_table_attributes_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.modify_transit_router_route_policy_table_attributes_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def modify_transit_router_route_policy_table_attributes_with_http_info(self, body, **kwargs):  # noqa: E501
        """modify_transit_router_route_policy_table_attributes  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.modify_transit_router_route_policy_table_attributes_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param ModifyTransitRouterRoutePolicyTableAttributesRequest body: (required)
        :return: ModifyTransitRouterRoutePolicyTableAttributesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method modify_transit_router_route_policy_table_attributes" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `modify_transit_router_route_policy_table_attributes`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/ModifyTransitRouterRoutePolicyTableAttributes/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='ModifyTransitRouterRoutePolicyTableAttributesResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def modify_transit_router_route_table_association_attributes(self, body, **kwargs):  # noqa: E501
        """modify_transit_router_route_table_association_attributes  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.modify_transit_router_route_table_association_attributes(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param ModifyTransitRouterRouteTableAssociationAttributesRequest body: (required)
        :return: ModifyTransitRouterRouteTableAssociationAttributesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.modify_transit_router_route_table_association_attributes_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.modify_transit_router_route_table_association_attributes_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def modify_transit_router_route_table_association_attributes_with_http_info(self, body, **kwargs):  # noqa: E501
        """modify_transit_router_route_table_association_attributes  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.modify_transit_router_route_table_association_attributes_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param ModifyTransitRouterRouteTableAssociationAttributesRequest body: (required)
        :return: ModifyTransitRouterRouteTableAssociationAttributesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method modify_transit_router_route_table_association_attributes" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `modify_transit_router_route_table_association_attributes`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/ModifyTransitRouterRouteTableAssociationAttributes/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='ModifyTransitRouterRouteTableAssociationAttributesResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def modify_transit_router_route_table_attributes(self, body, **kwargs):  # noqa: E501
        """modify_transit_router_route_table_attributes  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.modify_transit_router_route_table_attributes(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param ModifyTransitRouterRouteTableAttributesRequest body: (required)
        :return: ModifyTransitRouterRouteTableAttributesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.modify_transit_router_route_table_attributes_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.modify_transit_router_route_table_attributes_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def modify_transit_router_route_table_attributes_with_http_info(self, body, **kwargs):  # noqa: E501
        """modify_transit_router_route_table_attributes  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.modify_transit_router_route_table_attributes_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param ModifyTransitRouterRouteTableAttributesRequest body: (required)
        :return: ModifyTransitRouterRouteTableAttributesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method modify_transit_router_route_table_attributes" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `modify_transit_router_route_table_attributes`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/ModifyTransitRouterRouteTableAttributes/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='ModifyTransitRouterRouteTableAttributesResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def modify_transit_router_traffic_qos_marking_policy_association(self, body, **kwargs):  # noqa: E501
        """modify_transit_router_traffic_qos_marking_policy_association  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.modify_transit_router_traffic_qos_marking_policy_association(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param ModifyTransitRouterTrafficQosMarkingPolicyAssociationRequest body: (required)
        :return: ModifyTransitRouterTrafficQosMarkingPolicyAssociationResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.modify_transit_router_traffic_qos_marking_policy_association_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.modify_transit_router_traffic_qos_marking_policy_association_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def modify_transit_router_traffic_qos_marking_policy_association_with_http_info(self, body, **kwargs):  # noqa: E501
        """modify_transit_router_traffic_qos_marking_policy_association  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.modify_transit_router_traffic_qos_marking_policy_association_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param ModifyTransitRouterTrafficQosMarkingPolicyAssociationRequest body: (required)
        :return: ModifyTransitRouterTrafficQosMarkingPolicyAssociationResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method modify_transit_router_traffic_qos_marking_policy_association" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `modify_transit_router_traffic_qos_marking_policy_association`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/ModifyTransitRouterTrafficQosMarkingPolicyAssociation/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='ModifyTransitRouterTrafficQosMarkingPolicyAssociationResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def modify_transit_router_traffic_qos_marking_policy_attributes(self, body, **kwargs):  # noqa: E501
        """modify_transit_router_traffic_qos_marking_policy_attributes  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.modify_transit_router_traffic_qos_marking_policy_attributes(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param ModifyTransitRouterTrafficQosMarkingPolicyAttributesRequest body: (required)
        :return: ModifyTransitRouterTrafficQosMarkingPolicyAttributesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.modify_transit_router_traffic_qos_marking_policy_attributes_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.modify_transit_router_traffic_qos_marking_policy_attributes_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def modify_transit_router_traffic_qos_marking_policy_attributes_with_http_info(self, body, **kwargs):  # noqa: E501
        """modify_transit_router_traffic_qos_marking_policy_attributes  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.modify_transit_router_traffic_qos_marking_policy_attributes_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param ModifyTransitRouterTrafficQosMarkingPolicyAttributesRequest body: (required)
        :return: ModifyTransitRouterTrafficQosMarkingPolicyAttributesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method modify_transit_router_traffic_qos_marking_policy_attributes" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `modify_transit_router_traffic_qos_marking_policy_attributes`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/ModifyTransitRouterTrafficQosMarkingPolicyAttributes/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='ModifyTransitRouterTrafficQosMarkingPolicyAttributesResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def modify_transit_router_traffic_qos_queue_policy_association(self, body, **kwargs):  # noqa: E501
        """modify_transit_router_traffic_qos_queue_policy_association  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.modify_transit_router_traffic_qos_queue_policy_association(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param ModifyTransitRouterTrafficQosQueuePolicyAssociationRequest body: (required)
        :return: ModifyTransitRouterTrafficQosQueuePolicyAssociationResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.modify_transit_router_traffic_qos_queue_policy_association_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.modify_transit_router_traffic_qos_queue_policy_association_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def modify_transit_router_traffic_qos_queue_policy_association_with_http_info(self, body, **kwargs):  # noqa: E501
        """modify_transit_router_traffic_qos_queue_policy_association  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.modify_transit_router_traffic_qos_queue_policy_association_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param ModifyTransitRouterTrafficQosQueuePolicyAssociationRequest body: (required)
        :return: ModifyTransitRouterTrafficQosQueuePolicyAssociationResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method modify_transit_router_traffic_qos_queue_policy_association" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `modify_transit_router_traffic_qos_queue_policy_association`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/ModifyTransitRouterTrafficQosQueuePolicyAssociation/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='ModifyTransitRouterTrafficQosQueuePolicyAssociationResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def modify_transit_router_traffic_qos_queue_policy_attributes(self, body, **kwargs):  # noqa: E501
        """modify_transit_router_traffic_qos_queue_policy_attributes  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.modify_transit_router_traffic_qos_queue_policy_attributes(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param ModifyTransitRouterTrafficQosQueuePolicyAttributesRequest body: (required)
        :return: ModifyTransitRouterTrafficQosQueuePolicyAttributesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.modify_transit_router_traffic_qos_queue_policy_attributes_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.modify_transit_router_traffic_qos_queue_policy_attributes_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def modify_transit_router_traffic_qos_queue_policy_attributes_with_http_info(self, body, **kwargs):  # noqa: E501
        """modify_transit_router_traffic_qos_queue_policy_attributes  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.modify_transit_router_traffic_qos_queue_policy_attributes_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param ModifyTransitRouterTrafficQosQueuePolicyAttributesRequest body: (required)
        :return: ModifyTransitRouterTrafficQosQueuePolicyAttributesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method modify_transit_router_traffic_qos_queue_policy_attributes" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `modify_transit_router_traffic_qos_queue_policy_attributes`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/ModifyTransitRouterTrafficQosQueuePolicyAttributes/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='ModifyTransitRouterTrafficQosQueuePolicyAttributesResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def modify_transit_router_vpc_attachment_attributes(self, body, **kwargs):  # noqa: E501
        """modify_transit_router_vpc_attachment_attributes  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.modify_transit_router_vpc_attachment_attributes(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param ModifyTransitRouterVpcAttachmentAttributesRequest body: (required)
        :return: ModifyTransitRouterVpcAttachmentAttributesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.modify_transit_router_vpc_attachment_attributes_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.modify_transit_router_vpc_attachment_attributes_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def modify_transit_router_vpc_attachment_attributes_with_http_info(self, body, **kwargs):  # noqa: E501
        """modify_transit_router_vpc_attachment_attributes  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.modify_transit_router_vpc_attachment_attributes_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param ModifyTransitRouterVpcAttachmentAttributesRequest body: (required)
        :return: ModifyTransitRouterVpcAttachmentAttributesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method modify_transit_router_vpc_attachment_attributes" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `modify_transit_router_vpc_attachment_attributes`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/ModifyTransitRouterVpcAttachmentAttributes/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='ModifyTransitRouterVpcAttachmentAttributesResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def modify_transit_router_vpn_attachment_attributes(self, body, **kwargs):  # noqa: E501
        """modify_transit_router_vpn_attachment_attributes  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.modify_transit_router_vpn_attachment_attributes(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param ModifyTransitRouterVpnAttachmentAttributesRequest body: (required)
        :return: ModifyTransitRouterVpnAttachmentAttributesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.modify_transit_router_vpn_attachment_attributes_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.modify_transit_router_vpn_attachment_attributes_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def modify_transit_router_vpn_attachment_attributes_with_http_info(self, body, **kwargs):  # noqa: E501
        """modify_transit_router_vpn_attachment_attributes  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.modify_transit_router_vpn_attachment_attributes_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param ModifyTransitRouterVpnAttachmentAttributesRequest body: (required)
        :return: ModifyTransitRouterVpnAttachmentAttributesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method modify_transit_router_vpn_attachment_attributes" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `modify_transit_router_vpn_attachment_attributes`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/ModifyTransitRouterVpnAttachmentAttributes/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='ModifyTransitRouterVpnAttachmentAttributesResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def reject_shared_transit_router(self, body, **kwargs):  # noqa: E501
        """reject_shared_transit_router  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.reject_shared_transit_router(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param RejectSharedTransitRouterRequest body: (required)
        :return: RejectSharedTransitRouterResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.reject_shared_transit_router_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.reject_shared_transit_router_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def reject_shared_transit_router_with_http_info(self, body, **kwargs):  # noqa: E501
        """reject_shared_transit_router  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.reject_shared_transit_router_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param RejectSharedTransitRouterRequest body: (required)
        :return: RejectSharedTransitRouterResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method reject_shared_transit_router" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `reject_shared_transit_router`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/RejectSharedTransitRouter/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='RejectSharedTransitRouterResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def renew_transit_router_bandwidth_package(self, body, **kwargs):  # noqa: E501
        """renew_transit_router_bandwidth_package  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.renew_transit_router_bandwidth_package(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param RenewTransitRouterBandwidthPackageRequest body: (required)
        :return: RenewTransitRouterBandwidthPackageResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.renew_transit_router_bandwidth_package_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.renew_transit_router_bandwidth_package_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def renew_transit_router_bandwidth_package_with_http_info(self, body, **kwargs):  # noqa: E501
        """renew_transit_router_bandwidth_package  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.renew_transit_router_bandwidth_package_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param RenewTransitRouterBandwidthPackageRequest body: (required)
        :return: RenewTransitRouterBandwidthPackageResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method renew_transit_router_bandwidth_package" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `renew_transit_router_bandwidth_package`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/RenewTransitRouterBandwidthPackage/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='RenewTransitRouterBandwidthPackageResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def set_transit_router_bandwidth_package_renewal(self, body, **kwargs):  # noqa: E501
        """set_transit_router_bandwidth_package_renewal  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.set_transit_router_bandwidth_package_renewal(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param SetTransitRouterBandwidthPackageRenewalRequest body: (required)
        :return: SetTransitRouterBandwidthPackageRenewalResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.set_transit_router_bandwidth_package_renewal_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.set_transit_router_bandwidth_package_renewal_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def set_transit_router_bandwidth_package_renewal_with_http_info(self, body, **kwargs):  # noqa: E501
        """set_transit_router_bandwidth_package_renewal  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.set_transit_router_bandwidth_package_renewal_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param SetTransitRouterBandwidthPackageRenewalRequest body: (required)
        :return: SetTransitRouterBandwidthPackageRenewalResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method set_transit_router_bandwidth_package_renewal" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `set_transit_router_bandwidth_package_renewal`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/SetTransitRouterBandwidthPackageRenewal/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='SetTransitRouterBandwidthPackageRenewalResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def tag_resources(self, body, **kwargs):  # noqa: E501
        """tag_resources  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.tag_resources(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param TagResourcesRequest body: (required)
        :return: TagResourcesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.tag_resources_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.tag_resources_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def tag_resources_with_http_info(self, body, **kwargs):  # noqa: E501
        """tag_resources  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.tag_resources_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param TagResourcesRequest body: (required)
        :return: TagResourcesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method tag_resources" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `tag_resources`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/TagResources/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='TagResourcesResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def untag_resources(self, body, **kwargs):  # noqa: E501
        """untag_resources  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.untag_resources(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param UntagResourcesRequest body: (required)
        :return: UntagResourcesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.untag_resources_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.untag_resources_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def untag_resources_with_http_info(self, body, **kwargs):  # noqa: E501
        """untag_resources  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.untag_resources_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param UntagResourcesRequest body: (required)
        :return: UntagResourcesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method untag_resources" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `untag_resources`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/UntagResources/2020-04-01/transitrouter/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='UntagResourcesResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)
