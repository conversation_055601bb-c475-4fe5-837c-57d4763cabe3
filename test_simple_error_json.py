#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试重试机制和简单错误JSON生成功能
"""

import json

def test_simple_error_json():
    """测试简单错误JSON生成功能"""
    print("=== 测试简单错误JSON生成功能 ===")
    
    # 模拟不同类型的错误
    error_cases = [
        "Connection error., request_id: 20250805095936ZSbDX7HINXa4XLApQAT8",
        "API返回的响应内容为空",
        "JSON解析失败: Expecting ',' delimiter",
        "网络超时"
    ]
    
    for i, error_str in enumerate(error_cases, 1):
        error_json = json.dumps({"请求异常": error_str}, ensure_ascii=False)
        print(f"\n{i}. 错误信息: {error_str}")
        print(f"   生成的JSON: {error_json}")
        
        # 验证JSON格式是否正确
        try:
            parsed = json.loads(error_json)
            print(f"   JSON验证: ✓ 格式正确")
            print(f"   解析结果: {parsed}")
        except Exception as e:
            print(f"   JSON验证: ✗ 格式错误 - {e}")
    
    print("\n=== 测试完成 ===")
    print("\n优势:")
    print("1. 格式简单统一，不需要获取题目数量")
    print("2. 包含具体错误信息，便于调试")
    print("3. 在比对时会被识别为错误，不会影响正常的准确率计算")
    print("4. JSON格式保持一致性，不会导致后续处理出错")

if __name__ == "__main__":
    test_simple_error_json()
