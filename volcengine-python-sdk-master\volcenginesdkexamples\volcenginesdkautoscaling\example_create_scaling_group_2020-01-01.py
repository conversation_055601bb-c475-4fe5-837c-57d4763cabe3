# Example Code generated by Beijing Volcanoengine Technology.
from __future__ import print_function
import volcenginesdkcore
import volcenginesdkautoscaling
from pprint import pprint
from volcenginesdkcore.rest import ApiException

if __name__ == '__main__':
    configuration = volcenginesdkcore.Configuration()
    configuration.ak = "Your AK"
    configuration.sk = "Your SK"
    configuration.region = "cn-beijing"
    # set default configuration
    volcenginesdkcore.Configuration.set_default(configuration)

    # use global default configuration
    api_instance = volcenginesdkautoscaling.AUTOSCALINGApi()
    create_scaling_group_request = volcenginesdkautoscaling.CreateScalingGroupRequest(
        max_instance_number=10,
        min_instance_number=1,
        scaling_group_name="scaling-group-test",
        subnet_ids=["subnet-inaimn26s8ow8gbssyxi****"],
    )
    
    try:
        resp = api_instance.create_scaling_group(create_scaling_group_request)
        pprint(resp)
    except ApiException as e:
        print("Exception when calling api: %s\n" % e)
