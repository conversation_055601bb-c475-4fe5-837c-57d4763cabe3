# Example Code generated by Beijing Volcanoengine Technology.
from __future__ import print_function
import volcenginesdkcore
import volcenginesdkclb
from pprint import pprint
from volcenginesdkcore.rest import ApiException

if __name__ == '__main__':
    configuration = volcenginesdkcore.Configuration()
    configuration.ak = "Your AK"
    configuration.sk = "Your SK"
    configuration.region = "cn-beijing"
    # set default configuration
    volcenginesdkcore.Configuration.set_default(configuration)

    # use global default configuration
    api_instance = volcenginesdkclb.CLBApi()
    remove_server_group_backend_servers_request = volcenginesdkclb.RemoveServerGroupBackendServersRequest(
        server_group_id="rsp-bp1o94dp5i6ea****",
        server_ids=["rs-3cig8e5o0kxs06c6rrsqn****"],
    )
    
    try:
        resp = api_instance.remove_server_group_backend_servers(remove_server_group_backend_servers_request)
        pprint(resp)
    except ApiException as e:
        print("Exception when calling api: %s\n" % e)
