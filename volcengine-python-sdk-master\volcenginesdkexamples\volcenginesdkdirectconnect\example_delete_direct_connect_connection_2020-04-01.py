# Example Code generated by Beijing Volcanoengine Technology.
from __future__ import print_function
import volcenginesdkcore
import volcenginesdkdirectconnect
from pprint import pprint
from volcenginesdkcore.rest import ApiException

if __name__ == '__main__':
    configuration = volcenginesdkcore.Configuration()
    configuration.ak = "Your AK"
    configuration.sk = "Your SK"
    configuration.region = "cn-beijing"
    # set default configuration
    volcenginesdkcore.Configuration.set_default(configuration)

    # use global default configuration
    api_instance = volcenginesdkdirectconnect.DIRECTCONNECTApi()
    delete_direct_connect_connection_request = volcenginesdkdirectconnect.DeleteDirectConnectConnectionRequest(
        direct_connect_connection_id="dcc-3tehy13n2l4c6c0v****",
    )
    
    try:
        resp = api_instance.delete_direct_connect_connection(delete_direct_connect_connection_request)
        pprint(resp)
    except ApiException as e:
        print("Exception when calling api: %s\n" % e)
