import os
import datetime
import json
import re

def generate_summary(results, output_dir, question_type, processing_type, round2_prompt_text=None):
    summary_path = os.path.join(output_dir, "summary.md")
    with open(summary_path, 'w', encoding='utf-8') as f:
        f.write(f"# AI图像识别和验证结果\n\n")
        f.write(f"**题型**: {question_type}\n")
        f.write(f"**处理方式**: {processing_type}\n")
        f.write(f"**处理时间**: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        if round2_prompt_text:
            f.write(f"**使用的提示词**:\n```")
            f.write(str(round2_prompt_text))
            f.write("\n```\n\n")
        success_count = sum(1 for r in results if r['success'])
        total_boxes = sum(r.get('box_count', 0) for r in results if r['success'])
        f.write(f"**处理统计**:\n")
        f.write(f"- 总图片数: {len(results)}\n")
        f.write(f"- 成功处理: {success_count}\n")
        f.write(f"- 总标注框数: {total_boxes}\n\n")
        f.write("## 处理结果\n\n")
        for i, result in enumerate(results, 1):
            f.write(f"###图片: {result['filename']}\n\n")
            if result['success']:
                f.write(f"![{result.get('processed_filename', '')}]({result.get('processed_filename', '')})\n\n")
                f.write("**标注信息:**\n")
                f.write(f"- 图片尺寸: {result.get('image_size', '未知')}\n")
                f.write(f"- 标注框数量: {result.get('box_count', 0)}\n")
                f.write(f"- 处理时间: {result.get('processing_time', 0):.2f}秒\n\n")
                if result.get('boxes'):
                    f.write("- 标注框坐标 (x_min, y_min, x_max, y_max, class_id):\n")
                    for j, (x_min, y_min, x_max, y_max, class_id) in enumerate(result['boxes'], 1):
                        width = x_max - x_min
                        height = y_max - y_min
                        area = width * height
                        f.write(f"  - 区域{j}: ({x_min}, {y_min}, {x_max}, {y_max}, 类别{class_id}) - 尺寸: {width}×{height}, 面积: {area}\n")
                if result.get('answer_json'):
                    f.write("\n**正确答案:**\n")
                    f.write(f"```json\n{result.get('answer_json', '{}')}\n```\n")
                f.write("\n**AI模型回答:**\n")
                f.write(f"```")
                f.write(str(result.get('ai_response', '无回答')))
                f.write("\n```\n")
            else:
                f.write(f"**错误**: {result.get('error', '未知错误')}\n")
            f.write("\n---\n\n")
    print(f"\n✓ 处理完成！summary.md 已保存至: {summary_path}")

def calc_accuracy_and_wrong_items(new_results, template_results):
    wrongs = []
    error_msgs = []
    wrong_items = []
    if len(new_results) != len(template_results):
        error_msgs.append(f"响应数量不一致：本次{len(new_results)}，模板{len(template_results)}")
    min_len = min(len(new_results), len(template_results))
    for i in range(min_len):
        new_name, new_json = new_results[i]
        tpl_name, tpl_json = template_results[i]
        if new_json != tpl_json:
            if tpl_json == "{}":
                continue
            else:
                wrongs.append(f"第 {i+1} 张图片: {new_name}")
                wrong_items.append(i+1)
    total = min_len
    wrong_count = len(wrongs)
    accuracy = (total - wrong_count) / total if total > 0 else 1.0
    return accuracy, wrongs, error_msgs, wrong_items 