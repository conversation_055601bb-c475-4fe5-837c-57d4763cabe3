# Example Code generated by Beijing Volcanoengine Technology.
from __future__ import print_function
import volcenginesdkcore
import volcenginesdkclb
from pprint import pprint
from volcenginesdkcore.rest import ApiException

if __name__ == '__main__':
    configuration = volcenginesdkcore.Configuration()
    configuration.ak = "Your AK"
    configuration.sk = "Your SK"
    configuration.region = "cn-beijing"
    # set default configuration
    volcenginesdkcore.Configuration.set_default(configuration)

    # use global default configuration
    api_instance = volcenginesdkclb.CLBApi()
    req_servers = volcenginesdkclb.ServerForCreateServerGroupInput(
        instance_id="i-3tkuehz8oa3vj0wz****",
        ip="192.XX.XX.2",
        port=88,
        type="ecs",
        weight=100,
    )
    create_server_group_request = volcenginesdkclb.CreateServerGroupRequest(
        load_balancer_id="clb-bp1b6c719dfa08ex****",
        server_group_name="myservergroup",
        servers=[req_servers],
    )
    
    try:
        resp = api_instance.create_server_group(create_server_group_request)
        pprint(resp)
    except ApiException as e:
        print("Exception when calling api: %s\n" % e)
