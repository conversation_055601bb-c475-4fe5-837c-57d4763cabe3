# coding: utf-8

# flake8: noqa

"""
    vefaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


from __future__ import absolute_import

# import apis into sdk package
from volcenginesdkvefaas.api.vefaas_api import VEFAASApi

# import models into sdk package
from volcenginesdkvefaas.models.abort_release_request import AbortReleaseRequest
from volcenginesdkvefaas.models.abort_release_response import AbortReleaseResponse
from volcenginesdkvefaas.models.create_function_request import CreateFunctionRequest
from volcenginesdkvefaas.models.create_function_response import CreateFunctionResponse
from volcenginesdkvefaas.models.create_kafka_trigger_request import CreateKafkaTriggerRequest
from volcenginesdkvefaas.models.create_kafka_trigger_response import CreateKafkaTriggerResponse
from volcenginesdkvefaas.models.create_sandbox_request import CreateSandboxRequest
from volcenginesdkvefaas.models.create_sandbox_response import CreateSandboxResponse
from volcenginesdkvefaas.models.create_timer_request import CreateTimerRequest
from volcenginesdkvefaas.models.create_timer_response import CreateTimerResponse
from volcenginesdkvefaas.models.credentials_for_create_function_input import CredentialsForCreateFunctionInput
from volcenginesdkvefaas.models.credentials_for_create_function_output import CredentialsForCreateFunctionOutput
from volcenginesdkvefaas.models.credentials_for_get_function_output import CredentialsForGetFunctionOutput
from volcenginesdkvefaas.models.credentials_for_get_revision_output import CredentialsForGetRevisionOutput
from volcenginesdkvefaas.models.credentials_for_list_functions_output import CredentialsForListFunctionsOutput
from volcenginesdkvefaas.models.credentials_for_list_revisions_output import CredentialsForListRevisionsOutput
from volcenginesdkvefaas.models.credentials_for_update_function_input import CredentialsForUpdateFunctionInput
from volcenginesdkvefaas.models.credentials_for_update_function_output import CredentialsForUpdateFunctionOutput
from volcenginesdkvefaas.models.delete_function_request import DeleteFunctionRequest
from volcenginesdkvefaas.models.delete_function_response import DeleteFunctionResponse
from volcenginesdkvefaas.models.delete_kafka_trigger_request import DeleteKafkaTriggerRequest
from volcenginesdkvefaas.models.delete_kafka_trigger_response import DeleteKafkaTriggerResponse
from volcenginesdkvefaas.models.delete_timer_request import DeleteTimerRequest
from volcenginesdkvefaas.models.delete_timer_response import DeleteTimerResponse
from volcenginesdkvefaas.models.env_for_create_function_input import EnvForCreateFunctionInput
from volcenginesdkvefaas.models.env_for_create_function_output import EnvForCreateFunctionOutput
from volcenginesdkvefaas.models.env_for_create_sandbox_input import EnvForCreateSandboxInput
from volcenginesdkvefaas.models.env_for_get_function_output import EnvForGetFunctionOutput
from volcenginesdkvefaas.models.env_for_get_revision_output import EnvForGetRevisionOutput
from volcenginesdkvefaas.models.env_for_list_functions_output import EnvForListFunctionsOutput
from volcenginesdkvefaas.models.env_for_list_revisions_output import EnvForListRevisionsOutput
from volcenginesdkvefaas.models.env_for_update_function_input import EnvForUpdateFunctionInput
from volcenginesdkvefaas.models.env_for_update_function_output import EnvForUpdateFunctionOutput
from volcenginesdkvefaas.models.filter_for_list_functions_input import FilterForListFunctionsInput
from volcenginesdkvefaas.models.filter_for_list_release_records_input import FilterForListReleaseRecordsInput
from volcenginesdkvefaas.models.filter_for_list_revisions_input import FilterForListRevisionsInput
from volcenginesdkvefaas.models.gen_webshell_endpoint_request import GenWebshellEndpointRequest
from volcenginesdkvefaas.models.gen_webshell_endpoint_response import GenWebshellEndpointResponse
from volcenginesdkvefaas.models.get_code_upload_address_request import GetCodeUploadAddressRequest
from volcenginesdkvefaas.models.get_code_upload_address_response import GetCodeUploadAddressResponse
from volcenginesdkvefaas.models.get_function_instance_logs_request import GetFunctionInstanceLogsRequest
from volcenginesdkvefaas.models.get_function_instance_logs_response import GetFunctionInstanceLogsResponse
from volcenginesdkvefaas.models.get_function_request import GetFunctionRequest
from volcenginesdkvefaas.models.get_function_response import GetFunctionResponse
from volcenginesdkvefaas.models.get_image_sync_status_request import GetImageSyncStatusRequest
from volcenginesdkvefaas.models.get_image_sync_status_response import GetImageSyncStatusResponse
from volcenginesdkvefaas.models.get_kafka_trigger_request import GetKafkaTriggerRequest
from volcenginesdkvefaas.models.get_kafka_trigger_response import GetKafkaTriggerResponse
from volcenginesdkvefaas.models.get_release_status_request import GetReleaseStatusRequest
from volcenginesdkvefaas.models.get_release_status_response import GetReleaseStatusResponse
from volcenginesdkvefaas.models.get_revision_request import GetRevisionRequest
from volcenginesdkvefaas.models.get_revision_response import GetRevisionResponse
from volcenginesdkvefaas.models.get_timer_request import GetTimerRequest
from volcenginesdkvefaas.models.get_timer_response import GetTimerResponse
from volcenginesdkvefaas.models.http_get_for_get_revision_output import HTTPGetForGetRevisionOutput
from volcenginesdkvefaas.models.http_get_for_list_revisions_output import HTTPGetForListRevisionsOutput
from volcenginesdkvefaas.models.http_header_for_get_revision_output import HTTPHeaderForGetRevisionOutput
from volcenginesdkvefaas.models.http_header_for_list_revisions_output import HTTPHeaderForListRevisionsOutput
from volcenginesdkvefaas.models.health_check_config_for_get_revision_output import HealthCheckConfigForGetRevisionOutput
from volcenginesdkvefaas.models.health_check_config_for_list_revisions_output import HealthCheckConfigForListRevisionsOutput
from volcenginesdkvefaas.models.instance_tos_mount_config_for_create_sandbox_input import InstanceTosMountConfigForCreateSandboxInput
from volcenginesdkvefaas.models.item_for_list_function_instances_output import ItemForListFunctionInstancesOutput
from volcenginesdkvefaas.models.item_for_list_functions_output import ItemForListFunctionsOutput
from volcenginesdkvefaas.models.item_for_list_release_records_output import ItemForListReleaseRecordsOutput
from volcenginesdkvefaas.models.item_for_list_revisions_output import ItemForListRevisionsOutput
from volcenginesdkvefaas.models.item_for_list_triggers_output import ItemForListTriggersOutput
from volcenginesdkvefaas.models.kafka_credentials_for_create_kafka_trigger_input import KafkaCredentialsForCreateKafkaTriggerInput
from volcenginesdkvefaas.models.kill_sandbox_request import KillSandboxRequest
from volcenginesdkvefaas.models.kill_sandbox_response import KillSandboxResponse
from volcenginesdkvefaas.models.list_function_elastic_scale_strategy_request import ListFunctionElasticScaleStrategyRequest
from volcenginesdkvefaas.models.list_function_elastic_scale_strategy_response import ListFunctionElasticScaleStrategyResponse
from volcenginesdkvefaas.models.list_function_instances_request import ListFunctionInstancesRequest
from volcenginesdkvefaas.models.list_function_instances_response import ListFunctionInstancesResponse
from volcenginesdkvefaas.models.list_functions_request import ListFunctionsRequest
from volcenginesdkvefaas.models.list_functions_response import ListFunctionsResponse
from volcenginesdkvefaas.models.list_release_records_request import ListReleaseRecordsRequest
from volcenginesdkvefaas.models.list_release_records_response import ListReleaseRecordsResponse
from volcenginesdkvefaas.models.list_revisions_request import ListRevisionsRequest
from volcenginesdkvefaas.models.list_revisions_response import ListRevisionsResponse
from volcenginesdkvefaas.models.list_sandboxes_request import ListSandboxesRequest
from volcenginesdkvefaas.models.list_sandboxes_response import ListSandboxesResponse
from volcenginesdkvefaas.models.list_triggers_request import ListTriggersRequest
from volcenginesdkvefaas.models.list_triggers_response import ListTriggersResponse
from volcenginesdkvefaas.models.metadata_for_create_sandbox_input import MetadataForCreateSandboxInput
from volcenginesdkvefaas.models.metadata_for_list_sandboxes_input import MetadataForListSandboxesInput
from volcenginesdkvefaas.models.metadata_for_list_sandboxes_output import MetadataForListSandboxesOutput
from volcenginesdkvefaas.models.mount_point_for_create_function_input import MountPointForCreateFunctionInput
from volcenginesdkvefaas.models.mount_point_for_create_function_output import MountPointForCreateFunctionOutput
from volcenginesdkvefaas.models.mount_point_for_get_function_output import MountPointForGetFunctionOutput
from volcenginesdkvefaas.models.mount_point_for_get_revision_output import MountPointForGetRevisionOutput
from volcenginesdkvefaas.models.mount_point_for_list_functions_output import MountPointForListFunctionsOutput
from volcenginesdkvefaas.models.mount_point_for_list_revisions_output import MountPointForListRevisionsOutput
from volcenginesdkvefaas.models.mount_point_for_update_function_input import MountPointForUpdateFunctionInput
from volcenginesdkvefaas.models.mount_point_for_update_function_output import MountPointForUpdateFunctionOutput
from volcenginesdkvefaas.models.nas_config_for_create_function_input import NasConfigForCreateFunctionInput
from volcenginesdkvefaas.models.nas_config_for_create_function_output import NasConfigForCreateFunctionOutput
from volcenginesdkvefaas.models.nas_config_for_get_function_output import NasConfigForGetFunctionOutput
from volcenginesdkvefaas.models.nas_config_for_get_revision_output import NasConfigForGetRevisionOutput
from volcenginesdkvefaas.models.nas_config_for_list_functions_output import NasConfigForListFunctionsOutput
from volcenginesdkvefaas.models.nas_config_for_list_revisions_output import NasConfigForListRevisionsOutput
from volcenginesdkvefaas.models.nas_config_for_update_function_input import NasConfigForUpdateFunctionInput
from volcenginesdkvefaas.models.nas_config_for_update_function_output import NasConfigForUpdateFunctionOutput
from volcenginesdkvefaas.models.nas_storage_for_create_function_input import NasStorageForCreateFunctionInput
from volcenginesdkvefaas.models.nas_storage_for_create_function_output import NasStorageForCreateFunctionOutput
from volcenginesdkvefaas.models.nas_storage_for_get_function_output import NasStorageForGetFunctionOutput
from volcenginesdkvefaas.models.nas_storage_for_get_revision_output import NasStorageForGetRevisionOutput
from volcenginesdkvefaas.models.nas_storage_for_list_functions_output import NasStorageForListFunctionsOutput
from volcenginesdkvefaas.models.nas_storage_for_list_revisions_output import NasStorageForListRevisionsOutput
from volcenginesdkvefaas.models.nas_storage_for_update_function_input import NasStorageForUpdateFunctionInput
from volcenginesdkvefaas.models.nas_storage_for_update_function_output import NasStorageForUpdateFunctionOutput
from volcenginesdkvefaas.models.order_by_for_list_release_records_input import OrderByForListReleaseRecordsInput
from volcenginesdkvefaas.models.probe_handler_for_get_revision_output import ProbeHandlerForGetRevisionOutput
from volcenginesdkvefaas.models.probe_handler_for_list_revisions_output import ProbeHandlerForListRevisionsOutput
from volcenginesdkvefaas.models.release_request import ReleaseRequest
from volcenginesdkvefaas.models.release_response import ReleaseResponse
from volcenginesdkvefaas.models.rule_for_list_function_elastic_scale_strategy_output import RuleForListFunctionElasticScaleStrategyOutput
from volcenginesdkvefaas.models.rule_for_update_function_metric_scale_strategy_rules_input import RuleForUpdateFunctionMetricScaleStrategyRulesInput
from volcenginesdkvefaas.models.run_code_request import RunCodeRequest
from volcenginesdkvefaas.models.run_code_response import RunCodeResponse
from volcenginesdkvefaas.models.sandbox_for_list_sandboxes_output import SandboxForListSandboxesOutput
from volcenginesdkvefaas.models.scale_strategy_for_list_function_elastic_scale_strategy_output import ScaleStrategyForListFunctionElasticScaleStrategyOutput
from volcenginesdkvefaas.models.set_sandbox_timeout_request import SetSandboxTimeoutRequest
from volcenginesdkvefaas.models.set_sandbox_timeout_response import SetSandboxTimeoutResponse
from volcenginesdkvefaas.models.source_access_config_for_create_function_input import SourceAccessConfigForCreateFunctionInput
from volcenginesdkvefaas.models.source_access_config_for_update_function_input import SourceAccessConfigForUpdateFunctionInput
from volcenginesdkvefaas.models.tcp_socket_for_get_revision_output import TCPSocketForGetRevisionOutput
from volcenginesdkvefaas.models.tcp_socket_for_list_revisions_output import TCPSocketForListRevisionsOutput
from volcenginesdkvefaas.models.tag_filter_for_list_functions_input import TagFilterForListFunctionsInput
from volcenginesdkvefaas.models.tag_for_create_function_input import TagForCreateFunctionInput
from volcenginesdkvefaas.models.tag_for_list_functions_output import TagForListFunctionsOutput
from volcenginesdkvefaas.models.tag_for_update_function_input import TagForUpdateFunctionInput
from volcenginesdkvefaas.models.tag_for_update_function_output import TagForUpdateFunctionOutput
from volcenginesdkvefaas.models.terminate_async_task_request import TerminateAsyncTaskRequest
from volcenginesdkvefaas.models.terminate_async_task_response import TerminateAsyncTaskResponse
from volcenginesdkvefaas.models.tls_config_for_create_function_input import TlsConfigForCreateFunctionInput
from volcenginesdkvefaas.models.tls_config_for_create_function_output import TlsConfigForCreateFunctionOutput
from volcenginesdkvefaas.models.tls_config_for_get_function_output import TlsConfigForGetFunctionOutput
from volcenginesdkvefaas.models.tls_config_for_get_revision_output import TlsConfigForGetRevisionOutput
from volcenginesdkvefaas.models.tls_config_for_list_functions_output import TlsConfigForListFunctionsOutput
from volcenginesdkvefaas.models.tls_config_for_list_revisions_output import TlsConfigForListRevisionsOutput
from volcenginesdkvefaas.models.tls_config_for_update_function_input import TlsConfigForUpdateFunctionInput
from volcenginesdkvefaas.models.tls_config_for_update_function_output import TlsConfigForUpdateFunctionOutput
from volcenginesdkvefaas.models.tos_mount_config_for_create_function_input import TosMountConfigForCreateFunctionInput
from volcenginesdkvefaas.models.tos_mount_config_for_create_function_output import TosMountConfigForCreateFunctionOutput
from volcenginesdkvefaas.models.tos_mount_config_for_get_function_output import TosMountConfigForGetFunctionOutput
from volcenginesdkvefaas.models.tos_mount_config_for_get_revision_output import TosMountConfigForGetRevisionOutput
from volcenginesdkvefaas.models.tos_mount_config_for_list_functions_output import TosMountConfigForListFunctionsOutput
from volcenginesdkvefaas.models.tos_mount_config_for_list_revisions_output import TosMountConfigForListRevisionsOutput
from volcenginesdkvefaas.models.tos_mount_config_for_update_function_input import TosMountConfigForUpdateFunctionInput
from volcenginesdkvefaas.models.tos_mount_config_for_update_function_output import TosMountConfigForUpdateFunctionOutput
from volcenginesdkvefaas.models.tos_mount_point_for_create_sandbox_input import TosMountPointForCreateSandboxInput
from volcenginesdkvefaas.models.transition_sandbox_request import TransitionSandboxRequest
from volcenginesdkvefaas.models.transition_sandbox_response import TransitionSandboxResponse
from volcenginesdkvefaas.models.update_function_metric_scale_strategy_rules_request import UpdateFunctionMetricScaleStrategyRulesRequest
from volcenginesdkvefaas.models.update_function_metric_scale_strategy_rules_response import UpdateFunctionMetricScaleStrategyRulesResponse
from volcenginesdkvefaas.models.update_function_request import UpdateFunctionRequest
from volcenginesdkvefaas.models.update_function_resource_request import UpdateFunctionResourceRequest
from volcenginesdkvefaas.models.update_function_resource_response import UpdateFunctionResourceResponse
from volcenginesdkvefaas.models.update_function_response import UpdateFunctionResponse
from volcenginesdkvefaas.models.update_kafka_trigger_request import UpdateKafkaTriggerRequest
from volcenginesdkvefaas.models.update_kafka_trigger_response import UpdateKafkaTriggerResponse
from volcenginesdkvefaas.models.update_release_request import UpdateReleaseRequest
from volcenginesdkvefaas.models.update_release_response import UpdateReleaseResponse
from volcenginesdkvefaas.models.update_timer_request import UpdateTimerRequest
from volcenginesdkvefaas.models.update_timer_response import UpdateTimerResponse
from volcenginesdkvefaas.models.vpc_config_for_create_function_input import VpcConfigForCreateFunctionInput
from volcenginesdkvefaas.models.vpc_config_for_create_function_output import VpcConfigForCreateFunctionOutput
from volcenginesdkvefaas.models.vpc_config_for_get_function_output import VpcConfigForGetFunctionOutput
from volcenginesdkvefaas.models.vpc_config_for_get_revision_output import VpcConfigForGetRevisionOutput
from volcenginesdkvefaas.models.vpc_config_for_list_functions_output import VpcConfigForListFunctionsOutput
from volcenginesdkvefaas.models.vpc_config_for_list_revisions_output import VpcConfigForListRevisionsOutput
from volcenginesdkvefaas.models.vpc_config_for_update_function_input import VpcConfigForUpdateFunctionInput
from volcenginesdkvefaas.models.vpc_config_for_update_function_output import VpcConfigForUpdateFunctionOutput
