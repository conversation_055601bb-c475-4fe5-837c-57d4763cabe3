# Example Code generated by Beijing Volcanoengine Technology.
from __future__ import print_function
import volcenginesdkcore
import volcenginesdkcen
from pprint import pprint
from volcenginesdkcore.rest import ApiException

if __name__ == '__main__':
    configuration = volcenginesdkcore.Configuration()
    configuration.ak = "Your AK"
    configuration.sk = "Your SK"
    configuration.region = "cn-beijing"
    # set default configuration
    volcenginesdkcore.Configuration.set_default(configuration)

    # use global default configuration
    api_instance = volcenginesdkcen.CENApi()
    create_cen_service_route_entry_request = volcenginesdkcen.CreateCenServiceRouteEntryRequest(
        cen_id="cen-2nim00ybaylts7trquyzt****",
        destination_cidr_block="100.XX.XX.0/24",
        service_region_id="cn-beijing",
        service_vpc_id="vpc-3rlkeggyn6tc010exd32q****",
    )
    
    try:
        resp = api_instance.create_cen_service_route_entry(create_cen_service_route_entry_request)
        pprint(resp)
    except ApiException as e:
        print("Exception when calling api: %s\n" % e)
