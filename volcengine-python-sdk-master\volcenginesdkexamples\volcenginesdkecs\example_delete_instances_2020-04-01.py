# Example Code generated by Beijing Volcanoengine Technology.
from __future__ import print_function
import volcenginesdkcore
import volcenginesdkecs
from pprint import pprint
from volcenginesdkcore.rest import ApiException

if __name__ == '__main__':
    configuration = volcenginesdkcore.Configuration()
    configuration.ak = "Your AK"
    configuration.sk = "Your SK"
    configuration.region = "cn-beijing"
    # set default configuration
    volcenginesdkcore.Configuration.set_default(configuration)

    # use global default configuration
    api_instance = volcenginesdkecs.ECSApi()
    delete_instances_request = volcenginesdkecs.DeleteInstancesRequest(
        instance_ids=["i-ybo349sxoncm9t******", "i-ybo349sxolcm9t******"],
    )
    
    try:
        resp = api_instance.delete_instances(delete_instances_request)
        pprint(resp)
    except ApiException as e:
        print("Exception when calling api: %s\n" % e)
