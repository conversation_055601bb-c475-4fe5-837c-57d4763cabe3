------



## 在 `doubao-test` 目录下从源码安装 `volcengine-python-sdk` 并启用 `[ark]` 功能



这个文档将指导您如何在您的 `doubao-test` 项目中，从源码安装 `volcengine-python-sdk`，并确保其 `[ark]` 扩展功能所需的依赖也正确安装。这样做可以避免直接通过 `pip install 'volcengine-python-sdk[ark]'` 时的路径过长问题，并允许您在自己的 GitHub 仓库中管理这个 SDK 的特定版本。

------



### 前提条件



在开始之前，请确保您已经完成以下准备：

- **已安装 Python 环境：** 建议使用 Python 3.8 或更高版本。

- **已安装 pip：** Python 包管理器，通常随 Python 一起安装。

- **已将 `volcengine-python-sdk-master` 源码复制到您的项目目录：** 确保 `volcengine-python-sdk-master` 文件夹位于您的 `doubao-test` 目录下。您的项目结构可能看起来像这样：

  ```
  doubao-test/
  ├── volcengine-python-sdk-master/
  │   ├── setup.py
  │   ├── pyproject.toml (可能存在)
  │   └── ... (其他SDK文件)
  └── your_project_files.py
  ```

- **已激活虚拟环境（推荐）：** 强烈建议为您的项目创建一个独立的虚拟环境，以避免包冲突。

  Bash

  ```
  # 在 doubao-test 目录下创建虚拟环境
  python -m venv venv
  
  # 激活虚拟环境 (Windows)
  .\venv\Scripts\activate
  
  # 激活虚拟环境 (macOS/Linux)
  source venv/bin/activate
  ```

------



### 步骤 1：构建并安装 `volcengine-python-sdk` 基础包



首先，我们需要进入 `volcengine-python-sdk-master` 源码目录，并构建、安装其基础包。

1. **打开命令行工具** (CMD 或 PowerShell)。

2. **导航到 `volcengine-python-sdk-master` 目录：** 假设您的 `doubao-test` 目录在 `C:\Users\<USER>\Documents` 下。

   Bash

   ```
   cd C:\Users\<USER>\Documents\doubao-test\volcengine-python-sdk-master
   ```

   请将路径替换为您的实际路径。

3. **安装构建工具：**

   Bash

   ```
   pip install build
   ```

4. 从源码构建 volcengine-python-sdk：

   这将生成一个 .whl 文件（wheel 文件），它是 Python 的二进制分发格式，用于方便安装。

   Bash

   ```
   python -m build
   ```

   构建成功后，您会在 `C:\Users\<USER>\Documents\doubao-test\volcengine-python-sdk-master\dist` 目录下找到一个类似于 `volcengine_python_sdk-X.X.X-py3-none-any.whl` 的文件。

5. **安装生成的 `wheel` 文件：**

   Bash

   ```
   pip install dist\volcengine_python_sdk-4.0.5-py3-none-any.whl
   ```

   这条命令会安装 `dist` 目录下最新生成的 `wheel` 文件。至此，`volcengine-python-sdk` 的基础功能已经安装完成。

------



### 步骤 2：手动安装 `[ark]` 额外依赖



`volcenginesdkarkruntime` 模块需要 `volcengine-python-sdk` 的 `[ark]` 额外依赖。由于我们是源码安装，`pip` 无法自动处理这些额外依赖。我们需要根据 `setup.py` 文件中的定义，手动安装它们。

根据您提供的 `setup.py` 文件，`[ark]` 依赖包括：

- `pydantic>=1.9.0, <3`
- `httpx>=0.23.0, <1`
- `anyio>=3.5.0, <5`
- `cached-property` (仅当 Python 版本低于 3.8 时需要安装)
- `cryptography>=42.0.0`

请根据您的 Python 版本执行相应的安装命令：

1. **确保您仍在** **激活的虚拟环境中**（如果您之前创建并激活了）。如果您关闭了命令行窗口，需要重新激活虚拟环境。

2. **根据您的 Python 版本选择并执行安装命令：**

   - **如果您的 Python 版本是 3.8 或更高版本 (例如 3.9, 3.10, 3.11 等)：**

     Bash

     ```
     pip install "pydantic>=1.9.0,<3" "httpx>=0.23.0,<1" "anyio>=3.5.0,<5" "cryptography>=42.0.0"
     ```

   - **如果您的 Python 版本低于 3.8 (例如 3.7 或更早版本)：**

     Bash

     ```
     pip install "pydantic>=1.9.0,<3" "httpx>=0.23.0,<1" "anyio>=3.5.0,<5" "cached-property" "cryptography>=42.0.0"
     ```

   **注意：** 使用双引号将版本范围括起来是确保命令行正确解析的良好实践。

------



### 步骤 3：验证安装



所有依赖安装完成后，您可以验证 `volcenginesdkarkruntime.Ark` 是否可以正常导入和使用。

1. **在命令行中打开 Python 解释器：**

   Bash

   ```
   python
   ```

2. **尝试导入 `Ark` 模块：**

   Python

   ```
   from volcenginesdkarkruntime import Ark
   print("volcenginesdkarkruntime.Ark 导入成功！")
   
   # 您也可以尝试创建 Ark 实例来进一步验证
   # from volcenginesdkarkruntime.models import ChatCompletionRequest
   # from volcenginesdkarkruntime.models import ChatCompletionMessage
   
   # client = Ark(
   #     volcengine_access_key_id="YOUR_ACCESS_KEY", # 替换为您的实际密钥
   #     volcengine_secret_access_key="YOUR_SECRET_KEY", # 替换为您的实际密钥
   #     volcengine_region="cn-beijing" # 替换为您的实际区域
   # )
   # print(client)
   ```

   如果以上命令没有报错 `ModuleNotFoundError` 或其他相关错误，并且 `Ark` 对象能被正确创建（如果进行了尝试），则说明 `volcengine-python-sdk` 及其 `[ark]` 依赖已成功安装并准备就绪。

------

通过遵循这些步骤，您应该能够在您的 `doubao-test` 项目中成功使用 `volcengine-python-sdk` 的 `[ark]` 功能。