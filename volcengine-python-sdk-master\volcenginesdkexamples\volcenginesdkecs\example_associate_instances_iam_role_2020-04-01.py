# Example Code generated by Beijing Volcanoengine Technology.
from __future__ import print_function
import volcenginesdkcore
import volcenginesdkecs
from pprint import pprint
from volcenginesdkcore.rest import ApiException

if __name__ == '__main__':
    configuration = volcenginesdkcore.Configuration()
    configuration.ak = "Your AK"
    configuration.sk = "Your SK"
    configuration.region = "cn-beijing"
    # set default configuration
    volcenginesdkcore.Configuration.set_default(configuration)

    # use global default configuration
    api_instance = volcenginesdkecs.ECSApi()
    associate_instances_iam_role_request = volcenginesdkecs.AssociateInstancesIamRoleRequest(
        iam_role_name="EcsTestRole",
        instance_ids=["i-3tiefmkskq3vj0******"],
    )
    
    try:
        resp = api_instance.associate_instances_iam_role(associate_instances_iam_role_request)
        pprint(resp)
    except ApiException as e:
        print("Exception when calling api: %s\n" % e)
