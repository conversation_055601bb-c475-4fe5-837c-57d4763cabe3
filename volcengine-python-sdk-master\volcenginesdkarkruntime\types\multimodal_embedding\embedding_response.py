from typing import List
from typing_extensions import Literal

from .embedding_data import Multimodal<PERSON><PERSON>dding
from .embedding_usage import MultimodalEmbeddingUsage
from ..._models import BaseModel

__all__ = ["MultimodalEmbeddingResponse"]


class MultimodalEmbeddingResponse(BaseModel):
    id: str
    """A unique identifier for the embeddings."""

    created: int
    """The Unix timestamp (in seconds) of when the embeddings was created."""

    data: List[MultimodalEmbedding]
    """The list of embeddings generated by the model."""

    model: str
    """The name of the model used to generate the embedding."""

    object: Literal["list"]
    """The object type, which is always "list"."""

    usage: MultimodalEmbeddingUsage
    """The usage information for the request."""
