# Example Code generated by Beijing Volcanoengine Technology.
from __future__ import print_function
import volcenginesdkcore
import volcenginesdkvpn
from pprint import pprint
from volcenginesdkcore.rest import ApiException

if __name__ == '__main__':
    configuration = volcenginesdkcore.Configuration()
    configuration.ak = "AK"
    configuration.sk = "SK"
    configuration.region = "cn-beijing"
    # set default configuration
    volcenginesdkcore.Configuration.set_default(configuration)

    # use global default configuration
    api_instance = volcenginesdkvpn.VPNApi()
    create_vpn_gateway_request = volcenginesdkvpn.CreateVpnGatewayRequest(
        bandwidth=5,
        billing_type=1,
        description="test",
        period=1,
        period_unit="Month",
        subnet_id="subnet-2fewbgn7jbk0059gp67ap****",
        vpc_id="vpc-12bhs1ivo6p6o17q7y2x3****",
        vpn_gateway_name="test",
    )

    try:
        resp = api_instance.create_vpn_gateway(create_vpn_gateway_request)
        pprint(resp)
    except ApiException as e:
        print("Exception when calling api: %s\n" % e)
