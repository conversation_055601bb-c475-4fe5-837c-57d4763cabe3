__all__ = ["ContentGenerationTask"]

from volcenginesdkarkruntime._models import BaseModel


class Usage(BaseModel):
    completion_tokens: int
    """The number of tokens used for completion."""


class Content(BaseModel):
    video_url: str
    """The URL of the generated video, if any."""


class ContentGenerationError(BaseModel):
    message: str
    """The reason for task failure"""

    code: str
    """The error code for task failure"""


class ContentGenerationTask(BaseModel):
    id: str
    """A unique identifier for the task."""

    model: str
    """The model used for the task."""

    status: str
    """The status of the task (running, failed, queued, succeeded, cancelled)."""

    error: ContentGenerationError
    """The error body, if applicable."""

    content: Content
    """The content generated by the task."""

    usage: Usage
    """The usage information for the task."""

    created_at: int
    """The Unix timestamp when the task was created."""

    updated_at: int
    """The Unix timestamp when the task was last updated."""

    seed: int
    """The seed used in the model."""

    revised_prompt: str
    """The revised prompt the model uses to generate content"""
