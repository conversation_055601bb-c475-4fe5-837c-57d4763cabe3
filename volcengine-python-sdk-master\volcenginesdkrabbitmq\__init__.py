# coding: utf-8

# flake8: noqa

"""
    rabbitmq

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


from __future__ import absolute_import

# import apis into sdk package
from volcenginesdkrabbitmq.api.rabbitmq_api import RABBITMQApi

# import models into sdk package
from volcenginesdkrabbitmq.models.add_tags_to_resource_request import AddTagsToResourceRequest
from volcenginesdkrabbitmq.models.add_tags_to_resource_response import AddTagsToResourceResponse
from volcenginesdkrabbitmq.models.basic_instance_info_for_describe_instance_detail_output import BasicInstanceInfoForDescribeInstanceDetailOutput
from volcenginesdkrabbitmq.models.charge_detail_for_describe_instance_detail_output import ChargeDetailForDescribeInstanceDetailOutput
from volcenginesdkrabbitmq.models.charge_detail_for_describe_instances_output import ChargeDetailForDescribeInstancesOutput
from volcenginesdkrabbitmq.models.charge_info_for_create_instance_input import ChargeInfoForCreateInstanceInput
from volcenginesdkrabbitmq.models.charge_info_for_modify_instance_charge_type_input import ChargeInfoForModifyInstanceChargeTypeInput
from volcenginesdkrabbitmq.models.create_instance_request import CreateInstanceRequest
from volcenginesdkrabbitmq.models.create_instance_response import CreateInstanceResponse
from volcenginesdkrabbitmq.models.create_public_address_request import CreatePublicAddressRequest
from volcenginesdkrabbitmq.models.create_public_address_response import CreatePublicAddressResponse
from volcenginesdkrabbitmq.models.delete_instance_request import DeleteInstanceRequest
from volcenginesdkrabbitmq.models.delete_instance_response import DeleteInstanceResponse
from volcenginesdkrabbitmq.models.delete_public_address_request import DeletePublicAddressRequest
from volcenginesdkrabbitmq.models.delete_public_address_response import DeletePublicAddressResponse
from volcenginesdkrabbitmq.models.describe_availability_zones_request import DescribeAvailabilityZonesRequest
from volcenginesdkrabbitmq.models.describe_availability_zones_response import DescribeAvailabilityZonesResponse
from volcenginesdkrabbitmq.models.describe_instance_detail_request import DescribeInstanceDetailRequest
from volcenginesdkrabbitmq.models.describe_instance_detail_response import DescribeInstanceDetailResponse
from volcenginesdkrabbitmq.models.describe_instances_request import DescribeInstancesRequest
from volcenginesdkrabbitmq.models.describe_instances_response import DescribeInstancesResponse
from volcenginesdkrabbitmq.models.describe_plugins_request import DescribePluginsRequest
from volcenginesdkrabbitmq.models.describe_plugins_response import DescribePluginsResponse
from volcenginesdkrabbitmq.models.describe_regions_request import DescribeRegionsRequest
from volcenginesdkrabbitmq.models.describe_regions_response import DescribeRegionsResponse
from volcenginesdkrabbitmq.models.describe_tags_by_resource_request import DescribeTagsByResourceRequest
from volcenginesdkrabbitmq.models.describe_tags_by_resource_response import DescribeTagsByResourceResponse
from volcenginesdkrabbitmq.models.endpoint_for_describe_instance_detail_output import EndpointForDescribeInstanceDetailOutput
from volcenginesdkrabbitmq.models.instances_info_for_describe_instances_output import InstancesInfoForDescribeInstancesOutput
from volcenginesdkrabbitmq.models.modify_instance_attributes_request import ModifyInstanceAttributesRequest
from volcenginesdkrabbitmq.models.modify_instance_attributes_response import ModifyInstanceAttributesResponse
from volcenginesdkrabbitmq.models.modify_instance_charge_type_request import ModifyInstanceChargeTypeRequest
from volcenginesdkrabbitmq.models.modify_instance_charge_type_response import ModifyInstanceChargeTypeResponse
from volcenginesdkrabbitmq.models.modify_instance_spec_request import ModifyInstanceSpecRequest
from volcenginesdkrabbitmq.models.modify_instance_spec_response import ModifyInstanceSpecResponse
from volcenginesdkrabbitmq.models.modify_plugin_request import ModifyPluginRequest
from volcenginesdkrabbitmq.models.modify_plugin_response import ModifyPluginResponse
from volcenginesdkrabbitmq.models.modify_user_password_request import ModifyUserPasswordRequest
from volcenginesdkrabbitmq.models.modify_user_password_response import ModifyUserPasswordResponse
from volcenginesdkrabbitmq.models.plugin_for_modify_plugin_input import PluginForModifyPluginInput
from volcenginesdkrabbitmq.models.plugins_info_for_describe_plugins_output import PluginsInfoForDescribePluginsOutput
from volcenginesdkrabbitmq.models.region_for_describe_regions_output import RegionForDescribeRegionsOutput
from volcenginesdkrabbitmq.models.remove_tags_from_resource_request import RemoveTagsFromResourceRequest
from volcenginesdkrabbitmq.models.remove_tags_from_resource_response import RemoveTagsFromResourceResponse
from volcenginesdkrabbitmq.models.restart_instance_request import RestartInstanceRequest
from volcenginesdkrabbitmq.models.restart_instance_response import RestartInstanceResponse
from volcenginesdkrabbitmq.models.tag_filter_for_describe_instances_input import TagFilterForDescribeInstancesInput
from volcenginesdkrabbitmq.models.tag_filter_for_describe_tags_by_resource_input import TagFilterForDescribeTagsByResourceInput
from volcenginesdkrabbitmq.models.tag_for_add_tags_to_resource_input import TagForAddTagsToResourceInput
from volcenginesdkrabbitmq.models.tag_for_create_instance_input import TagForCreateInstanceInput
from volcenginesdkrabbitmq.models.tag_for_describe_instance_detail_output import TagForDescribeInstanceDetailOutput
from volcenginesdkrabbitmq.models.tag_for_describe_instances_output import TagForDescribeInstancesOutput
from volcenginesdkrabbitmq.models.tag_resource_for_describe_tags_by_resource_output import TagResourceForDescribeTagsByResourceOutput
from volcenginesdkrabbitmq.models.zone_for_describe_availability_zones_output import ZoneForDescribeAvailabilityZonesOutput
