# Example Code generated by Beijing Volcanoengine Technology.
from __future__ import print_function
import volcenginesdkcore
import volcenginesdkclb
from pprint import pprint
from volcenginesdkcore.rest import ApiException

if __name__ == '__main__':
    configuration = volcenginesdkcore.Configuration()
    configuration.ak = "Your AK"
    configuration.sk = "Your SK"
    configuration.region = "cn-beijing"
    # set default configuration
    volcenginesdkcore.Configuration.set_default(configuration)

    # use global default configuration
    api_instance = volcenginesdkclb.CLBApi()
    set_load_balancer_renewal_request = volcenginesdkclb.SetLoadBalancerRenewalRequest(
        load_balancer_id="clb-bp1b6c719dfa08ex****",
        renew_type=2,
    )
    
    try:
        resp = api_instance.set_load_balancer_renewal(set_load_balancer_renewal_request)
        pprint(resp)
    except ApiException as e:
        print("Exception when calling api: %s\n" % e)
