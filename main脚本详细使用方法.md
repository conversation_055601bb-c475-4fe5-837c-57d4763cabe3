# main.py 脚本详细使用方法

## 概述

`main.py` 是整个工具集的核心控制脚本，提供统一的入口来管理单阶段和双阶段图像处理流程。它支持两种运行模式：手动输入模式和批处理配置模式，并提供强大的配置管理功能。

## 主要功能特性

### 🎛️ 双模式支持
- **手动输入模式**：交互式选择参数，适合单次测试
- **批处理配置模式**：从JSON文件读取配置，支持批量自动化处理

### 🔄 流程控制
- **单阶段模式**：调用 `one_stage_test.py` 进行一站式处理
- **双阶段不发图片模式**：先调用 `test.py`，再调用 `test2.py`
- **双阶段发图片模式**：先调用 `test.py`，再调用 `test3.py`

### 🎯 智能配置管理
- 支持从Markdown文件读取提示词
- 自动创建配置副本，便于调试和记录
- 支持命令行参数管理配置文件

## 运行方式

### 基本运行
```bash
python main.py
```

### 命令行参数
```bash
# 列出所有可用的配置文件
python main.py --list

# 重命名配置文件为数字序列（1.json, 2.json等）
python main.py --rename

# 指定特定的配置文件
python main.py --name 配置文件名.json
```

## 手动输入模式详解

### 1. 选择输入模式
运行脚本后，首先选择输入模式：
```
请选择输入模式：
1. 手动输入
2. batch_configs读取
请输入选择（1-2）：
```

### 2. 选择处理模式
选择手动输入后，需要选择处理模式：
```
请选择处理模式：
1. 单阶段
2. 双阶段（不发图片）
3. 双阶段（发图片）
请输入选择（1-3）：
```

### 3. 模式说明

#### 单阶段模式
- 直接调用 `one_stage_test.py`
- 将图片处理和答题分析合并为单个步骤
- 适合快速测试和简单场景

#### 双阶段不发图片模式
- 第一阶段：调用 `test.py` 进行图片处理
- 第二阶段：调用 `test2.py` 进行答题分析（不发送图片）
- 支持选择批改模式：大模型批改或JSON比对

#### 双阶段发图片模式
- 第一阶段：调用 `test.py` 进行图片处理
- 第二阶段：调用 `test3.py` 进行答题分析（发送图片）
- 适合需要图像上下文的复杂分析场景

## 批处理配置模式详解

### 1. 配置文件结构

配置文件应放在 `batch_configs` 文件夹下，基本结构如下：

```json
{
  "batch_configs": [
    {
      "处理模式": 1,
      "round2图片": 1,
      "round2批改模式": 1,
      "模型ID": 1,
      "题型": 13,
      "图像文件夹": 1,
      "像素增强": "y",
      "像素粘连": "y",
      "图像放大倍数": 2,
      "response_format": "json_object",
      "temperature": 0.8,
      "top_p": 0.9,
      "max_tokens": 16384,
      "gray_threshold": 200,
      "test_prompt": "你是一个专业的批改老师...",
      "test2_prompt": "prompt.md",
      "test3_prompt": "请仔细分析图片...",
      "one_stage_test_prompt": "one_stage_prompt.md"
    }
  ]
}
```

### 2. 参数详细说明

#### 基础参数
- **处理模式**：
  - `1` = 单阶段
  - `2` = 双阶段不发图片
  - `3` = 双阶段发图片

- **round2图片**（仅双阶段时有效）：
  - `1` = 是
  - `2` = 否

- **round2批改模式**（仅双阶段不发图片时有效）：
  - `1` = 大模型批改
  - `2` = JSON比对

#### 模型和题型参数
- **模型ID**：
  - `1` = doubao-seed-1-6-250615
  - `2` = doubao-1-5-vision-pro-32k-250115
  - `3` = doubao-1-5-vision-pro-32k-250115
  - `4` = doubao-1-5-vision-pro-32k-250115

- **题型**：
  - `1` = 涂卡选择题
  - `2` = 涂卡判断题
  - `3` = 连线题
  - `4` = 图表题
  - `5` = 翻译题
  - `6` = 画图题
  - `7` = 数学应用题
  - `8` = 数学计算题
  - `9` = 简单的四则运算
  - `10` = 填空题
  - `11` = 判断题
  - `12` = 多选题
  - `13` = 单选题

#### 图像处理参数
- **图像文件夹**：
  - `1` = images
  - `2` = images2
  - `3` = images3
  - `4` = images4
  - `5` = images5
  - `6` = images6
  - `7` = images7

- **像素增强**：
  - `"y"` = 启用灰度阀门与像素增强
  - `"n"` = 不启用

- **像素粘连**：
  - `"y"` = 启用黑色像素粘连处理
  - `"n"` = 不启用（默认值）

- **图像放大倍数**：数字，如 `2`、`4`、`6` 等

- **gray_threshold**：灰度阀门值，范围 0-255，默认 200

#### API参数
- **response_format**：
  - `"text"` = 文本格式
  - `"json_object"` = JSON对象格式

- **temperature**：API温度参数，范围 0.0-2.0，默认 1.0
- **top_p**：API top_p参数，范围 0.0-1.0，默认 0.7
- **max_tokens**：最大token数，根据模型自动设置

#### 自定义提示词参数
- **test_prompt**：第一阶段提示词
- **test2_prompt**：第二阶段提示词（不发图片）
- **test3_prompt**：第二阶段提示词（发图片）
- **one_stage_test_prompt**：单阶段提示词

提示词支持两种格式：
1. **直接文本**：在JSON中直接写入提示词内容
2. **Markdown文件**：以 `.md` 结尾，从 `batch_configs/prompt/` 目录读取

### 3. 配置文件管理

#### 自动选择最新配置
程序会自动选择 `batch_configs` 文件夹中时间最晚的配置文件。

#### 配置文件重命名
使用 `--rename` 参数可以将所有配置文件重命名为数字序列：
```bash
python main.py --rename
```
这会将文件重命名为 `1.json`、`2.json`、`3.json` 等。

#### 列出可用配置
使用 `--list` 参数查看所有可用的配置文件：
```bash
python main.py --list
```

#### 配置副本功能
程序会自动创建配置副本到 `batch_configs/batch_configs_copy/` 目录，副本中的Markdown提示词会被转换为实际的文本内容，便于调试和记录。

## 输出说明

### 手动输入模式输出
输出与选择的具体脚本相同（one_stage_test.py、test.py等）。

### 批处理配置模式输出

#### 执行过程
- 实时显示每个批处理配置的执行状态和参数
- 显示当前处理的配置编号和总数
- 显示每个阶段的执行结果

#### 最终总结
程序会在所有批处理完成后显示详细总结：

```
=== 批处理执行总结 ===
总批处理数量: 3
成功执行: 2
执行失败: 1
成功率: 66.67%

=== 成功执行的批处理 ===
✓ 第 1 个配置（单阶段）处理完成！
  输出文档: d:\mycode\doubao-test\types\danxuanti\one_stage_response\one_stage_response_2025-08-03 15-30-45.md

✓ 第 3 个配置（双阶段-不发图片）处理完成！
  第一阶段输出: d:\mycode\doubao-test\types\danxuanti\response\response_2025-08-03 15-35-20.md
  第二阶段输出: d:\mycode\doubao-test\types\danxuanti\round2_response_without_images\round2_response_without_images_2025-08-03 15-40-15.md

=== 失败的批处理 ===
✗ 第 2 个配置处理失败
  错误信息: 第一阶段test.py运行失败
```

## 注意事项

### 配置文件要求
- JSON格式必须正确
- 所有必需参数都要提供
- 提示词文件路径要正确

### 文件夹结构要求
- 图片文件要放在对应题型的指定文件夹中
- 提示词文件要放在正确的位置
- 确保有写入权限

### 错误处理
- 单个批处理失败不会影响其他批处理的执行
- 程序会记录详细的错误信息
- 建议在批处理前先用手动模式测试

### 性能建议
- 批处理模式适合大量重复性任务
- 手动模式适合调试和单次测试
- 合理设置API参数以平衡质量和速度

## 示例配置文件

### 单阶段处理示例
```json
{
  "batch_configs": [
    {
      "处理模式": 1,
      "模型ID": 1,
      "题型": 13,
      "图像文件夹": 1,
      "像素增强": "n",
      "像素粘连": "n",
      "图像放大倍数": 1,
      "temperature": 0.8,
      "top_p": 0.9,
      "max_tokens": 8192,
      "one_stage_test_prompt": "one_stage_prompt.md"
    }
  ]
}
```

### 双阶段不发图片处理示例
```json
{
  "batch_configs": [
    {
      "处理模式": 2,
      "round2图片": 2,
      "round2批改模式": 2,
      "模型ID": 2,
      "题型": 13,
      "图像文件夹": 1,
      "像素增强": "n",
      "像素粘连": "n",
      "图像放大倍数": 1,
      "temperature": 0.5,
      "top_p": 0.8,
      "test_prompt": "prompt.md",
      "test2_prompt": "round2_prompt.md"
    }
  ]
}
```

### 双阶段发图片处理示例
```json
{
  "batch_configs": [
    {
      "处理模式": 3,
      "模型ID": 1,
      "题型": 13,
      "图像文件夹": 1,
      "像素增强": "n",
      "像素粘连": "n",
      "图像放大倍数": 1,
      "test_prompt": "prompt.md",
      "test3_prompt": "round2_prompt_new.md"
    }
  ]
}
```

### 混合配置示例（多种处理模式）
```json
{
  "batch_configs": [
    {
      "处理模式": 1,
      "模型ID": 1,
      "题型": 13,
      "图像文件夹": 1,
      "像素增强": "n",
      "像素粘连": "n",
      "图像放大倍数": 1,
      "one_stage_test_prompt": "one_stage_prompt.md"
    },
    {
      "处理模式": 2,
      "round2批改模式": 2,
      "模型ID": 1,
      "题型": 13,
      "图像文件夹": 1,
      "像素增强": "n",
      "像素粘连": "n",
      "图像放大倍数": 1,
      "test_prompt": "prompt.md",
      "test2_prompt": "round2_prompt.md"
    },
    {
      "处理模式": 3,
      "模型ID": 1,
      "题型": 13,
      "图像文件夹": 1,
      "像素增强": "n",
      "像素粘连": "n",
      "图像放大倍数": 1,
      "test_prompt": "prompt.md",
      "test3_prompt": "round2_prompt_new.md"
    },
    {
      "处理模式": 1,
      "模型ID": 1,
      "题型": 13,
      "图像文件夹": 1,
      "像素增强": "n",
      "像素粘连": "n",
      "图像放大倍数": 1,
      "one_stage_test_prompt": "这是一个直接的字符串prompt，不是md文件。请分析图片内容并给出JSON格式答案。"
    }
  ]
}
```

### 完整参数示例
```json
{
  "batch_configs": [
    {
      "处理模式": 2,
      "round2图片": 2,
      "round2批改模式": 1,
      "模型ID": 2,
      "题型": 10,
      "图像文件夹": 1,
      "像素增强": "y",
      "像素粘连": "y",
      "图像放大倍数": 4,
      "response_format": "json_object",
      "temperature": 1.0,
      "top_p": 0.8,
      "max_tokens": 12288,
      "gray_threshold": 180,
      "test_prompt": "你是一个专业的批改老师，请仔细分析图片中的题目...",
      "test2_prompt": "round2_prompt.md"
    }
  ]
}
```

## 常见问题解答

### Q: 如何选择合适的处理模式？
A:
- **单阶段模式**：适合简单场景，处理速度快，一次性完成图片处理和答题分析
- **双阶段不发图片模式**：适合需要分步处理的场景，第二阶段不需要图像上下文
- **双阶段发图片模式**：适合复杂分析场景，第二阶段需要图像上下文信息

### Q: 提示词应该使用文件还是直接文本？
A:
- **Markdown文件**：适合长提示词，便于维护和版本控制，以`.md`结尾
- **直接文本**：适合短提示词或临时测试，直接在JSON中编写

### Q: 如何优化API参数？
A:
- **temperature**：控制输出随机性，0.0-1.0适合精确任务，1.0-2.0适合创造性任务
- **top_p**：控制词汇选择范围，0.7-0.9是常用范围
- **max_tokens**：根据任务复杂度设置，简单任务可以设置较小值

### Q: 批处理失败如何排查？
A:
1. 检查JSON格式是否正确
2. 确认文件路径是否存在
3. 检查参数值是否在有效范围内
4. 查看详细的错误信息
5. 先用手动模式测试单个配置

这个文档提供了 main.py 脚本的完整使用指南，涵盖了所有功能特性和配置选项。
