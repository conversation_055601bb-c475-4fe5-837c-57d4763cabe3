#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
像素增强和像素粘连处理脚本
功能：对images文件夹下的图片进行像素增强和像素粘连处理，生成处理后的图片到pixel_enhancement_result文件夹
"""

import os
import shutil
import io
import subprocess  # 新增：导入子进程模块
from PIL import Image
import numpy as np
from pypinyin import pinyin, Style
import time
import multiprocessing  # 新增：导入多进程模块

# 尝试导入tqdm，如果不可用则使用简单的进度显示
try:
    from tqdm import tqdm
    TQDM_AVAILABLE = True
except ImportError:
    TQDM_AVAILABLE = False
    print("提示：安装 tqdm 可以获得更好的进度显示效果：pip install tqdm")

def chinese_to_pinyin(chinese_text):
    """将中文转换为拼音（无声调）"""
    pinyin_list = pinyin(chinese_text, style=Style.NORMAL)
    return ''.join([p[0] for p in pinyin_list])

def get_question_type():
    """获取用户输入的题型并转换为拼音路径"""
    question_types = {
        "1": "涂卡选择题",
        "2": "涂卡判断题",
        "3": "连线题",
        "4": "图表题",
        "5": "翻译题",
        "6": "画图题",
        "7": "数学应用题",
        "8": "数学计算题",
        "9": "简单的四则运算",
        "10": "填空题",
        "11": "判断题",
        "12": "多选题",
        "13": "单选题"
    }

    print("请选择题型：")
    for key, value in question_types.items():
        print(f"{key}. {value}")

    while True:
        user_input = input("请输入题型编号（1-13）或直接输入中文：").strip()

        # 检查数字输入
        if user_input in question_types:
            question_type = question_types[user_input]
            break
        # 检查中文输入
        elif user_input in question_types.values():
            question_type = user_input
            break
        else:
            print("输入无效，请输入 1-13 的数字或直接输入中文题型名称")

    # 转换为拼音
    pinyin_name = chinese_to_pinyin(question_type)
    print(f"选择的题型：{question_type}")
    print(f"对应的拼音路径：{pinyin_name}")

    return question_type, pinyin_name

def enhance_marks_to_black(image_data, threshold=220, use_pixel_connection=False):
    """
    将图片中的灰色或浅黑色标记增强为纯黑色，并将背景转换为纯白色。
    可选择是否采用黑色像素粘连处理。

    Args:
        image_data: 图片数据
        threshold: 阈值，低于该值的像素变为黑色，否则为白色
        use_pixel_connection: 是否采用黑色像素粘连处理
    """
    image = Image.open(io.BytesIO(image_data))
    grayscale_image = image.convert('L')
    threshold_image = grayscale_image.point(lambda p: 0 if p < threshold else 255)

    if use_pixel_connection:
        # 应用黑色像素粘连处理
        arr = np.array(threshold_image)

        # 横向扫描，相差两个像素进行粘连
        for y in range(arr.shape[0]):
            for x in range(2, arr.shape[1] - 2):
                if arr[y, x - 2] == 0 and arr[y, x - 1] == 255 and arr[y, x] == 255 and arr[y, x + 1] == 255 and \
                        arr[y, x + 2] == 0:
                    arr[y, x - 1] = 0
                    arr[y, x] = 0
                    arr[y, x + 1] = 0

        # 纵向扫描，相差两个像素进行粘连
        for x in range(arr.shape[1]):
            for y in range(2, arr.shape[0] - 2):
                if arr[y - 2, x] == 0 and arr[y - 1, x] == 255 and arr[y, x] == 255 and arr[y + 1, x] == 255 and \
                        arr[y + 2, x] == 0:
                    arr[y - 1, x] = 0
                    arr[y, x] = 0
                    arr[y + 1, x] = 0

        final_image = Image.fromarray(arr).convert('RGB')
    else:
        final_image = threshold_image.convert('RGB')

    return final_image

def process_single_image(task):
    """
    处理单张图片：增强、缩放、保存
    
    Args:
        task: 包含处理参数的任务字典
    """
    # 从任务字典中提取参数
    image_path = task['image_path']
    output_dir = task['output_dir']
    use_enhance = task['use_enhance']
    enhance_threshold = task['enhance_threshold']
    scale = task['scale']
    use_pixel_connection = task['use_pixel_connection']
    try:
        # 读取图片
        with open(image_path, "rb") as image_file:
            image_data = image_file.read()
        
        # 获取原始文件名（不含扩展名）和扩展名
        filename = os.path.basename(image_path)
        name_without_ext, ext = os.path.splitext(filename)
        
        # 处理图片
        if use_enhance:
            # 应用像素增强
            enhanced_image = enhance_marks_to_black(image_data, enhance_threshold, use_pixel_connection)
        else:
            # 不增强，直接打开
            enhanced_image = Image.open(io.BytesIO(image_data))
        
        # 应用缩放
        if scale != 1.0:
            original_width, original_height = enhanced_image.size
            new_width = int(original_width * scale)
            new_height = int(original_height * scale)
            enhanced_image = enhanced_image.resize((new_width, new_height), Image.Resampling.LANCZOS)
        
        # 确定输出格式和文件名
        if ext.lower() in ['.jpg', '.jpeg']:
            output_format = 'JPEG'
            output_ext = '.jpg'
        elif ext.lower() == '.png':
            output_format = 'PNG'
            output_ext = '.png'
        elif ext.lower() == '.gif':
            output_format = 'GIF'
            output_ext = '.gif'
        elif ext.lower() == '.webp':
            output_format = 'WEBP'
            output_ext = '.webp'
        else:
            output_format = 'JPEG'
            output_ext = '.jpg'
        
        # 生成输出文件名（添加处理标识）
        process_info = []
        if use_enhance:
            process_info.append(f"enhanced_{enhance_threshold}")
        if use_pixel_connection:
            process_info.append("connected")
        if scale != 1.0:
            process_info.append(f"scale_{scale}")
        
        if process_info:
            output_filename = f"{name_without_ext}_{'_'.join(process_info)}{output_ext}"
        else:
            output_filename = f"{name_without_ext}_processed{output_ext}"
        
        output_path = os.path.join(output_dir, output_filename)
        
        # 保存处理后的图片
        enhanced_image.save(output_path, format=output_format, quality=95)
        
        return {
            'success': True,
            'input_file': filename,
            'output_file': output_filename,
            'output_path': output_path
        }
        
    except Exception as e:
        return {
            'success': False,
            'input_file': os.path.basename(image_path),
            'error': str(e)
        }

def get_image_files(images_dir):
    """获取images文件夹中的所有图片文件，按文件名字典序排序"""
    image_extensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp']
    image_files = []
    if os.path.exists(images_dir):
        for filename in os.listdir(images_dir):
            if any(filename.lower().endswith(ext) for ext in image_extensions):
                image_files.append(os.path.join(images_dir, filename))
    # 按文件名字典序排序
    return sorted(image_files)

def generate_summary_md(question_type, pinyin_name, image_files, python_results, java_results, result_dir):
    """
    生成summary.md文档，对比三种处理结果
    
    Args:
        question_type: 题型名称
        pinyin_name: 拼音名称
        image_files: 原始图片文件列表
        python_results: Python处理结果列表
        java_results: Java处理结果列表
        result_dir: 结果目录
    """
    summary_file = os.path.join(result_dir, "summary.md")
    
    try:
        with open(summary_file, 'w', encoding='utf-8') as f:
            # 写入标题和基本信息
            f.write(f"# {question_type} - 像素增强处理总结\n\n")
            f.write(f"**处理时间：** {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"**题型：** {question_type}\n")
            f.write(f"**拼音路径：** {pinyin_name}\n")
            f.write(f"**处理参数：**\n")
            f.write(f"  - 像素增强：是（阈值：220）\n")
            f.write(f"  - 像素粘连：是\n")
            f.write(f"  - 缩放倍数：1.0\n\n")
            
            # 写入统计信息
            total_images = len(image_files)
            python_success_count = sum(1 for result in python_results if result['success'])
            python_failed_count = total_images - python_success_count
            java_success_count = sum(1 for result in java_results if result['success'])
            java_failed_count = total_images - java_success_count
            
            f.write(f"## 处理统计\n\n")
            f.write(f"**总图片数：** {total_images}\n\n")
            f.write(f"### Python处理结果\n")
            f.write(f"- **成功处理：** {python_success_count}\n")
            f.write(f"- **处理失败：** {python_failed_count}\n")
            f.write(f"- **成功率：** {python_success_count/total_images*100:.1f}%\n\n")
            f.write(f"### Java处理结果\n")
            f.write(f"- **成功处理：** {java_success_count}\n")
            f.write(f"  - **处理失败：** {java_failed_count}\n")
            f.write(f"- **成功率：** {java_success_count/total_images*100:.1f}%\n\n")
            
            # 写入图片对比信息
            f.write(f"## 图片处理对比\n\n")
            f.write(f"| 序号 | 原始图片 | Python处理结果 | Java处理结果 | 处理状态 |\n")
            f.write(f"|------|----------|----------------|--------------|----------|\n")
            
            for i, (image_path, python_result, java_result) in enumerate(zip(image_files, python_results, java_results), 1):
                original_filename = os.path.basename(image_path)
                
                # 构建原始图片链接
                original_link = f"../images/{original_filename}"
                
                # Python处理结果
                if python_result['success']:
                    python_filename = python_result['output_file']
                    python_link = f"./python_process_images/{python_filename}"
                    python_status = "✅ 成功"
                else:
                    python_filename = "处理失败"
                    python_link = "#"
                    python_status = f"❌ 失败 - {python_result['error']}"
                
                # Java处理结果
                if java_result['success']:
                    java_filename = java_result['output_file']
                    java_link = f"./java_process_images/{java_filename}"
                    java_status = "✅ 成功"
                else:
                    java_filename = "处理失败"
                    java_link = "#"
                    java_status = f"❌ 失败 - {java_result['error']}"
                
                # 写入表格行
                f.write(f"| {i} | ![原图]({original_link}) | ![Python结果]({python_link}) | ![Java结果]({java_link}) | Python: {python_status}<br>Java: {java_status} |\n")
            
            # 写入处理说明
            f.write(f"\n## 处理说明\n\n")
            f.write(f"### 像素增强\n")
            f.write(f"- **阈值设置：** 220\n")
            f.write(f"- **处理效果：** 将图片中的灰色或浅黑色标记增强为纯黑色，背景转换为纯白色\n")
            f.write(f"- **适用场景：** 提高图片对比度，使标记更加清晰\n\n")
            
            f.write(f"### 像素粘连\n")
            f.write(f"- **处理效果：** 连接断开的黑色线条或标记\n")
            f.write(f"- **粘连规则：** 检测相距2像素的黑色像素，将中间的白色像素连接为黑色\n")
            f.write(f"- **适用场景：** 修复扫描图片中的断线问题\n\n")
            
            f.write(f"### 文件命名规则\n")
            f.write(f"处理后的文件名格式：`原文件名_enhanced_220_connected.jpg`\n")
            f.write(f"- `enhanced_220`：增强阈值220\n")
            f.write(f"- `connected`：使用了像素粘连\n\n")
            
            # 写入注意事项
            f.write(f"## 注意事项\n\n")
            f.write(f"1. 原始图片保存在 `../images/` 文件夹中\n")
            f.write(f"2. 处理后的图片保存在当前文件夹中\n")
            f.write(f"3. 支持多种图片格式：JPG、PNG、GIF、WEBP、BMP\n")
            f.write(f"4. 输出格式统一为JPEG，质量设置为95%\n")
        
        print(f"总结文档已生成：{summary_file}")
        return True
        
    except Exception as e:
        print(f"生成总结文档时出错：{e}")
        return False

def compile_java_program():
    """
    自动编译Java程序
    
    Returns:
        bool: 编译是否成功
    """
    try:
        # 检查Java源码是否存在
        java_source = "ImageProcessor.java"
        if not os.path.exists(java_source):
            print(f"错误：Java源码文件 {java_source} 不存在")
            return False
        
        # 检查javac命令是否可用
        try:
            subprocess.run(["javac", "-version"], capture_output=True, check=True, encoding='utf-8', errors='ignore')
        except (subprocess.CalledProcessError, FileNotFoundError):
            print(f"错误：javac命令不可用，请确保已安装JDK")
            return False
        
        print(f"正在编译Java程序...")
        
        # 编译Java源码 - 使用UTF-8编码避免中文编码问题
        result = subprocess.run(["javac", "-encoding", "UTF-8", java_source], 
                               capture_output=True, text=True, encoding='utf-8', errors='ignore', timeout=30)
        if result.returncode != 0:
            print(f"Java编译失败：{result.stderr}")
            return False
        
        print(f"Java编译成功，正在创建JAR文件...")
        
        # 创建JAR文件
        jar_result = subprocess.run(["jar", "cfe", "ImageProcessor.jar", "ImageProcessor", "ImageProcessor.class"], 
                                   capture_output=True, text=True, encoding='utf-8', errors='ignore', timeout=30)
        if jar_result.returncode != 0:
            print(f"JAR文件创建失败：{jar_result.stderr}")
            return False
        
        # 清理class文件
        if os.path.exists("ImageProcessor.class"):
            os.remove("ImageProcessor.class")
        
        print(f"JAR文件创建成功！")
        return True
        
    except subprocess.TimeoutExpired:
        print(f"编译超时")
        return False
    except Exception as e:
        print(f"编译过程中出错：{e}")
        return False

def call_java_processor(image_files, java_result_dir, enhance_threshold):
    """
    调用Java程序处理图片
    
    Args:
        image_files: 图片文件列表
        java_result_dir: Java处理结果目录
        enhance_threshold: 增强阈值
    
    Returns:
        tuple: (成功数量, 失败数量, 结果列表)
    """
    java_success_count = 0
    java_failed_count = 0
    java_results = []
    
    # 检查Java程序是否存在，如果不存在则尝试编译
    java_jar_path = "ImageProcessor.jar"
    if not os.path.exists(java_jar_path):
        print(f"Java程序 {java_jar_path} 不存在，尝试自动编译...")
        if compile_java_program():
            print(f"Java程序编译成功！")
        else:
            print(f"警告：Java程序编译失败，跳过Java处理")
            return java_success_count, java_failed_count, java_results
    
    print(f"找到Java程序：{java_jar_path}")
    
    # 使用进度条显示Java处理进度
    if TQDM_AVAILABLE:
        # 使用tqdm进度条
        for image_path in tqdm(image_files, desc="Java图片处理", unit="张"):
            try:
                filename = os.path.basename(image_path)
                name_without_ext, ext = os.path.splitext(filename)
                
                # 生成Java处理后的文件名（Java会自动转换为PNG格式）
                java_output_filename_jpg = f"{name_without_ext}_java_enhanced_{enhance_threshold}_connected.jpg"
                java_output_filename_png = f"{name_without_ext}_java_enhanced_{enhance_threshold}_connected.png"
                java_output_path_jpg = os.path.join(java_result_dir, java_output_filename_jpg)
                java_output_path_png = os.path.join(java_result_dir, java_output_filename_png)
                
                # 调用Java程序（指定JPG输出，但Java会自动转换为PNG）
                cmd = [
                    "java", "-Dfile.encoding=UTF-8", "-jar", java_jar_path,
                    "--input", image_path,
                    "--output", java_output_path_jpg,
                    "--threshold", str(enhance_threshold),
                    "--connect", "true"
                ]
                
                result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', errors='ignore', timeout=60)
                
                # 检查实际生成的PNG文件是否存在
                if result.returncode == 0 and os.path.exists(java_output_path_png):
                    java_success_count += 1
                    java_results.append({
                        'success': True,
                        'input_file': filename,
                        'output_file': java_output_filename_png,
                        'output_path': java_output_path_png
                    })
                else:
                    java_failed_count += 1
                    error_msg = result.stderr if result.stderr else "未知错误"
                    java_results.append({
                        'success': False,
                        'input_file': filename,
                        'error': error_msg
                    })
                    # 进度条模式下，失败信息在进度条下方显示
                    tqdm.write(f"✗ Java处理失败：{filename} - {error_msg}")
                    
            except subprocess.TimeoutExpired:
                java_failed_count += 1
                java_results.append({
                    'success': False,
                    'input_file': filename,
                    'error': "处理超时"
                })
                tqdm.write(f"✗ Java处理失败：{filename} - 处理超时")
            except Exception as e:
                java_failed_count += 1
                java_results.append({
                    'success': False,
                    'input_file': filename,
                    'error': str(e)
                })
                tqdm.write(f"✗ Java处理失败：{filename} - {str(e)}")
    else:
        # 使用简单进度显示
        for i, image_path in enumerate(image_files, 1):
            try:
                filename = os.path.basename(image_path)
                name_without_ext, ext = os.path.splitext(filename)
                
                # 生成Java处理后的文件名（Java会自动转换为PNG格式）
                java_output_filename_jpg = f"{name_without_ext}_java_enhanced_{enhance_threshold}_connected.jpg"
                java_output_filename_png = f"{name_without_ext}_java_enhanced_{enhance_threshold}_connected.png"
                java_output_path_jpg = os.path.join(java_result_dir, java_output_filename_jpg)
                java_output_path_png = os.path.join(java_result_dir, java_output_filename_png)
                
                # 调用Java程序（指定JPG输出，但Java会自动转换为PNG）
                cmd = [
                    "java", "-Dfile.encoding=UTF-8", "-jar", java_jar_path,
                    "--input", image_path,
                    "--output", java_output_path_jpg,
                    "--threshold", str(enhance_threshold),
                    "--connect", "true"
                ]
                
                result = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', errors='ignore', timeout=60)
                
                # 检查实际生成的PNG文件是否存在
                if result.returncode == 0 and os.path.exists(java_output_path_png):
                    java_success_count += 1
                    java_results.append({
                        'success': True,
                        'input_file': filename,
                        'output_file': java_output_filename_png,
                        'output_path': java_output_path_png
                    })
                else:
                    java_failed_count += 1
                    error_msg = result.stderr if result.stderr else "未知错误"
                    java_results.append({
                        'success': False,
                        'input_file': filename,
                        'error': error_msg
                    })
                    print(f"\n✗ Java处理失败：{filename} - {error_msg}")
                    
            except subprocess.TimeoutExpired:
                java_failed_count += 1
                java_results.append({
                    'success': False,
                    'input_file': filename,
                    'error': "处理超时"
                })
                print(f"\n✗ Java处理失败：{filename} - 处理超时")
            except Exception as e:
                java_failed_count += 1
                java_results.append({
                    'success': False,
                    'input_file': filename,
                    'error': str(e)
                })
                print(f"\n✗ Java处理失败：{filename} - {str(e)}")
    
    return java_success_count, java_failed_count, java_results

def main():
    """主函数"""
    print("=" * 60)
    print("像素增强和像素粘连处理脚本")
    print("=" * 60)
    
    # 1. 选择题型
    question_type, pinyin_name = get_question_type()
    
    # 2. 构建路径
    types_dir = "types"
    question_dir = os.path.join(types_dir, pinyin_name)
    images_dir = os.path.join(question_dir, "images")
    result_dir = os.path.join(question_dir, "pixel_enhancement_result")
    
    # 创建子目录结构
    python_result_dir = os.path.join(result_dir, "python_process_images")
    java_result_dir = os.path.join(result_dir, "java_process_images")
    
    print(f"\n使用路径：")
    print(f"题型目录：{question_dir}")
    print(f"图片文件夹：{images_dir}")
    print(f"结果文件夹：{result_dir}")
    print(f"  - Python处理结果：{python_result_dir}")
    print(f"  - Java处理结果：{java_result_dir}")
    
    # 3. 检查并创建必要的目录
    if not os.path.exists(images_dir):
        print(f"\n错误：图片文件夹 {images_dir} 不存在！")
        print(f"请先创建文件夹 {images_dir} 并放入需要处理的图片。")
        print(f"文件夹结构应该是：types/{pinyin_name}/images/")
        return
    
    os.makedirs(result_dir, exist_ok=True)
    os.makedirs(python_result_dir, exist_ok=True)
    os.makedirs(java_result_dir, exist_ok=True)
    
    # 4. 设置默认处理参数
    print(f"\n--- 处理参数设置 ---")
    
    # 默认采用所有增强功能
    use_enhance = True
    enhance_threshold = 220  # 默认阈值220
    use_pixel_connection = True  # 默认使用像素粘连
    scale = 1.0  # 固定缩放倍数为1.0
    
    print("采用默认处理参数：")
    print(f"  - 像素增强：是（阈值：{enhance_threshold}）")
    print(f"  - 像素粘连：是")
    print(f"  - 缩放倍数：{scale}")
    
    print("\n所有参数已设置为默认值，无需用户输入。")
    
    # 5. 显示处理参数
    print(f"\n--- 处理参数确认 ---")
    print(f"题型：{question_type}")
    print(f"拼音路径：{pinyin_name}")
    print(f"像素增强：{'是' if use_enhance else '否'}")
    if use_enhance:
        print(f"增强阈值：{enhance_threshold}")
        print(f"像素粘连：{'是' if use_pixel_connection else '否'}")
    print(f"缩放倍数：{scale}")
    print(f"输入文件夹：{images_dir}")
    print(f"输出文件夹：{result_dir}")
    
    # 6. 确认开始处理
    while True:
        confirm = input("\n确认开始处理？(y/n)：").strip().lower()
        if confirm in ("y", "n"):
            if confirm == "y":
                break
            else:
                print("处理已取消。")
                return
        else:
            print("请输入 y 或 n")
    
    # 7. 获取图片文件列表
    image_files = get_image_files(images_dir)
    if not image_files:
        print(f"\n错误：在 {images_dir} 文件夹中没有找到图片文件！")
        print("支持的格式：.jpg, .jpeg, .png, .gif, .webp, .bmp")
        return
    
    print(f"\n找到 {len(image_files)} 张图片，开始处理...")
    
    # 8. 准备多进程处理任务
    start_time = time.time()
    
    # 构建Python处理任务列表
    python_tasks = []
    for image_path in image_files:
        task = {
            'image_path': image_path,
            'output_dir': python_result_dir,
            'use_enhance': use_enhance,
            'enhance_threshold': enhance_threshold,
            'scale': scale,
            'use_pixel_connection': use_pixel_connection
        }
        python_tasks.append(task)
    
    # 使用多进程进行Python图片处理
    num_processes = os.cpu_count() if os.cpu_count() else 4
    print(f"正在使用 {num_processes} 个进程进行Python图片处理...")
    
    python_success_count = 0
    python_failed_count = 0
    python_results = []
    
    with multiprocessing.Pool(processes=num_processes) as pool:
        if TQDM_AVAILABLE:
            # 使用tqdm进度条，只显示进度，不显示成功信息
            for result in tqdm(pool.imap(process_single_image, python_tasks), 
                             total=len(python_tasks), desc="Python图片处理", unit="张"):
                if result['success']:
                    python_success_count += 1
                else:
                    # 只有失败时才打印详细信息
                    filename = result['input_file']
                    print(f"\n✗ Python处理失败：{filename} - {result['error']}")
                    python_failed_count += 1
                python_results.append(result)
        else:
            # 使用简单进度显示
            for i, result in enumerate(pool.imap(process_single_image, python_tasks), 1):
                if result['success']:
                    python_success_count += 1
                else:
                    # 只有失败时才打印详细信息
                    filename = result['input_file']
                    print(f"\n✗ Python处理失败：{filename} - {result['error']}")
                    python_failed_count += 1
                python_results.append(result)
    
    # 9. Java图片处理
    print(f"\n开始Java图片处理...")
    java_start_time = time.time()
    
    # 调用Java程序处理图片（现在包含进度条）
    java_success_count, java_failed_count, java_results = call_java_processor(
        image_files, java_result_dir, enhance_threshold
    )
    
    java_processing_time = time.time() - java_start_time
    
    # 10. 显示处理结果
    end_time = time.time()
    python_processing_time = end_time - start_time
    
    print(f"\n" + "=" * 60)
    print("处理完成！")
    print("=" * 60)
    print(f"总图片数：{len(image_files)}")
    print(f"Python处理结果：")
    print(f"  - 成功：{python_success_count}")
    print(f"  - 失败：{python_failed_count}")
    print(f"  - 处理时间：{python_processing_time:.2f}秒")
    print(f"Java处理结果：")
    print(f"  - 成功：{java_success_count}")
    print(f"  - 失败：{java_failed_count}")
    print(f"  - 处理时间：{java_processing_time:.2f}秒")
    print(f"结果保存到：{result_dir}")
    
    # 11. 生成总结文档
    print(f"\n正在生成处理总结文档...")
    generate_summary_md(question_type, pinyin_name, image_files, python_results, java_results, result_dir)
    
    print(f"\n处理完成！所有结果已保存到 {result_dir} 文件夹。")

if __name__ == "__main__":
    main() 
