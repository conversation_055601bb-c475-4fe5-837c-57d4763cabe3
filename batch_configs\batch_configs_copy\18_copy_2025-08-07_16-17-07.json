{"batch_configs": [{"处理模式": 1, "模型ID": 1, "题型": 9, "像素增强": "n", "像素粘连": "n", "图像放大倍数": 1, "response_format": 2, "test_prompt": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回\"NAN\"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{\"题目 1\": \"识别内容 1\", \"题目 2\": \"识别内容 2\", \"题目 3\": \"识别内容 3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。", "test3_prompt": "请判断学生答案与下方正确答案是否意义相同（忽略分数格式、小数点后多余的 0 等非意义性差异），必须按照如下 JSON 格式识别：{\"题目 1\": true, \"题目 2\": false, \"题目 3\": true}，返回的批改结果数量必须与正确答案数量一致，当学生回答与正确答案意义相同时，该题目为 true，否则为 false，识别的 JSON 题号必须始终从 \"题目 1\" 开始，依次递增。\n\n图片为学生回答的图片，首先先按照学生回答的JSON和正确答案的JSON进行比较。比较完成后，将图片和最终的比较结果进行比对，看看是否需要修改回答的JSON。\n批改时要严格按照以下的批改原则：\n\n1.比对正确答案和学生答案的数字是否等价，而不需要严格要求两个答案完全一致，例如：图片上的学生答案为\"1 1/2\"，正确答案为\"1.5\"，则返回\"true\"。\n\n2.请严格按照给出的正确答案的JSON和图片上的学生答案进行比较，严禁根据图片上的题目信息联想答案。", "one_test_prompt": "one_test_prompt.md", "图像文件夹": 1, "round2批改模式": 2, "准确率": "准确率：56.78%  （(236 - 102) / 236）"}]}