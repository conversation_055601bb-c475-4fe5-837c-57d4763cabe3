# Example Code generated by Beijing Volcanoengine Technology.
from __future__ import print_function
import volcenginesdkcore
import volcenginesdkautoscaling
from pprint import pprint
from volcenginesdkcore.rest import ApiException

if __name__ == '__main__':
    configuration = volcenginesdkcore.Configuration()
    configuration.ak = "Your AK"
    configuration.sk = "Your SK"
    configuration.region = "cn-beijing"
    # set default configuration
    volcenginesdkcore.Configuration.set_default(configuration)

    # use global default configuration
    api_instance = volcenginesdkautoscaling.AUTOSCALINGApi()
    req_scheduled_policy = volcenginesdkautoscaling.ScheduledPolicyForCreateScalingPolicyInput(
        launch_time="2022-04-01T16:00Z",
    )
    create_scaling_policy_request = volcenginesdkautoscaling.CreateScalingPolicyRequest(
        adjustment_type="QuantityChangeInCapacity",
        adjustment_value=12,
        scaling_group_id="scg-ybmssdnnhn5pkgyd****",
        scaling_policy_name="test-policy",
        scaling_policy_type="Scheduled",
        scheduled_policy=req_scheduled_policy,
    )
    
    try:
        resp = api_instance.create_scaling_policy(create_scaling_policy_request)
        pprint(resp)
    except ApiException as e:
        print("Exception when calling api: %s\n" % e)
