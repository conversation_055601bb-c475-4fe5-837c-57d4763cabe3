# Example Code generated by Beijing Volcanoengine Technology.
from __future__ import print_function
import volcenginesdkcore
import volcenginesdkvpn
from pprint import pprint
from volcenginesdkcore.rest import ApiException

if __name__ == '__main__':
    configuration = volcenginesdkcore.Configuration()
    configuration.ak = "AK"
    configuration.sk = "SK"
    configuration.region = "cn-beijing"
    # set default configuration
    volcenginesdkcore.Configuration.set_default(configuration)

    # use global default configuration
    api_instance = volcenginesdkvpn.VPNApi()
    modify_vpn_gateway_attributes_request = volcenginesdkvpn.ModifyVpnGatewayAttributesRequest(
        bandwidth=10,
        description="test",
        vpn_gateway_id="vgw-12bfa2du7fojk17q7y1rk****",
        vpn_gateway_name="test",
    )

    try:
        resp = api_instance.modify_vpn_gateway_attributes(modify_vpn_gateway_attributes_request)
        pprint(resp)
    except ApiException as e:
        print("Exception when calling api: %s\n" % e)
