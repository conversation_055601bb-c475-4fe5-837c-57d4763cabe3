# Example Code generated by Beijing Volcanoengine Technology.
from __future__ import print_function
import volcenginesdkcore
import volcenginesdkvpc
from pprint import pprint
from volcenginesdkcore.rest import ApiException

if __name__ == '__main__':
    configuration = volcenginesdkcore.Configuration()
    configuration.ak = "Your AK"
    configuration.sk = "Your SK"
    configuration.region = "cn-beijing"
    # set default configuration
    volcenginesdkcore.Configuration.set_default(configuration)

    # use global default configuration
    api_instance = volcenginesdkvpc.VPCApi()
    unassign_private_ip_addresses_request = volcenginesdkvpc.UnassignPrivateIpAddressesRequest(
        network_interface_id="eni-bp67acfmxazb4ph****",
        private_ip_address=["192.168.XX.10"],
    )
    
    try:
        resp = api_instance.unassign_private_ip_addresses(unassign_private_ip_addresses_request)
        pprint(resp)
    except ApiException as e:
        print("Exception when calling api: %s\n" % e)
