# Example Code generated by Beijing Volcanoengine Technology.
from __future__ import print_function
import volcenginesdkcore
import volcenginesdkecs
from pprint import pprint
from volcenginesdkcore.rest import ApiException

if __name__ == '__main__':
    configuration = volcenginesdkcore.Configuration()
    configuration.ak = "Your AK"
    configuration.sk = "Your SK"
    configuration.region = "cn-beijing"
    # set default configuration
    volcenginesdkcore.Configuration.set_default(configuration)

    # use global default configuration
    api_instance = volcenginesdkecs.ECSApi()
    replace_system_volume_request = volcenginesdkecs.ReplaceSystemVolumeRequest(
        image_id="image-38dfk6rfisf6kir6****",
        instance_id="i-a8j6n1i4hojfqpa0****",
        password="Password@123",
    )
    
    try:
        resp = api_instance.replace_system_volume(replace_system_volume_request)
        pprint(resp)
    except ApiException as e:
        print("Exception when calling api: %s\n" % e)
