# Example Code generated by Beijing Volcanoengine Technology.
from __future__ import print_function
import volcenginesdkcore
import volcenginesdkautoscaling
from pprint import pprint
from volcenginesdkcore.rest import ApiException

if __name__ == '__main__':
    configuration = volcenginesdkcore.Configuration()
    configuration.ak = "Your AK"
    configuration.sk = "Your SK"
    configuration.region = "cn-beijing"
    # set default configuration
    volcenginesdkcore.Configuration.set_default(configuration)

    # use global default configuration
    api_instance = volcenginesdkautoscaling.AUTOSCALINGApi()
    modify_scaling_policy_request = volcenginesdkautoscaling.ModifyScalingPolicyRequest(
        scaling_policy_id="sp-ybmvamf8uql8j1fl****",
        scaling_policy_name="test1-policy",
    )
    
    try:
        resp = api_instance.modify_scaling_policy(modify_scaling_policy_request)
        pprint(resp)
    except ApiException as e:
        print("Exception when calling api: %s\n" % e)
