# coding: utf-8

# flake8: noqa

"""
    aiotvideo

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


from __future__ import absolute_import

# import apis into sdk package
from volcenginesdkaiotvideo.api.aiotvideo_api import AIOTVIDEOApi

# import models into sdk package
from volcenginesdkaiotvideo.models.ai_for_get_space_output import AIForGetSpaceOutput
from volcenginesdkaiotvideo.models.ai_for_get_stream_output import AIForGetStreamOutput
from volcenginesdkaiotvideo.models.ai_for_list_spaces_output import AIForListSpacesOutput
from volcenginesdkaiotvideo.models.ai_for_list_streams_output import AIForListStreamsOutput
from volcenginesdkaiotvideo.models.add_list_for_update_record_plan_input import AddListForUpdateRecordPlanInput
from volcenginesdkaiotvideo.models.alert_notification_for_create_device_input import AlertNotificationForCreateDeviceInput
from volcenginesdkaiotvideo.models.alert_notification_for_get_device_output import AlertNotificationForGetDeviceOutput
from volcenginesdkaiotvideo.models.alert_notification_for_list_devices_output import AlertNotificationForListDevicesOutput
from volcenginesdkaiotvideo.models.alert_notification_for_update_device_input import AlertNotificationForUpdateDeviceInput
from volcenginesdkaiotvideo.models.b_audio_for_get_stream_data_output import BAudioForGetStreamDataOutput
from volcenginesdkaiotvideo.models.b_video_for_get_stream_data_output import BVideoForGetStreamDataOutput
from volcenginesdkaiotvideo.models.bind_channels_for_create_record_plan_input import BindChannelsForCreateRecordPlanInput
from volcenginesdkaiotvideo.models.cancel_stream_template_request import CancelStreamTemplateRequest
from volcenginesdkaiotvideo.models.cancel_stream_template_response import CancelStreamTemplateResponse
from volcenginesdkaiotvideo.models.channel_for_get_device_channels_v2_output import ChannelForGetDeviceChannelsV2Output
from volcenginesdkaiotvideo.models.cnt_for_get_push_stream_cnt_output import CntForGetPushStreamCntOutput
from volcenginesdkaiotvideo.models.coordinates_for_create_device_input import CoordinatesForCreateDeviceInput
from volcenginesdkaiotvideo.models.coordinates_for_create_device_output import CoordinatesForCreateDeviceOutput
from volcenginesdkaiotvideo.models.coordinates_for_get_device_output import CoordinatesForGetDeviceOutput
from volcenginesdkaiotvideo.models.coordinates_for_list_devices_output import CoordinatesForListDevicesOutput
from volcenginesdkaiotvideo.models.create_device_request import CreateDeviceRequest
from volcenginesdkaiotvideo.models.create_device_response import CreateDeviceResponse
from volcenginesdkaiotvideo.models.create_record_plan_request import CreateRecordPlanRequest
from volcenginesdkaiotvideo.models.create_record_plan_response import CreateRecordPlanResponse
from volcenginesdkaiotvideo.models.create_stream_request import CreateStreamRequest
from volcenginesdkaiotvideo.models.create_stream_response import CreateStreamResponse
from volcenginesdkaiotvideo.models.data_down_for_get_data_project_with_bind_width_and_flow_output import DataDownForGetDataProjectWithBindWidthAndFlowOutput
from volcenginesdkaiotvideo.models.data_up_for_get_data_project_with_bind_width_and_flow_output import DataUpForGetDataProjectWithBindWidthAndFlowOutput
from volcenginesdkaiotvideo.models.del_list_for_update_record_plan_input import DelListForUpdateRecordPlanInput
from volcenginesdkaiotvideo.models.delete_device_request import DeleteDeviceRequest
from volcenginesdkaiotvideo.models.delete_device_response import DeleteDeviceResponse
from volcenginesdkaiotvideo.models.delete_record_plan_request import DeleteRecordPlanRequest
from volcenginesdkaiotvideo.models.delete_record_plan_response import DeleteRecordPlanResponse
from volcenginesdkaiotvideo.models.delete_space_request import DeleteSpaceRequest
from volcenginesdkaiotvideo.models.delete_space_response import DeleteSpaceResponse
from volcenginesdkaiotvideo.models.delete_stream_record_request import DeleteStreamRecordRequest
from volcenginesdkaiotvideo.models.delete_stream_record_response import DeleteStreamRecordResponse
from volcenginesdkaiotvideo.models.delete_stream_request import DeleteStreamRequest
from volcenginesdkaiotvideo.models.delete_stream_response import DeleteStreamResponse
from volcenginesdkaiotvideo.models.device_for_list_devices_output import DeviceForListDevicesOutput
from volcenginesdkaiotvideo.models.device_item_for_get_device_channels_v2_output import DeviceItemForGetDeviceChannelsV2Output
from volcenginesdkaiotvideo.models.device_streams_for_get_device_output import DeviceStreamsForGetDeviceOutput
from volcenginesdkaiotvideo.models.device_streams_for_list_devices_output import DeviceStreamsForListDevicesOutput
from volcenginesdkaiotvideo.models.device_sub_stream_for_get_device_output import DeviceSubStreamForGetDeviceOutput
from volcenginesdkaiotvideo.models.device_sub_stream_for_list_devices_output import DeviceSubStreamForListDevicesOutput
from volcenginesdkaiotvideo.models.domains_for_get_space_output import DomainsForGetSpaceOutput
from volcenginesdkaiotvideo.models.domains_for_list_spaces_output import DomainsForListSpacesOutput
from volcenginesdkaiotvideo.models.extra_for_get_stream_record_output import ExtraForGetStreamRecordOutput
from volcenginesdkaiotvideo.models.fp_for_get_stream_data_output import FPForGetStreamDataOutput
from volcenginesdkaiotvideo.models.frame_for_get_stream_data_output import FrameForGetStreamDataOutput
from volcenginesdkaiotvideo.models.fresh_device_request import FreshDeviceRequest
from volcenginesdkaiotvideo.models.fresh_device_response import FreshDeviceResponse
from volcenginesdkaiotvideo.models.gb_for_get_space_output import GBForGetSpaceOutput
from volcenginesdkaiotvideo.models.gb_for_list_spaces_output import GBForListSpacesOutput
from volcenginesdkaiotvideo.models.gb_for_update_space_input import GBForUpdateSpaceInput
from volcenginesdkaiotvideo.models.gb_media_for_get_local_download_output import GBMediaForGetLocalDownloadOutput
from volcenginesdkaiotvideo.models.gen_sip_id_request import GenSipIDRequest
from volcenginesdkaiotvideo.models.gen_sip_id_response import GenSipIDResponse
from volcenginesdkaiotvideo.models.get_data_project_with_bind_width_and_flow_request import GetDataProjectWithBindWidthAndFlowRequest
from volcenginesdkaiotvideo.models.get_data_project_with_bind_width_and_flow_response import GetDataProjectWithBindWidthAndFlowResponse
from volcenginesdkaiotvideo.models.get_device_channels_v2_request import GetDeviceChannelsV2Request
from volcenginesdkaiotvideo.models.get_device_channels_v2_response import GetDeviceChannelsV2Response
from volcenginesdkaiotvideo.models.get_device_request import GetDeviceRequest
from volcenginesdkaiotvideo.models.get_device_response import GetDeviceResponse
from volcenginesdkaiotvideo.models.get_local_download_request import GetLocalDownloadRequest
from volcenginesdkaiotvideo.models.get_local_download_response import GetLocalDownloadResponse
from volcenginesdkaiotvideo.models.get_push_stream_cnt_request import GetPushStreamCntRequest
from volcenginesdkaiotvideo.models.get_push_stream_cnt_response import GetPushStreamCntResponse
from volcenginesdkaiotvideo.models.get_record_plan_request import GetRecordPlanRequest
from volcenginesdkaiotvideo.models.get_record_plan_response import GetRecordPlanResponse
from volcenginesdkaiotvideo.models.get_space_request import GetSpaceRequest
from volcenginesdkaiotvideo.models.get_space_response import GetSpaceResponse
from volcenginesdkaiotvideo.models.get_stream_data_request import GetStreamDataRequest
from volcenginesdkaiotvideo.models.get_stream_data_response import GetStreamDataResponse
from volcenginesdkaiotvideo.models.get_stream_record_request import GetStreamRecordRequest
from volcenginesdkaiotvideo.models.get_stream_record_response import GetStreamRecordResponse
from volcenginesdkaiotvideo.models.get_stream_request import GetStreamRequest
from volcenginesdkaiotvideo.models.get_stream_response import GetStreamResponse
from volcenginesdkaiotvideo.models.get_total_data_request import GetTotalDataRequest
from volcenginesdkaiotvideo.models.get_total_data_response import GetTotalDataResponse
from volcenginesdkaiotvideo.models.list_devices_request import ListDevicesRequest
from volcenginesdkaiotvideo.models.list_devices_response import ListDevicesResponse
from volcenginesdkaiotvideo.models.list_for_list_record_plan_channels_output import ListForListRecordPlanChannelsOutput
from volcenginesdkaiotvideo.models.list_for_list_record_plans_output import ListForListRecordPlansOutput
from volcenginesdkaiotvideo.models.list_record_plan_channels_request import ListRecordPlanChannelsRequest
from volcenginesdkaiotvideo.models.list_record_plan_channels_response import ListRecordPlanChannelsResponse
from volcenginesdkaiotvideo.models.list_record_plans_request import ListRecordPlansRequest
from volcenginesdkaiotvideo.models.list_record_plans_response import ListRecordPlansResponse
from volcenginesdkaiotvideo.models.list_spaces_request import ListSpacesRequest
from volcenginesdkaiotvideo.models.list_spaces_response import ListSpacesResponse
from volcenginesdkaiotvideo.models.list_streams_request import ListStreamsRequest
from volcenginesdkaiotvideo.models.list_streams_response import ListStreamsResponse
from volcenginesdkaiotvideo.models.record_for_get_space_output import RecordForGetSpaceOutput
from volcenginesdkaiotvideo.models.record_for_get_stream_output import RecordForGetStreamOutput
from volcenginesdkaiotvideo.models.record_for_list_spaces_output import RecordForListSpacesOutput
from volcenginesdkaiotvideo.models.record_for_list_streams_output import RecordForListStreamsOutput
from volcenginesdkaiotvideo.models.res_for_get_stream_record_output import ResForGetStreamRecordOutput
from volcenginesdkaiotvideo.models.screenshot_for_get_space_output import ScreenshotForGetSpaceOutput
from volcenginesdkaiotvideo.models.screenshot_for_get_stream_output import ScreenshotForGetStreamOutput
from volcenginesdkaiotvideo.models.screenshot_for_list_spaces_output import ScreenshotForListSpacesOutput
from volcenginesdkaiotvideo.models.screenshot_for_list_streams_output import ScreenshotForListStreamsOutput
from volcenginesdkaiotvideo.models.session_data_for_stat_stream_output import SessionDataForStatStreamOutput
from volcenginesdkaiotvideo.models.set_stream_template_request import SetStreamTemplateRequest
from volcenginesdkaiotvideo.models.set_stream_template_response import SetStreamTemplateResponse
from volcenginesdkaiotvideo.models.sip_port_for_get_space_output import SipPortForGetSpaceOutput
from volcenginesdkaiotvideo.models.sip_port_for_list_spaces_output import SipPortForListSpacesOutput
from volcenginesdkaiotvideo.models.sip_server_for_get_space_output import SipServerForGetSpaceOutput
from volcenginesdkaiotvideo.models.sip_server_for_list_spaces_output import SipServerForListSpacesOutput
from volcenginesdkaiotvideo.models.space_for_list_spaces_output import SpaceForListSpacesOutput
from volcenginesdkaiotvideo.models.start_stream_request import StartStreamRequest
from volcenginesdkaiotvideo.models.start_stream_response import StartStreamResponse
from volcenginesdkaiotvideo.models.start_voice_talk_request import StartVoiceTalkRequest
from volcenginesdkaiotvideo.models.start_voice_talk_response import StartVoiceTalkResponse
from volcenginesdkaiotvideo.models.stat_stream_request import StatStreamRequest
from volcenginesdkaiotvideo.models.stat_stream_response import StatStreamResponse
from volcenginesdkaiotvideo.models.stop_stream_request import StopStreamRequest
from volcenginesdkaiotvideo.models.stop_stream_response import StopStreamResponse
from volcenginesdkaiotvideo.models.stop_voice_talk_request import StopVoiceTalkRequest
from volcenginesdkaiotvideo.models.stop_voice_talk_response import StopVoiceTalkResponse
from volcenginesdkaiotvideo.models.stream_for_list_streams_output import StreamForListStreamsOutput
from volcenginesdkaiotvideo.models.stream_start_record_request import StreamStartRecordRequest
from volcenginesdkaiotvideo.models.stream_start_record_response import StreamStartRecordResponse
from volcenginesdkaiotvideo.models.stream_stop_record_request import StreamStopRecordRequest
from volcenginesdkaiotvideo.models.stream_stop_record_response import StreamStopRecordResponse
from volcenginesdkaiotvideo.models.ttl_for_get_record_plan_output import TTLForGetRecordPlanOutput
from volcenginesdkaiotvideo.models.ttl_for_list_record_plans_output import TTLForListRecordPlansOutput
from volcenginesdkaiotvideo.models.tag_filter_for_list_spaces_input import TagFilterForListSpacesInput
from volcenginesdkaiotvideo.models.tag_for_list_spaces_output import TagForListSpacesOutput
from volcenginesdkaiotvideo.models.template_for_get_space_output import TemplateForGetSpaceOutput
from volcenginesdkaiotvideo.models.template_for_list_spaces_output import TemplateForListSpacesOutput
from volcenginesdkaiotvideo.models.template_info_for_get_record_plan_output import TemplateInfoForGetRecordPlanOutput
from volcenginesdkaiotvideo.models.template_info_for_list_record_plans_output import TemplateInfoForListRecordPlansOutput
from volcenginesdkaiotvideo.models.trans_config_for_get_record_plan_output import TransConfigForGetRecordPlanOutput
from volcenginesdkaiotvideo.models.trans_config_for_list_record_plans_output import TransConfigForListRecordPlansOutput
from volcenginesdkaiotvideo.models.trans_pull_urls_for_get_stream_output import TransPullUrlsForGetStreamOutput
from volcenginesdkaiotvideo.models.trans_pull_urls_for_list_streams_output import TransPullUrlsForListStreamsOutput
from volcenginesdkaiotvideo.models.update_device_request import UpdateDeviceRequest
from volcenginesdkaiotvideo.models.update_device_response import UpdateDeviceResponse
from volcenginesdkaiotvideo.models.update_record_plan_request import UpdateRecordPlanRequest
from volcenginesdkaiotvideo.models.update_record_plan_response import UpdateRecordPlanResponse
from volcenginesdkaiotvideo.models.update_space_request import UpdateSpaceRequest
from volcenginesdkaiotvideo.models.update_space_response import UpdateSpaceResponse
from volcenginesdkaiotvideo.models.update_stream_request import UpdateStreamRequest
from volcenginesdkaiotvideo.models.update_stream_response import UpdateStreamResponse
