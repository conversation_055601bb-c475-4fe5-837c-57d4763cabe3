# Example Code generated by Beijing Volcanoengine Technology.
from __future__ import print_function
import volcenginesdkcore
import volcenginesdkecs
from pprint import pprint
from volcenginesdkcore.rest import ApiException

if __name__ == '__main__':
    configuration = volcenginesdkcore.Configuration()
    configuration.ak = "Your AK"
    configuration.sk = "Your SK"
    configuration.region = "cn-beijing"
    # set default configuration
    volcenginesdkcore.Configuration.set_default(configuration)

    # use global default configuration
    api_instance = volcenginesdkecs.ECSApi()
    modify_instance_charge_type_request = volcenginesdkecs.ModifyInstanceChargeTypeRequest(
        auto_pay=True,
        include_data_volumes=False,
        instance_charge_type="PrePaid",
        instance_ids=["i-4ay51iinvo8w4nho****"],
        period=2,
        period_unit="Month",
    )
    
    try:
        resp = api_instance.modify_instance_charge_type(modify_instance_charge_type_request)
        pprint(resp)
    except ApiException as e:
        print("Exception when calling api: %s\n" % e)
