# Example Code generated by Beijing Volcanoengine Technology.
from __future__ import print_function
import volcenginesdkcore
import volcenginesdkcen
from pprint import pprint
from volcenginesdkcore.rest import ApiException

if __name__ == '__main__':
    configuration = volcenginesdkcore.Configuration()
    configuration.ak = "Your AK"
    configuration.sk = "Your SK"
    configuration.region = "cn-beijing"
    # set default configuration
    volcenginesdkcore.Configuration.set_default(configuration)

    # use global default configuration
    api_instance = volcenginesdkcen.CENApi()
    attach_instance_to_cen_request = volcenginesdkcen.AttachInstanceToCenRequest(
        cen_id="cen-7qthudw0ll6jmc****",
        instance_id="vpc-2fexiqjlgjif45oxruvso****",
        instance_region_id="cn-beijing",
        instance_type="vpc",
    )
    
    try:
        resp = api_instance.attach_instance_to_cen(attach_instance_to_cen_request)
        pprint(resp)
    except ApiException as e:
        print("Exception when calling api: %s\n" % e)
