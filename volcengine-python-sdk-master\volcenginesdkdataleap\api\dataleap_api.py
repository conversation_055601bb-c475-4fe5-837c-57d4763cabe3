# coding: utf-8

"""
    dataleap

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


from __future__ import absolute_import

import re  # noqa: F401

# python 2 and python 3 compatibility library
import six

import volcenginesdkcore


class DATALEAPApi(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    Ref: https://github.com/swagger-api/swagger-codegen
    """

    def __init__(self, api_client=None):
        if api_client is None:
            api_client = volcenginesdkcore.ApiClient()
        self.api_client = api_client

    def d_ts_open_describe_resource_groups(self, body, **kwargs):  # noqa: E501
        """d_ts_open_describe_resource_groups  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.d_ts_open_describe_resource_groups(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DTSOpenDescribeResourceGroupsRequest body: (required)
        :return: DTSOpenDescribeResourceGroupsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.d_ts_open_describe_resource_groups_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.d_ts_open_describe_resource_groups_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def d_ts_open_describe_resource_groups_with_http_info(self, body, **kwargs):  # noqa: E501
        """d_ts_open_describe_resource_groups  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.d_ts_open_describe_resource_groups_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DTSOpenDescribeResourceGroupsRequest body: (required)
        :return: DTSOpenDescribeResourceGroupsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method d_ts_open_describe_resource_groups" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `d_ts_open_describe_resource_groups`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/DTSOpenDescribeResourceGroups/2024-02-01/dataleap/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='DTSOpenDescribeResourceGroupsResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def d_ts_open_list_tag_resource_groups(self, body, **kwargs):  # noqa: E501
        """d_ts_open_list_tag_resource_groups  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.d_ts_open_list_tag_resource_groups(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DTSOpenListTagResourceGroupsRequest body: (required)
        :return: DTSOpenListTagResourceGroupsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.d_ts_open_list_tag_resource_groups_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.d_ts_open_list_tag_resource_groups_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def d_ts_open_list_tag_resource_groups_with_http_info(self, body, **kwargs):  # noqa: E501
        """d_ts_open_list_tag_resource_groups  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.d_ts_open_list_tag_resource_groups_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DTSOpenListTagResourceGroupsRequest body: (required)
        :return: DTSOpenListTagResourceGroupsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method d_ts_open_list_tag_resource_groups" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `d_ts_open_list_tag_resource_groups`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/DTSOpenListTagResourceGroups/2024-02-01/dataleap/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='DTSOpenListTagResourceGroupsResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def d_ts_open_tag_resource_groups(self, body, **kwargs):  # noqa: E501
        """d_ts_open_tag_resource_groups  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.d_ts_open_tag_resource_groups(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DTSOpenTagResourceGroupsRequest body: (required)
        :return: DTSOpenTagResourceGroupsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.d_ts_open_tag_resource_groups_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.d_ts_open_tag_resource_groups_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def d_ts_open_tag_resource_groups_with_http_info(self, body, **kwargs):  # noqa: E501
        """d_ts_open_tag_resource_groups  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.d_ts_open_tag_resource_groups_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DTSOpenTagResourceGroupsRequest body: (required)
        :return: DTSOpenTagResourceGroupsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method d_ts_open_tag_resource_groups" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `d_ts_open_tag_resource_groups`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/DTSOpenTagResourceGroups/2024-02-01/dataleap/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='DTSOpenTagResourceGroupsResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def d_ts_open_untag_resource_groups(self, body, **kwargs):  # noqa: E501
        """d_ts_open_untag_resource_groups  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.d_ts_open_untag_resource_groups(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DTSOpenUntagResourceGroupsRequest body: (required)
        :return: DTSOpenUntagResourceGroupsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.d_ts_open_untag_resource_groups_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.d_ts_open_untag_resource_groups_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def d_ts_open_untag_resource_groups_with_http_info(self, body, **kwargs):  # noqa: E501
        """d_ts_open_untag_resource_groups  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.d_ts_open_untag_resource_groups_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DTSOpenUntagResourceGroupsRequest body: (required)
        :return: DTSOpenUntagResourceGroupsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method d_ts_open_untag_resource_groups" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `d_ts_open_untag_resource_groups`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/DTSOpenUntagResourceGroups/2024-02-01/dataleap/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='DTSOpenUntagResourceGroupsResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)
