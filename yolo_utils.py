import os
import numpy as np
import cv2

def parse_yolo_annotation(annotation_path, image_width, image_height):
    boxes = []
    if not os.path.exists(annotation_path):
        return boxes
    try:
        with open(annotation_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        for line in lines:
            line = line.strip()
            if not line:
                continue
            parts = line.split()
            if len(parts) == 9:
                class_id = int(parts[0])
                points = [float(x) for x in parts[1:]]
                xs = points[0::2]
                ys = points[1::2]
                xs_pixel = [int(x * image_width) for x in xs]
                ys_pixel = [int(y * image_height) for y in ys]
                x_min = max(0, min(xs_pixel))
                y_min = max(0, min(ys_pixel))
                x_max = min(image_width - 1, max(xs_pixel))
                y_max = min(image_height - 1, max(ys_pixel))
                boxes.append((x_min, y_min, x_max, y_max, class_id))
            elif len(parts) == 5:
                class_id = int(parts[0])
                center_x = float(parts[1])
                center_y = float(parts[2])
                width = float(parts[3])
                height = float(parts[4])
                center_x_pixel = center_x * image_width
                center_y_pixel = center_y * image_height
                width_pixel = width * image_width
                height_pixel = height * image_height
                x_min = int(center_x_pixel - width_pixel / 2)
                y_min = int(center_y_pixel - height_pixel / 2)
                x_max = int(center_x_pixel + width_pixel / 2)
                y_max = int(center_y_pixel + height_pixel / 2)
                x_min = max(0, min(x_min, image_width - 1))
                y_min = max(0, min(y_min, image_height - 1))
                x_max = max(x_min, min(x_max, image_width - 1))
                y_max = max(y_min, min(y_max, image_height - 1))
                boxes.append((x_min, y_min, x_max, y_max, class_id))
            else:
                continue
    except Exception as e:
        print(f"读取标注文件时出错: {annotation_path}, 错误: {e}")
    return boxes

def create_bbox_mask(image_shape, boxes):
    h, w = image_shape[:2]
    mask = np.zeros((h, w), dtype=bool)
    for x_min, y_min, x_max, y_max, _ in boxes:
        x_min = max(0, min(x_min, w - 1))
        y_min = max(0, min(y_min, h - 1))
        x_max = max(x_min, min(x_max, w - 1))
        y_max = max(y_min, min(y_max, h - 1))
        if x_max > x_min and y_max > y_min:
            mask[y_min:y_max + 1, x_min:x_max + 1] = True
    return mask

def apply_white_noise_processing(image, bbox_mask):
    processed_image = image.copy()
    outside_mask = ~bbox_mask
    outside_positions = np.where(outside_mask)
    total_outside_pixels = len(outside_positions[0])
    if total_outside_pixels > 0:
        noise_density = 0.8
        num_noise_pixels = int(total_outside_pixels * noise_density)
        indices = np.random.choice(range(total_outside_pixels), num_noise_pixels, replace=False)
        noise_y = outside_positions[0][indices]
        noise_x = outside_positions[1][indices]
        processed_image[noise_y, noise_x] = [255, 255, 255]
    return processed_image

def apply_gaussian_blur_processing(image, bbox_mask):
    processed_image = image.copy()
    blur_radius = 7
    kernel_size = blur_radius * 2 + 1
    blurred_image = cv2.GaussianBlur(image, (kernel_size, kernel_size), 0)
    outside_mask = ~bbox_mask
    processed_image[outside_mask] = blurred_image[outside_mask]
    return processed_image

def apply_transparent_mask_processing(image, bbox_mask):
    processed_image = image.copy().astype(np.float32)
    alpha = 0.5
    mask_color = 0
    outside_mask = ~bbox_mask
    processed_image[outside_mask] = processed_image[outside_mask] * (1 - alpha) + mask_color * alpha
    return processed_image.astype(np.uint8)

def process_image_with_roboflow_boxes(image_path, boxes, output_path, processing_type="4"):
    if processing_type == "5":
        return True
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError(f"无法读取图片: {image_path}")
    bbox_mask = create_bbox_mask(image.shape, boxes)
    if processing_type == "1":
        processed_image = apply_white_noise_processing(image, bbox_mask)
    elif processing_type == "2":
        processed_image = apply_gaussian_blur_processing(image, bbox_mask)
    elif processing_type == "3":
        processed_image = apply_transparent_mask_processing(image, bbox_mask)
    else:
        processed_image = image.copy()
    black_color = (0, 0, 0)
    for i, (x_min, y_min, x_max, y_max, class_id) in enumerate(boxes):
        cv2.rectangle(processed_image, (x_min, y_min), (x_max, y_max), black_color, 2)
    cv2.imwrite(output_path, processed_image, [cv2.IMWRITE_PNG_COMPRESSION, 9])
    return True 