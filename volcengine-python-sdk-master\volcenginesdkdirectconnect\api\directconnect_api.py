# coding: utf-8

"""
    directconnect

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


from __future__ import absolute_import

import re  # noqa: F401

# python 2 and python 3 compatibility library
import six

import volcenginesdkcore


class DIRECTCONNECTApi(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    Ref: https://github.com/swagger-api/swagger-codegen
    """

    def __init__(self, api_client=None):
        if api_client is None:
            api_client = volcenginesdkcore.ApiClient()
        self.api_client = api_client

    def apply_direct_connect_connection_loa(self, body, **kwargs):  # noqa: E501
        """apply_direct_connect_connection_loa  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.apply_direct_connect_connection_loa(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param ApplyDirectConnectConnectionLoaRequest body: (required)
        :return: ApplyDirectConnectConnectionLoaResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.apply_direct_connect_connection_loa_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.apply_direct_connect_connection_loa_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def apply_direct_connect_connection_loa_with_http_info(self, body, **kwargs):  # noqa: E501
        """apply_direct_connect_connection_loa  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.apply_direct_connect_connection_loa_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param ApplyDirectConnectConnectionLoaRequest body: (required)
        :return: ApplyDirectConnectConnectionLoaResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method apply_direct_connect_connection_loa" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `apply_direct_connect_connection_loa`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/ApplyDirectConnectConnectionLoa/2020-04-01/directconnect/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='ApplyDirectConnectConnectionLoaResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def create_bgp_peer(self, body, **kwargs):  # noqa: E501
        """create_bgp_peer  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_bgp_peer(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param CreateBgpPeerRequest body: (required)
        :return: CreateBgpPeerResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.create_bgp_peer_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.create_bgp_peer_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def create_bgp_peer_with_http_info(self, body, **kwargs):  # noqa: E501
        """create_bgp_peer  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_bgp_peer_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param CreateBgpPeerRequest body: (required)
        :return: CreateBgpPeerResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method create_bgp_peer" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `create_bgp_peer`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/CreateBgpPeer/2020-04-01/directconnect/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='CreateBgpPeerResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def create_direct_connect_connection(self, body, **kwargs):  # noqa: E501
        """create_direct_connect_connection  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_direct_connect_connection(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param CreateDirectConnectConnectionRequest body: (required)
        :return: CreateDirectConnectConnectionResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.create_direct_connect_connection_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.create_direct_connect_connection_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def create_direct_connect_connection_with_http_info(self, body, **kwargs):  # noqa: E501
        """create_direct_connect_connection  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_direct_connect_connection_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param CreateDirectConnectConnectionRequest body: (required)
        :return: CreateDirectConnectConnectionResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method create_direct_connect_connection" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `create_direct_connect_connection`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/CreateDirectConnectConnection/2020-04-01/directconnect/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='CreateDirectConnectConnectionResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def create_direct_connect_connection_order(self, body, **kwargs):  # noqa: E501
        """create_direct_connect_connection_order  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_direct_connect_connection_order(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param CreateDirectConnectConnectionOrderRequest body: (required)
        :return: CreateDirectConnectConnectionOrderResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.create_direct_connect_connection_order_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.create_direct_connect_connection_order_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def create_direct_connect_connection_order_with_http_info(self, body, **kwargs):  # noqa: E501
        """create_direct_connect_connection_order  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_direct_connect_connection_order_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param CreateDirectConnectConnectionOrderRequest body: (required)
        :return: CreateDirectConnectConnectionOrderResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method create_direct_connect_connection_order" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `create_direct_connect_connection_order`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/CreateDirectConnectConnectionOrder/2020-04-01/directconnect/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='CreateDirectConnectConnectionOrderResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def create_direct_connect_gateway(self, body, **kwargs):  # noqa: E501
        """create_direct_connect_gateway  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_direct_connect_gateway(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param CreateDirectConnectGatewayRequest body: (required)
        :return: CreateDirectConnectGatewayResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.create_direct_connect_gateway_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.create_direct_connect_gateway_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def create_direct_connect_gateway_with_http_info(self, body, **kwargs):  # noqa: E501
        """create_direct_connect_gateway  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_direct_connect_gateway_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param CreateDirectConnectGatewayRequest body: (required)
        :return: CreateDirectConnectGatewayResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method create_direct_connect_gateway" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `create_direct_connect_gateway`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/CreateDirectConnectGateway/2020-04-01/directconnect/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='CreateDirectConnectGatewayResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def create_direct_connect_gateway_route(self, body, **kwargs):  # noqa: E501
        """create_direct_connect_gateway_route  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_direct_connect_gateway_route(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param CreateDirectConnectGatewayRouteRequest body: (required)
        :return: CreateDirectConnectGatewayRouteResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.create_direct_connect_gateway_route_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.create_direct_connect_gateway_route_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def create_direct_connect_gateway_route_with_http_info(self, body, **kwargs):  # noqa: E501
        """create_direct_connect_gateway_route  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_direct_connect_gateway_route_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param CreateDirectConnectGatewayRouteRequest body: (required)
        :return: CreateDirectConnectGatewayRouteResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method create_direct_connect_gateway_route" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `create_direct_connect_gateway_route`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/CreateDirectConnectGatewayRoute/2020-04-01/directconnect/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='CreateDirectConnectGatewayRouteResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def create_direct_connect_virtual_interface(self, body, **kwargs):  # noqa: E501
        """create_direct_connect_virtual_interface  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_direct_connect_virtual_interface(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param CreateDirectConnectVirtualInterfaceRequest body: (required)
        :return: CreateDirectConnectVirtualInterfaceResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.create_direct_connect_virtual_interface_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.create_direct_connect_virtual_interface_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def create_direct_connect_virtual_interface_with_http_info(self, body, **kwargs):  # noqa: E501
        """create_direct_connect_virtual_interface  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.create_direct_connect_virtual_interface_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param CreateDirectConnectVirtualInterfaceRequest body: (required)
        :return: CreateDirectConnectVirtualInterfaceResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method create_direct_connect_virtual_interface" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `create_direct_connect_virtual_interface`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/CreateDirectConnectVirtualInterface/2020-04-01/directconnect/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='CreateDirectConnectVirtualInterfaceResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def delete_bgp_peer(self, body, **kwargs):  # noqa: E501
        """delete_bgp_peer  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_bgp_peer(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DeleteBgpPeerRequest body: (required)
        :return: DeleteBgpPeerResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.delete_bgp_peer_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.delete_bgp_peer_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def delete_bgp_peer_with_http_info(self, body, **kwargs):  # noqa: E501
        """delete_bgp_peer  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_bgp_peer_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DeleteBgpPeerRequest body: (required)
        :return: DeleteBgpPeerResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method delete_bgp_peer" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `delete_bgp_peer`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/DeleteBgpPeer/2020-04-01/directconnect/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='DeleteBgpPeerResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def delete_direct_connect_connection(self, body, **kwargs):  # noqa: E501
        """delete_direct_connect_connection  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_direct_connect_connection(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DeleteDirectConnectConnectionRequest body: (required)
        :return: DeleteDirectConnectConnectionResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.delete_direct_connect_connection_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.delete_direct_connect_connection_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def delete_direct_connect_connection_with_http_info(self, body, **kwargs):  # noqa: E501
        """delete_direct_connect_connection  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_direct_connect_connection_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DeleteDirectConnectConnectionRequest body: (required)
        :return: DeleteDirectConnectConnectionResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method delete_direct_connect_connection" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `delete_direct_connect_connection`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/DeleteDirectConnectConnection/2020-04-01/directconnect/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='DeleteDirectConnectConnectionResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def delete_direct_connect_gateway(self, body, **kwargs):  # noqa: E501
        """delete_direct_connect_gateway  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_direct_connect_gateway(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DeleteDirectConnectGatewayRequest body: (required)
        :return: DeleteDirectConnectGatewayResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.delete_direct_connect_gateway_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.delete_direct_connect_gateway_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def delete_direct_connect_gateway_with_http_info(self, body, **kwargs):  # noqa: E501
        """delete_direct_connect_gateway  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_direct_connect_gateway_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DeleteDirectConnectGatewayRequest body: (required)
        :return: DeleteDirectConnectGatewayResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method delete_direct_connect_gateway" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `delete_direct_connect_gateway`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/DeleteDirectConnectGateway/2020-04-01/directconnect/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='DeleteDirectConnectGatewayResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def delete_direct_connect_gateway_route(self, body, **kwargs):  # noqa: E501
        """delete_direct_connect_gateway_route  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_direct_connect_gateway_route(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DeleteDirectConnectGatewayRouteRequest body: (required)
        :return: DeleteDirectConnectGatewayRouteResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.delete_direct_connect_gateway_route_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.delete_direct_connect_gateway_route_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def delete_direct_connect_gateway_route_with_http_info(self, body, **kwargs):  # noqa: E501
        """delete_direct_connect_gateway_route  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_direct_connect_gateway_route_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DeleteDirectConnectGatewayRouteRequest body: (required)
        :return: DeleteDirectConnectGatewayRouteResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method delete_direct_connect_gateway_route" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `delete_direct_connect_gateway_route`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/DeleteDirectConnectGatewayRoute/2020-04-01/directconnect/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='DeleteDirectConnectGatewayRouteResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def delete_direct_connect_virtual_interface(self, body, **kwargs):  # noqa: E501
        """delete_direct_connect_virtual_interface  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_direct_connect_virtual_interface(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DeleteDirectConnectVirtualInterfaceRequest body: (required)
        :return: DeleteDirectConnectVirtualInterfaceResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.delete_direct_connect_virtual_interface_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.delete_direct_connect_virtual_interface_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def delete_direct_connect_virtual_interface_with_http_info(self, body, **kwargs):  # noqa: E501
        """delete_direct_connect_virtual_interface  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.delete_direct_connect_virtual_interface_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DeleteDirectConnectVirtualInterfaceRequest body: (required)
        :return: DeleteDirectConnectVirtualInterfaceResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method delete_direct_connect_virtual_interface" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `delete_direct_connect_virtual_interface`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/DeleteDirectConnectVirtualInterface/2020-04-01/directconnect/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='DeleteDirectConnectVirtualInterfaceResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def describe_bgp_peer_attributes(self, body, **kwargs):  # noqa: E501
        """describe_bgp_peer_attributes  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.describe_bgp_peer_attributes(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DescribeBgpPeerAttributesRequest body: (required)
        :return: DescribeBgpPeerAttributesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.describe_bgp_peer_attributes_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.describe_bgp_peer_attributes_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def describe_bgp_peer_attributes_with_http_info(self, body, **kwargs):  # noqa: E501
        """describe_bgp_peer_attributes  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.describe_bgp_peer_attributes_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DescribeBgpPeerAttributesRequest body: (required)
        :return: DescribeBgpPeerAttributesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method describe_bgp_peer_attributes" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `describe_bgp_peer_attributes`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/DescribeBgpPeerAttributes/2020-04-01/directconnect/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='DescribeBgpPeerAttributesResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def describe_bgp_peers(self, body, **kwargs):  # noqa: E501
        """describe_bgp_peers  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.describe_bgp_peers(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DescribeBgpPeersRequest body: (required)
        :return: DescribeBgpPeersResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.describe_bgp_peers_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.describe_bgp_peers_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def describe_bgp_peers_with_http_info(self, body, **kwargs):  # noqa: E501
        """describe_bgp_peers  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.describe_bgp_peers_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DescribeBgpPeersRequest body: (required)
        :return: DescribeBgpPeersResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method describe_bgp_peers" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `describe_bgp_peers`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/DescribeBgpPeers/2020-04-01/directconnect/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='DescribeBgpPeersResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def describe_direct_connect_access_points(self, body, **kwargs):  # noqa: E501
        """describe_direct_connect_access_points  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.describe_direct_connect_access_points(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DescribeDirectConnectAccessPointsRequest body: (required)
        :return: DescribeDirectConnectAccessPointsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.describe_direct_connect_access_points_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.describe_direct_connect_access_points_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def describe_direct_connect_access_points_with_http_info(self, body, **kwargs):  # noqa: E501
        """describe_direct_connect_access_points  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.describe_direct_connect_access_points_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DescribeDirectConnectAccessPointsRequest body: (required)
        :return: DescribeDirectConnectAccessPointsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method describe_direct_connect_access_points" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `describe_direct_connect_access_points`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/DescribeDirectConnectAccessPoints/2020-04-01/directconnect/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='DescribeDirectConnectAccessPointsResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def describe_direct_connect_connection_attributes(self, body, **kwargs):  # noqa: E501
        """describe_direct_connect_connection_attributes  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.describe_direct_connect_connection_attributes(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DescribeDirectConnectConnectionAttributesRequest body: (required)
        :return: DescribeDirectConnectConnectionAttributesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.describe_direct_connect_connection_attributes_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.describe_direct_connect_connection_attributes_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def describe_direct_connect_connection_attributes_with_http_info(self, body, **kwargs):  # noqa: E501
        """describe_direct_connect_connection_attributes  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.describe_direct_connect_connection_attributes_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DescribeDirectConnectConnectionAttributesRequest body: (required)
        :return: DescribeDirectConnectConnectionAttributesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method describe_direct_connect_connection_attributes" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `describe_direct_connect_connection_attributes`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/DescribeDirectConnectConnectionAttributes/2020-04-01/directconnect/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='DescribeDirectConnectConnectionAttributesResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def describe_direct_connect_connection_loa_attributes(self, body, **kwargs):  # noqa: E501
        """describe_direct_connect_connection_loa_attributes  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.describe_direct_connect_connection_loa_attributes(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DescribeDirectConnectConnectionLoaAttributesRequest body: (required)
        :return: DescribeDirectConnectConnectionLoaAttributesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.describe_direct_connect_connection_loa_attributes_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.describe_direct_connect_connection_loa_attributes_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def describe_direct_connect_connection_loa_attributes_with_http_info(self, body, **kwargs):  # noqa: E501
        """describe_direct_connect_connection_loa_attributes  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.describe_direct_connect_connection_loa_attributes_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DescribeDirectConnectConnectionLoaAttributesRequest body: (required)
        :return: DescribeDirectConnectConnectionLoaAttributesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method describe_direct_connect_connection_loa_attributes" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `describe_direct_connect_connection_loa_attributes`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/DescribeDirectConnectConnectionLoaAttributes/2020-04-01/directconnect/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='DescribeDirectConnectConnectionLoaAttributesResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def describe_direct_connect_connections(self, body, **kwargs):  # noqa: E501
        """describe_direct_connect_connections  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.describe_direct_connect_connections(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DescribeDirectConnectConnectionsRequest body: (required)
        :return: DescribeDirectConnectConnectionsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.describe_direct_connect_connections_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.describe_direct_connect_connections_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def describe_direct_connect_connections_with_http_info(self, body, **kwargs):  # noqa: E501
        """describe_direct_connect_connections  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.describe_direct_connect_connections_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DescribeDirectConnectConnectionsRequest body: (required)
        :return: DescribeDirectConnectConnectionsResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method describe_direct_connect_connections" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `describe_direct_connect_connections`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/DescribeDirectConnectConnections/2020-04-01/directconnect/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='DescribeDirectConnectConnectionsResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def describe_direct_connect_gateway_attributes(self, body, **kwargs):  # noqa: E501
        """describe_direct_connect_gateway_attributes  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.describe_direct_connect_gateway_attributes(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DescribeDirectConnectGatewayAttributesRequest body: (required)
        :return: DescribeDirectConnectGatewayAttributesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.describe_direct_connect_gateway_attributes_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.describe_direct_connect_gateway_attributes_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def describe_direct_connect_gateway_attributes_with_http_info(self, body, **kwargs):  # noqa: E501
        """describe_direct_connect_gateway_attributes  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.describe_direct_connect_gateway_attributes_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DescribeDirectConnectGatewayAttributesRequest body: (required)
        :return: DescribeDirectConnectGatewayAttributesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method describe_direct_connect_gateway_attributes" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `describe_direct_connect_gateway_attributes`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/DescribeDirectConnectGatewayAttributes/2020-04-01/directconnect/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='DescribeDirectConnectGatewayAttributesResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def describe_direct_connect_gateway_route_attributes(self, body, **kwargs):  # noqa: E501
        """describe_direct_connect_gateway_route_attributes  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.describe_direct_connect_gateway_route_attributes(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DescribeDirectConnectGatewayRouteAttributesRequest body: (required)
        :return: DescribeDirectConnectGatewayRouteAttributesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.describe_direct_connect_gateway_route_attributes_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.describe_direct_connect_gateway_route_attributes_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def describe_direct_connect_gateway_route_attributes_with_http_info(self, body, **kwargs):  # noqa: E501
        """describe_direct_connect_gateway_route_attributes  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.describe_direct_connect_gateway_route_attributes_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DescribeDirectConnectGatewayRouteAttributesRequest body: (required)
        :return: DescribeDirectConnectGatewayRouteAttributesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method describe_direct_connect_gateway_route_attributes" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `describe_direct_connect_gateway_route_attributes`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/DescribeDirectConnectGatewayRouteAttributes/2020-04-01/directconnect/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='DescribeDirectConnectGatewayRouteAttributesResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def describe_direct_connect_gateway_routes(self, body, **kwargs):  # noqa: E501
        """describe_direct_connect_gateway_routes  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.describe_direct_connect_gateway_routes(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DescribeDirectConnectGatewayRoutesRequest body: (required)
        :return: DescribeDirectConnectGatewayRoutesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.describe_direct_connect_gateway_routes_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.describe_direct_connect_gateway_routes_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def describe_direct_connect_gateway_routes_with_http_info(self, body, **kwargs):  # noqa: E501
        """describe_direct_connect_gateway_routes  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.describe_direct_connect_gateway_routes_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DescribeDirectConnectGatewayRoutesRequest body: (required)
        :return: DescribeDirectConnectGatewayRoutesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method describe_direct_connect_gateway_routes" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `describe_direct_connect_gateway_routes`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/DescribeDirectConnectGatewayRoutes/2020-04-01/directconnect/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='DescribeDirectConnectGatewayRoutesResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def describe_direct_connect_gateways(self, body, **kwargs):  # noqa: E501
        """describe_direct_connect_gateways  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.describe_direct_connect_gateways(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DescribeDirectConnectGatewaysRequest body: (required)
        :return: DescribeDirectConnectGatewaysResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.describe_direct_connect_gateways_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.describe_direct_connect_gateways_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def describe_direct_connect_gateways_with_http_info(self, body, **kwargs):  # noqa: E501
        """describe_direct_connect_gateways  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.describe_direct_connect_gateways_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DescribeDirectConnectGatewaysRequest body: (required)
        :return: DescribeDirectConnectGatewaysResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method describe_direct_connect_gateways" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `describe_direct_connect_gateways`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/DescribeDirectConnectGateways/2020-04-01/directconnect/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='DescribeDirectConnectGatewaysResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def describe_direct_connect_virtual_interface_attributes(self, body, **kwargs):  # noqa: E501
        """describe_direct_connect_virtual_interface_attributes  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.describe_direct_connect_virtual_interface_attributes(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DescribeDirectConnectVirtualInterfaceAttributesRequest body: (required)
        :return: DescribeDirectConnectVirtualInterfaceAttributesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.describe_direct_connect_virtual_interface_attributes_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.describe_direct_connect_virtual_interface_attributes_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def describe_direct_connect_virtual_interface_attributes_with_http_info(self, body, **kwargs):  # noqa: E501
        """describe_direct_connect_virtual_interface_attributes  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.describe_direct_connect_virtual_interface_attributes_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DescribeDirectConnectVirtualInterfaceAttributesRequest body: (required)
        :return: DescribeDirectConnectVirtualInterfaceAttributesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method describe_direct_connect_virtual_interface_attributes" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `describe_direct_connect_virtual_interface_attributes`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/DescribeDirectConnectVirtualInterfaceAttributes/2020-04-01/directconnect/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='DescribeDirectConnectVirtualInterfaceAttributesResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def describe_direct_connect_virtual_interfaces(self, body, **kwargs):  # noqa: E501
        """describe_direct_connect_virtual_interfaces  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.describe_direct_connect_virtual_interfaces(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DescribeDirectConnectVirtualInterfacesRequest body: (required)
        :return: DescribeDirectConnectVirtualInterfacesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.describe_direct_connect_virtual_interfaces_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.describe_direct_connect_virtual_interfaces_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def describe_direct_connect_virtual_interfaces_with_http_info(self, body, **kwargs):  # noqa: E501
        """describe_direct_connect_virtual_interfaces  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.describe_direct_connect_virtual_interfaces_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param DescribeDirectConnectVirtualInterfacesRequest body: (required)
        :return: DescribeDirectConnectVirtualInterfacesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method describe_direct_connect_virtual_interfaces" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `describe_direct_connect_virtual_interfaces`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/DescribeDirectConnectVirtualInterfaces/2020-04-01/directconnect/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='DescribeDirectConnectVirtualInterfacesResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def modify_bgp_peer_attributes(self, body, **kwargs):  # noqa: E501
        """modify_bgp_peer_attributes  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.modify_bgp_peer_attributes(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param ModifyBgpPeerAttributesRequest body: (required)
        :return: ModifyBgpPeerAttributesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.modify_bgp_peer_attributes_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.modify_bgp_peer_attributes_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def modify_bgp_peer_attributes_with_http_info(self, body, **kwargs):  # noqa: E501
        """modify_bgp_peer_attributes  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.modify_bgp_peer_attributes_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param ModifyBgpPeerAttributesRequest body: (required)
        :return: ModifyBgpPeerAttributesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method modify_bgp_peer_attributes" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `modify_bgp_peer_attributes`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/ModifyBgpPeerAttributes/2020-04-01/directconnect/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='ModifyBgpPeerAttributesResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def modify_direct_connect_connection_attributes(self, body, **kwargs):  # noqa: E501
        """modify_direct_connect_connection_attributes  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.modify_direct_connect_connection_attributes(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param ModifyDirectConnectConnectionAttributesRequest body: (required)
        :return: ModifyDirectConnectConnectionAttributesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.modify_direct_connect_connection_attributes_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.modify_direct_connect_connection_attributes_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def modify_direct_connect_connection_attributes_with_http_info(self, body, **kwargs):  # noqa: E501
        """modify_direct_connect_connection_attributes  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.modify_direct_connect_connection_attributes_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param ModifyDirectConnectConnectionAttributesRequest body: (required)
        :return: ModifyDirectConnectConnectionAttributesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method modify_direct_connect_connection_attributes" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `modify_direct_connect_connection_attributes`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/ModifyDirectConnectConnectionAttributes/2020-04-01/directconnect/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='ModifyDirectConnectConnectionAttributesResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def modify_direct_connect_connection_loa_attributes(self, body, **kwargs):  # noqa: E501
        """modify_direct_connect_connection_loa_attributes  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.modify_direct_connect_connection_loa_attributes(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param ModifyDirectConnectConnectionLoaAttributesRequest body: (required)
        :return: ModifyDirectConnectConnectionLoaAttributesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.modify_direct_connect_connection_loa_attributes_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.modify_direct_connect_connection_loa_attributes_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def modify_direct_connect_connection_loa_attributes_with_http_info(self, body, **kwargs):  # noqa: E501
        """modify_direct_connect_connection_loa_attributes  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.modify_direct_connect_connection_loa_attributes_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param ModifyDirectConnectConnectionLoaAttributesRequest body: (required)
        :return: ModifyDirectConnectConnectionLoaAttributesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method modify_direct_connect_connection_loa_attributes" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `modify_direct_connect_connection_loa_attributes`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/ModifyDirectConnectConnectionLoaAttributes/2020-04-01/directconnect/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='ModifyDirectConnectConnectionLoaAttributesResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def modify_direct_connect_gateway_attributes(self, body, **kwargs):  # noqa: E501
        """modify_direct_connect_gateway_attributes  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.modify_direct_connect_gateway_attributes(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param ModifyDirectConnectGatewayAttributesRequest body: (required)
        :return: ModifyDirectConnectGatewayAttributesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.modify_direct_connect_gateway_attributes_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.modify_direct_connect_gateway_attributes_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def modify_direct_connect_gateway_attributes_with_http_info(self, body, **kwargs):  # noqa: E501
        """modify_direct_connect_gateway_attributes  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.modify_direct_connect_gateway_attributes_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param ModifyDirectConnectGatewayAttributesRequest body: (required)
        :return: ModifyDirectConnectGatewayAttributesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method modify_direct_connect_gateway_attributes" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `modify_direct_connect_gateway_attributes`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/ModifyDirectConnectGatewayAttributes/2020-04-01/directconnect/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='ModifyDirectConnectGatewayAttributesResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)

    def modify_direct_connect_virtual_interface_attributes(self, body, **kwargs):  # noqa: E501
        """modify_direct_connect_virtual_interface_attributes  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.modify_direct_connect_virtual_interface_attributes(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param ModifyDirectConnectVirtualInterfaceAttributesRequest body: (required)
        :return: ModifyDirectConnectVirtualInterfaceAttributesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """
        kwargs['_return_http_data_only'] = True
        if kwargs.get('async_req'):
            return self.modify_direct_connect_virtual_interface_attributes_with_http_info(body, **kwargs)  # noqa: E501
        else:
            (data) = self.modify_direct_connect_virtual_interface_attributes_with_http_info(body, **kwargs)  # noqa: E501
            return data

    def modify_direct_connect_virtual_interface_attributes_with_http_info(self, body, **kwargs):  # noqa: E501
        """modify_direct_connect_virtual_interface_attributes  # noqa: E501

        This method makes a synchronous HTTP request by default. To make an
        asynchronous HTTP request, please pass async_req=True
        >>> thread = api.modify_direct_connect_virtual_interface_attributes_with_http_info(body, async_req=True)
        >>> result = thread.get()

        :param async_req bool
        :param ModifyDirectConnectVirtualInterfaceAttributesRequest body: (required)
        :return: ModifyDirectConnectVirtualInterfaceAttributesResponse
                 If the method is called asynchronously,
                 returns the request thread.
        """

        all_params = ['body']  # noqa: E501
        all_params.append('async_req')
        all_params.append('_return_http_data_only')
        all_params.append('_preload_content')
        all_params.append('_request_timeout')

        params = locals()
        for key, val in six.iteritems(params['kwargs']):
            if key not in all_params:
                raise TypeError(
                    "Got an unexpected keyword argument '%s'"
                    " to method modify_direct_connect_virtual_interface_attributes" % key
                )
            params[key] = val
        del params['kwargs']
        # verify the required parameter 'body' is set
        if self.api_client.client_side_validation and ('body' not in params or
                                                       params['body'] is None):  # noqa: E501
            raise ValueError("Missing the required parameter `body` when calling `modify_direct_connect_virtual_interface_attributes`")  # noqa: E501

        collection_formats = {}

        path_params = {}

        query_params = []

        header_params = {}

        form_params = []
        local_var_files = {}

        body_params = None
        if 'body' in params:
            body_params = params['body']
        # HTTP header `Accept`
        header_params['Accept'] = self.api_client.select_header_accept(
            ['application/json'])  # noqa: E501

        # HTTP header `Content-Type`
        header_params['Content-Type'] = self.api_client.select_header_content_type(  # noqa: E501
            ['text/plain'])  # noqa: E501

        # Authentication setting
        auth_settings = ['volcengineSign']  # noqa: E501

        return self.api_client.call_api(
            '/ModifyDirectConnectVirtualInterfaceAttributes/2020-04-01/directconnect/get/text_plain/', 'GET',
            path_params,
            query_params,
            header_params,
            body=body_params,
            post_params=form_params,
            files=local_var_files,
            response_type='ModifyDirectConnectVirtualInterfaceAttributesResponse',  # noqa: E501
            auth_settings=auth_settings,
            async_req=params.get('async_req'),
            _return_http_data_only=params.get('_return_http_data_only'),
            _preload_content=params.get('_preload_content', True),
            _request_timeout=params.get('_request_timeout'),
            collection_formats=collection_formats)
