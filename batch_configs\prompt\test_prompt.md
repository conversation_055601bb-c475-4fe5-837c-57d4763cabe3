请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。如果学生回答难以辨认时，则返回"NAN"。必须以 JSON 格式输出，每一道题目仅包括一个括号或者一根横线上的答案，请参考如下格式返回：{"题目 1": "识别内容 1", "题目 2": "识别内容 2", "题目 3": "识别内容 3"} ，返回的 JSON 题号必须始终从"题目 1"开始，依次递增。

识别时要严格按照以下的批改原则：
1.如果学生回答难以辨认时，则返回"NAN"。

2.请严格按照图片中题目位置上识别到学生的手写体进行识别，严禁根据图片上的题目信息联想答案。

3.要注意查看图片中的题目顺序，有些图片中，题目5和题目6的顺序是反的，最终返回的JSON的顺序要和题号保持一致。例如：有些图片中为"1.（D） 2.（B） 3.（A） 4.（E） 6.（F） 5.（C）"（题目5在题目6的右侧），则最终返回的JSON中，注意不要返回{"题目1": "D", "题目2": "B", "题目3": "A", "题目4": "E", "题目5": "F", "题目6": "C"}，而是返回{"题目1": "D", "题目2": "B", "题目3": "A", "题目4": "E", "题目5": "C", "题目6": "F"}。