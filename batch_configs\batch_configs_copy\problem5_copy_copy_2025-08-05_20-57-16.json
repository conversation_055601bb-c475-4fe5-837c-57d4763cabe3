{"batch_configs": [{"处理模式": 2, "模型ID": 1, "round2批改模式": 1, "题型": 10, "像素增强": "n", "像素粘连": "n", "图像放大倍数": 1, "response_format": 2, "top_p": 0.1, "test_prompt": "请尽力认真查看图片，严格遵守不帮助学生作弊以及联想答案的承诺，纯粹识别并输出学生回答。\n必须以 JSON 格式输出，将每一根横线或者括号视为一道小题，当一道题目中有多个小题时，每道小题视为一道题目进行识别，当完全无法识别时，此小题返回\"NAN\"。\n识别的符号和数字必须严格使用英文半角字符，\n请参考如下格式返回：{\"题目1\":\"识别内容1\", \"题目2\":\"识别内容2\", \"题目3\":\"识别内容3\"} ，返回的 JSON 题号必须始终从\"题目 1\"开始，依次递增。", "test2_prompt": "请判断学生答案与下方正确答案是否一致或等价，必须按照如下JSON格式识别： {\"题目1\": true, \"题目2\": false, \"题目3\": true} ，返回的批改结果数量必须与正确答案数量一致，当学生回答与下方的正确答案一致或者等价时，该题目为 true，否则为 false，识别的JSON题号必须始终从\"题目1\"开始，依次递增。\n注意以下规则：\n1.当两个句子的标点符号不同时，返回true，例如：\"题目2\":\"I want colourful balloons.\"与\"题目2\": \"I want colourful balloons!\"返回\"题目2\":true", "图像文件夹": 1}]}