{"batch_configs": [{"处理模式": 1, "模型ID": 1, "题型": 13, "图像文件夹": 1, "像素增强": "n", "像素粘连": "n", "图像放大倍数": 1, "response_format": 2, "灰度阀门": 150, "temperature": 0.8, "one_stage_test_prompt": "one_stage_prompt.md"}, {"处理模式": 2, "round2批改模式": 2, "模型ID": 1, "题型": 13, "图像文件夹": 1, "像素增强": "n", "像素粘连": "n", "图像放大倍数": 1, "top_p": 0.9, "test_prompt": "prompt.md", "test2_prompt": "round2_prompt.md"}, {"处理模式": 3, "模型ID": 1, "题型": 13, "图像文件夹": 1, "像素增强": "n", "像素粘连": "n", "图像放大倍数": 1, "test_prompt": "prompt.md", "max_tokens": 8192, "test3_prompt": "round2_prompt_new.md"}, {"处理模式": 1, "模型ID": 1, "题型": 13, "图像文件夹": 1, "像素增强": "n", "像素粘连": "n", "图像放大倍数": 1, "one_stage_test_prompt": "你是一位严谨负责的资深阅卷老师，负责批改普通选择题。你的任务是结合学生答题图片，按照给定规则逐题识别并输出答案。\n以下是学生答题图片：\n<student_answer_image>\n{{STUDENT_ANSWER_IMAGE}}\n</student_answer_image>\n以下是正确答案：\n<answer>\n{{answer_json}}\n</answer>\n### 识别规则\n#### 选择题（选项为A、B、C、D、E、F、G）\n- **定位答题区域**：根据题号找到对应的答题位置。\n- **答案判断**：\n    - 仔细观察答题位置所写的答案内容，若内容为大写字母（A、B、C、D、E、F、G），则以该字母为答案。\n    - 若书写内容不是大写字母（A、B、C、D、E、F、G），视为错误，记为\"false\"。\n    - 若答题位置无书写内容，记录为\"false\"。\n    - 将识别到的学生答案和正确答案对比，若一致则输出\"true\"、若不一致则输出\"false\"\n### 输出格式\n必须以JSON格式输出，键为\"题目1\"\"题目2\"……（按题号顺序编号，不管图中题号从几开始，必须始终从\"题目1\"开始，依次递增），值为对应答案。\n若整图无有效题目或无法识别，输出{\"题目1\": \"未识别到有效答题内容\"}。\n示例（选择题）：\n图片含3道题，答案依次为B、C、D，正确答案为：\n{\"题目1\": \"B\", \"题目2\": \"A\", \"题目3\": \"D\"}\n则输出：\n{\"题目1\": \"true\", \"题目2\": \"false\", \"题目3\": \"true\"}"}]}