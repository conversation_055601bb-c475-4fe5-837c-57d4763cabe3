# Example Code generated by Beijing Volcanoengine Technology.
from __future__ import print_function
import volcenginesdkcore
import volcenginesdkdirectconnect
from pprint import pprint
from volcenginesdkcore.rest import ApiException

if __name__ == '__main__':
    configuration = volcenginesdkcore.Configuration()
    configuration.ak = "Your AK"
    configuration.sk = "Your SK"
    configuration.region = "cn-beijing"
    # set default configuration
    volcenginesdkcore.Configuration.set_default(configuration)

    # use global default configuration
    api_instance = volcenginesdkdirectconnect.DIRECTCONNECTApi()
    create_direct_connect_virtual_interface_request = volcenginesdkdirectconnect.CreateDirectConnectVirtualInterfaceRequest(
        description="test",
        direct_connect_connection_id="dcc-2fe3zsmkshs59g****",
        direct_connect_gateway_id="dcg-7qthudw0ll6jmc****",
        local_ip="192.XX.XX.10/24",
        peer_ip="192.XX.XX.20/24",
        virtual_interface_name="test",
        vlan_id=2000,
    )
    
    try:
        resp = api_instance.create_direct_connect_virtual_interface(create_direct_connect_virtual_interface_request)
        pprint(resp)
    except ApiException as e:
        print("Exception when calling api: %s\n" % e)
