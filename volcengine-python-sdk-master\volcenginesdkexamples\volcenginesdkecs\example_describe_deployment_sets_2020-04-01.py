# Example Code generated by Beijing Volcanoengine Technology.
from __future__ import print_function
import volcenginesdkcore
import volcenginesdkecs
from pprint import pprint
from volcenginesdkcore.rest import ApiException

if __name__ == '__main__':
    configuration = volcenginesdkcore.Configuration()
    configuration.ak = "Your AK"
    configuration.sk = "Your SK"
    configuration.region = "cn-beijing"
    # set default configuration
    volcenginesdkcore.Configuration.set_default(configuration)

    # use global default configuration
    api_instance = volcenginesdkecs.ECSApi()
    describe_deployment_sets_request = volcenginesdkecs.DescribeDeploymentSetsRequest(
        deployment_set_ids=["dps-yc1o9aahks5m57nk****"],
    )
    
    try:
        resp = api_instance.describe_deployment_sets(describe_deployment_sets_request)
        pprint(resp)
    except ApiException as e:
        print("Exception when calling api: %s\n" % e)
