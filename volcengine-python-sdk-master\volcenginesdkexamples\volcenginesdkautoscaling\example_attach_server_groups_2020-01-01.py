# Example Code generated by Beijing Volcanoengine Technology.
from __future__ import print_function
import volcenginesdkcore
import volcenginesdkautoscaling
from pprint import pprint
from volcenginesdkcore.rest import ApiException

if __name__ == '__main__':
    configuration = volcenginesdkcore.Configuration()
    configuration.ak = "Your AK"
    configuration.sk = "Your SK"
    configuration.region = "cn-beijing"
    # set default configuration
    volcenginesdkcore.Configuration.set_default(configuration)

    # use global default configuration
    api_instance = volcenginesdkautoscaling.AUTOSCALINGApi()
    req_server_group_attributes = volcenginesdkautoscaling.ServerGroupAttributeForAttachServerGroupsInput(
        port=12,
        server_group_id="rsp-12b2z5fnxvhts17q7y2fj****",
        weight=20,
    )
    attach_server_groups_request = volcenginesdkautoscaling.AttachServerGroupsRequest(
        scaling_group_id="",
        server_group_attributes=[req_server_group_attributes],
    )
    
    try:
        resp = api_instance.attach_server_groups(attach_server_groups_request)
        pprint(resp)
    except ApiException as e:
        print("Exception when calling api: %s\n" % e)
