# Example Code generated by Beijing Volcanoengine Technology.
from __future__ import print_function
import volcenginesdkcore
import volcenginesdkvpc
from pprint import pprint
from volcenginesdkcore.rest import ApiException

if __name__ == '__main__':
    configuration = volcenginesdkcore.Configuration()
    configuration.ak = "Your AK"
    configuration.sk = "Your SK"
    configuration.region = "cn-beijing"
    # set default configuration
    volcenginesdkcore.Configuration.set_default(configuration)

    # use global default configuration
    api_instance = volcenginesdkvpc.VPCApi()
    disassociate_ha_vip_request = volcenginesdkvpc.DisassociateHaVipRequest(
        ha_vip_id="havip-2zeo05qre24nhrqpy****",
        instance_id="i-faf344422ffsfad****",
        instance_type="EcsInstance",
    )
    
    try:
        resp = api_instance.disassociate_ha_vip(disassociate_ha_vip_request)
        pprint(resp)
    except ApiException as e:
        print("Exception when calling api: %s\n" % e)
