# Example Code generated by Beijing Volcanoengine Technology.
from __future__ import print_function
import volcenginesdkcore
import volcenginesdkvpn
from pprint import pprint
from volcenginesdkcore.rest import ApiException

if __name__ == '__main__':
    configuration = volcenginesdkcore.Configuration()
    configuration.ak = "AK"
    configuration.sk = "SK"
    configuration.region = "cn-beijing"
    # set default configuration
    volcenginesdkcore.Configuration.set_default(configuration)

    # use global default configuration
    api_instance = volcenginesdkvpn.VPNApi()
    describe_customer_gateways_request = volcenginesdkvpn.DescribeCustomerGatewaysRequest(
        customer_gateway_ids=["cgw-7qthudw0ll6jmc****"],
        page_number=1,
        page_size=20,
    )

    try:
        resp = api_instance.describe_customer_gateways(describe_customer_gateways_request)
        pprint(resp)
    except ApiException as e:
        print("Exception when calling api: %s\n" % e)
